"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[6619],{

/***/ 16918:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $n: () => (/* reexport safe */ antd_es_button__WEBPACK_IMPORTED_MODULE_0__.Ay),
/* harmony export */   B8: () => (/* reexport safe */ antd_es_list__WEBPACK_IMPORTED_MODULE_8__.A),
/* harmony export */   Fc: () => (/* reexport safe */ antd_es_alert__WEBPACK_IMPORTED_MODULE_19__.A),
/* harmony export */   PE: () => (/* reexport safe */ antd_es_layout__WEBPACK_IMPORTED_MODULE_3__.A),
/* harmony export */   _s: () => (/* reexport safe */ antd_es_drawer__WEBPACK_IMPORTED_MODULE_15__.A),
/* harmony export */   cG: () => (/* reexport safe */ antd_es_divider__WEBPACK_IMPORTED_MODULE_18__.A),
/* harmony export */   ff: () => (/* reexport safe */ antd_es_float_button__WEBPACK_IMPORTED_MODULE_57__.A),
/* harmony export */   jL: () => (/* reexport safe */ antd_es_statistic__WEBPACK_IMPORTED_MODULE_7__.A),
/* harmony export */   m_: () => (/* reexport safe */ antd_es_tooltip__WEBPACK_IMPORTED_MODULE_22__.A),
/* harmony export */   ms: () => (/* reexport safe */ antd_es_dropdown__WEBPACK_IMPORTED_MODULE_16__.A),
/* harmony export */   vw: () => (/* reexport safe */ antd_es_tag__WEBPACK_IMPORTED_MODULE_24__.A)
/* harmony export */ });
/* unused harmony exports importComponentCSS, CommonComponents, PageImports, OptimizationTips */
/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(49103);
/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(677);
/* harmony import */ var antd_es_typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(75475);
/* harmony import */ var antd_es_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(45448);
/* harmony import */ var antd_es_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(87206);
/* harmony import */ var antd_es_grid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(36768);
/* harmony import */ var antd_es_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(28392);
/* harmony import */ var antd_es_statistic__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(37122);
/* harmony import */ var antd_es_list__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(42652);
/* harmony import */ var antd_es_badge__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(52120);
/* harmony import */ var antd_es_checkbox__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(91196);
/* harmony import */ var antd_es_radio__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(50770);
/* harmony import */ var antd_es_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(42729);
/* harmony import */ var antd_es_pagination__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(44485);
/* harmony import */ var antd_es_empty__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(17308);
/* harmony import */ var antd_es_drawer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(1849);
/* harmony import */ var antd_es_dropdown__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(88603);
/* harmony import */ var antd_es_avatar__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(81427);
/* harmony import */ var antd_es_divider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(36552);
/* harmony import */ var antd_es_alert__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(27197);
/* harmony import */ var antd_es_spin__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(29029);
/* harmony import */ var antd_es_modal__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(49222);
/* harmony import */ var antd_es_tooltip__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(37977);
/* harmony import */ var antd_es_popover__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(28073);
/* harmony import */ var antd_es_tag__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(67034);
/* harmony import */ var antd_es_tabs__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(12075);
/* harmony import */ var antd_es_collapse__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(39356);
/* harmony import */ var antd_es_breadcrumb__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(94431);
/* harmony import */ var antd_es_steps__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(53515);
/* harmony import */ var antd_es_progress__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(6754);
/* harmony import */ var antd_es_result__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(14378);
/* harmony import */ var antd_es_skeleton__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(97072);
/* harmony import */ var antd_es_back_top__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(53308);
/* harmony import */ var antd_es_affix__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(18719);
/* harmony import */ var antd_es_anchor__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(8787);
/* harmony import */ var antd_es_form__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(28792);
/* harmony import */ var antd_es_input__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(17355);
/* harmony import */ var antd_es_select__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(36492);
/* harmony import */ var antd_es_switch__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(15039);
/* harmony import */ var antd_es_slider__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(6531);
/* harmony import */ var antd_es_rate__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(1285);
/* harmony import */ var antd_es_upload__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(77829);
/* harmony import */ var antd_es_date_picker__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(95082);
/* harmony import */ var antd_es_time_picker__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(15622);
/* harmony import */ var antd_es_transfer__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(80296);
/* harmony import */ var antd_es_cascader__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(93438);
/* harmony import */ var antd_es_tree_select__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(21102);
/* harmony import */ var antd_es_auto_complete__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(1062);
/* harmony import */ var antd_es_tree__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(87937);
/* harmony import */ var antd_es_calendar__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(40248);
/* harmony import */ var antd_es_image__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(62070);
/* harmony import */ var antd_es_carousel__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(88845);
/* harmony import */ var antd_es_descriptions__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(55957);
/* harmony import */ var antd_es_timeline__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(33835);
/* harmony import */ var antd_es_message__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(87959);
/* harmony import */ var antd_es_notification__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(76511);
/* harmony import */ var antd_es_popconfirm__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(16044);
/* harmony import */ var antd_es_float_button__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(75245);
/* harmony import */ var antd_es_config_provider__WEBPACK_IMPORTED_MODULE_58__ = __webpack_require__(38674);
/* harmony import */ var antd_dist_reset_css__WEBPACK_IMPORTED_MODULE_59__ = __webpack_require__(58545);
/**
 * Optimized Ant Design Imports Utility
 * 
 * This utility provides optimized imports for Ant Design components
 * to enable better tree-shaking and reduce bundle sizes.
 */

// Core Components - Import from specific paths for better tree-shaking




















































// Form Components



















// Data Display







// Feedback




// Navigation


// Other


// Import CSS for commonly used components
// Note: Using the reset CSS file for basic styling
// Component-specific styles are handled by the components themselves


/**
 * Utility function for component CSS (deprecated)
 * CSS is now imported globally via antd.css
 */
var importComponentCSS = function importComponentCSS(componentName) {
  // CSS is now imported globally, this function is kept for compatibility
  console.log("CSS for ".concat(componentName, " is already included globally"));
};

/**
 * Common component combinations for convenience
 */
var CommonComponents = {
  // Layout components
  Layout: {
    Layout: antd_es_layout__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A,
    Header: antd_es_layout__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A.Header,
    Content: antd_es_layout__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A.Content,
    Footer: antd_es_layout__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A.Footer,
    Sider: antd_es_layout__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A.Sider
  },
  // Typography components
  Typography: {
    Typography: antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A,
    Title: antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.Title,
    Text: antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.Text,
    Paragraph: antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.Paragraph,
    Link: antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.Link
  },
  // Grid components
  Grid: {
    Row: antd_es_grid__WEBPACK_IMPORTED_MODULE_5__/* .Row */ .fI,
    Col: antd_es_grid__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv
  },
  // Form components
  FormComponents: {
    Form: antd_es_form__WEBPACK_IMPORTED_MODULE_35__/* ["default"] */ .A,
    FormItem: antd_es_form__WEBPACK_IMPORTED_MODULE_35__/* ["default"] */ .A.Item,
    FormList: antd_es_form__WEBPACK_IMPORTED_MODULE_35__/* ["default"] */ .A.List,
    Input: antd_es_input__WEBPACK_IMPORTED_MODULE_36__/* ["default"] */ .A,
    TextArea: antd_es_input__WEBPACK_IMPORTED_MODULE_36__/* ["default"] */ .A.TextArea,
    Password: antd_es_input__WEBPACK_IMPORTED_MODULE_36__/* ["default"] */ .A.Password,
    Search: antd_es_input__WEBPACK_IMPORTED_MODULE_36__/* ["default"] */ .A.Search,
    Select: antd_es_select__WEBPACK_IMPORTED_MODULE_37__/* ["default"] */ .A,
    Option: antd_es_select__WEBPACK_IMPORTED_MODULE_37__/* ["default"] */ .A.Option,
    OptGroup: antd_es_select__WEBPACK_IMPORTED_MODULE_37__/* ["default"] */ .A.OptGroup
  }
};

/**
 * Optimized imports for specific page types
 */
var PageImports = {
  // For dashboard/home pages
  dashboard: function dashboard() {
    return {
      Card: antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A,
      Button: antd_es_button__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Ay,
      Typography: antd_es_typography__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A,
      Row: antd_es_grid__WEBPACK_IMPORTED_MODULE_5__/* .Row */ .fI,
      Col: antd_es_grid__WEBPACK_IMPORTED_MODULE_5__/* .Col */ .fv,
      Statistic: antd_es_statistic__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A,
      List: antd_es_list__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A,
      Badge: antd_es_badge__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A,
      Space: antd_es_space__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A
    };
  },
  // For form pages
  form: function form() {
    return {
      Form: antd_es_form__WEBPACK_IMPORTED_MODULE_35__/* ["default"] */ .A,
      Input: antd_es_input__WEBPACK_IMPORTED_MODULE_36__/* ["default"] */ .A,
      Select: antd_es_select__WEBPACK_IMPORTED_MODULE_37__/* ["default"] */ .A,
      Checkbox: antd_es_checkbox__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .A,
      Radio: antd_es_radio__WEBPACK_IMPORTED_MODULE_11__/* ["default"] */ .Ay,
      Button: antd_es_button__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Ay,
      Card: antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A,
      Space: antd_es_space__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A
    };
  },
  // For data display pages
  dataDisplay: function dataDisplay() {
    return {
      Table: antd_es_table__WEBPACK_IMPORTED_MODULE_12__/* ["default"] */ .A,
      Pagination: antd_es_pagination__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A,
      Empty: antd_es_empty__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .A,
      Card: antd_es_card__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A,
      Button: antd_es_button__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Ay,
      Space: antd_es_space__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A
    };
  },
  // For layout pages
  layout: function layout() {
    return {
      Layout: antd_es_layout__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A,
      Menu: antd_es_menu__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A,
      Drawer: antd_es_drawer__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .A,
      Dropdown: antd_es_dropdown__WEBPACK_IMPORTED_MODULE_16__/* ["default"] */ .A,
      Button: antd_es_button__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .Ay,
      Avatar: antd_es_avatar__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .A
    };
  }
};

/**
 * Bundle size optimization tips
 */
var OptimizationTips = {
  // Use specific imports instead of full library
  GOOD: "import { Button } from 'antd/es/button';",
  BAD: "import { Button } from 'antd';",
  // Import CSS only for used components
  CSS_GOOD: "import 'antd/es/button/style/css';",
  CSS_BAD: "import 'antd/dist/antd.css';",
  // Use this utility for consistent imports
  UTILITY: "import { Button, Card } from '../utils/optimizedAntdImports';"
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  CommonComponents: CommonComponents,
  PageImports: PageImports,
  OptimizationTips: OptimizationTips,
  importComponentCSS: importComponentCSS
});

/***/ }),

/***/ 18768:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   iz: () => (/* binding */ enhancedContrastUtils)
/* harmony export */ });
/* unused harmony exports ariaUtils, keyboardUtils, focusUtils, contrastUtils, accessibleDragDrop, screenReaderUtils, highContrastUtils, reducedMotionUtils, a11yTestUtils, a11yMonitorUtils */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(79146);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for WCAG 2.1 AA compliance
 * including keyboard navigation, screen reader support, focus management,
 * and accessible drag-and-drop interactions.
 */



/**
 * ARIA utilities for screen reader support
 */
var ariaUtils = {
  /**
   * Generate unique IDs for ARIA relationships
   */
  generateId: function generateId() {
    var prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'aria';
    return "".concat(prefix, "-").concat(Math.random().toString(36).substr(2, 9));
  },
  /**
   * Create ARIA label for components
   */
  createLabel: function createLabel(component) {
    var _component$props, _component$props2, _component$props3;
    var action = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'interact with';
    var type = component.type || 'component';
    var label = ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.label) || ((_component$props2 = component.props) === null || _component$props2 === void 0 ? void 0 : _component$props2.title) || ((_component$props3 = component.props) === null || _component$props3 === void 0 ? void 0 : _component$props3.text) || type;
    return "".concat(action, " ").concat(label, " ").concat(type);
  },
  /**
   * Create ARIA description for components
   */
  createDescription: function createDescription(component) {
    var _component$props4, _component$props5, _component$props6, _component$props7;
    var descriptions = [];
    if ((_component$props4 = component.props) !== null && _component$props4 !== void 0 && _component$props4.description) {
      descriptions.push(component.props.description);
    }
    if ((_component$props5 = component.props) !== null && _component$props5 !== void 0 && _component$props5.placeholder) {
      descriptions.push("Placeholder: ".concat(component.props.placeholder));
    }
    if ((_component$props6 = component.props) !== null && _component$props6 !== void 0 && _component$props6.required) {
      descriptions.push('Required field');
    }
    if ((_component$props7 = component.props) !== null && _component$props7 !== void 0 && _component$props7.disabled) {
      descriptions.push('Disabled');
    }
    return descriptions.join('. ');
  },
  /**
   * Create live region announcements
   */
  announce: function announce(message) {
    var priority = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'polite';
    var announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);

    // Remove after announcement
    setTimeout(function () {
      document.body.removeChild(announcement);
    }, 1000);
  },
  /**
   * Create ARIA attributes for drag and drop
   */
  createDragDropAttributes: function createDragDropAttributes(component) {
    var isDragging = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var isDropTarget = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    var attributes = {
      'aria-grabbed': isDragging,
      'aria-describedby': "".concat(component.id, "-drag-instructions")
    };
    if (isDropTarget) {
      attributes['aria-dropeffect'] = 'move';
    }
    return attributes;
  },
  /**
   * Create skip links for keyboard navigation
   */
  createSkipLink: function createSkipLink(targetId, text) {
    return {
      href: "#".concat(targetId),
      className: 'skip-link',
      'aria-label': text,
      onKeyDown: function onKeyDown(e) {
        if (e.key === 'Enter') {
          var _document$getElementB;
          e.preventDefault();
          (_document$getElementB = document.getElementById(targetId)) === null || _document$getElementB === void 0 || _document$getElementB.focus();
        }
      }
    };
  }
};

/**
 * Keyboard navigation utilities
 */
var keyboardUtils = {
  /**
   * Standard key codes for accessibility
   */
  KEYS: {
    ENTER: 'Enter',
    SPACE: ' ',
    ESCAPE: 'Escape',
    TAB: 'Tab',
    ARROW_UP: 'ArrowUp',
    ARROW_DOWN: 'ArrowDown',
    ARROW_LEFT: 'ArrowLeft',
    ARROW_RIGHT: 'ArrowRight',
    HOME: 'Home',
    END: 'End',
    PAGE_UP: 'PageUp',
    PAGE_DOWN: 'PageDown',
    DELETE: 'Delete',
    BACKSPACE: 'Backspace'
  },
  /**
   * Handle keyboard activation (Enter/Space)
   */
  handleActivation: function handleActivation(callback) {
    return function (e) {
      if (e.key === keyboardUtils.KEYS.ENTER || e.key === keyboardUtils.KEYS.SPACE) {
        e.preventDefault();
        callback(e);
      }
    };
  },
  /**
   * Handle arrow key navigation in lists/grids
   */
  handleArrowNavigation: function handleArrowNavigation(elements, currentIndex) {
    var orientation = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'vertical';
    return function (e) {
      var newIndex = currentIndex;
      switch (e.key) {
        case keyboardUtils.KEYS.ARROW_UP:
          if (orientation === 'vertical') {
            newIndex = Math.max(0, currentIndex - 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.ARROW_DOWN:
          if (orientation === 'vertical') {
            newIndex = Math.min(elements.length - 1, currentIndex + 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.ARROW_LEFT:
          if (orientation === 'horizontal') {
            newIndex = Math.max(0, currentIndex - 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.ARROW_RIGHT:
          if (orientation === 'horizontal') {
            newIndex = Math.min(elements.length - 1, currentIndex + 1);
            e.preventDefault();
          }
          break;
        case keyboardUtils.KEYS.HOME:
          newIndex = 0;
          e.preventDefault();
          break;
        case keyboardUtils.KEYS.END:
          newIndex = elements.length - 1;
          e.preventDefault();
          break;
      }
      if (newIndex !== currentIndex && elements[newIndex]) {
        elements[newIndex].focus();
        return newIndex;
      }
      return currentIndex;
    };
  },
  /**
   * Create roving tabindex for component groups
   */
  createRovingTabindex: function createRovingTabindex(elements) {
    var activeIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    return elements.map(function (element, index) {
      return _objectSpread(_objectSpread({}, element), {}, {
        tabIndex: index === activeIndex ? 0 : -1,
        'aria-selected': index === activeIndex
      });
    });
  },
  /**
   * Handle escape key to close modals/dropdowns
   */
  handleEscape: function handleEscape(callback) {
    return function (e) {
      if (e.key === keyboardUtils.KEYS.ESCAPE) {
        e.preventDefault();
        callback(e);
      }
    };
  }
};

/**
 * Focus management utilities
 */
var focusUtils = {
  /**
   * Focus trap for modals and dialogs
   */
  createFocusTrap: function createFocusTrap(containerRef) {
    var getFocusableElements = function getFocusableElements() {
      if (!containerRef.current) return [];
      var focusableSelectors = ['button:not([disabled])', 'input:not([disabled])', 'select:not([disabled])', 'textarea:not([disabled])', 'a[href]', '[tabindex]:not([tabindex="-1"])', '[contenteditable="true"]'].join(', ');
      return Array.from(containerRef.current.querySelectorAll(focusableSelectors));
    };
    var handleKeyDown = function handleKeyDown(e) {
      if (e.key !== keyboardUtils.KEYS.TAB) return;
      var focusableElements = getFocusableElements();
      if (focusableElements.length === 0) return;
      var firstElement = focusableElements[0];
      var lastElement = focusableElements[focusableElements.length - 1];
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };
    var activate = function activate() {
      var focusableElements = getFocusableElements();
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
      document.addEventListener('keydown', handleKeyDown);
    };
    var deactivate = function deactivate() {
      document.removeEventListener('keydown', handleKeyDown);
    };
    return {
      activate: activate,
      deactivate: deactivate
    };
  },
  /**
   * Restore focus to previous element
   */
  createFocusRestore: function createFocusRestore() {
    var previousActiveElement = document.activeElement;
    return function () {
      if (previousActiveElement && typeof previousActiveElement.focus === 'function') {
        previousActiveElement.focus();
      }
    };
  },
  /**
   * Focus first error in form validation
   */
  focusFirstError: function focusFirstError(formRef) {
    var errorClass = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '.ant-form-item-has-error';
    if (!formRef.current) return;
    var firstError = formRef.current.querySelector("".concat(errorClass, " input, ").concat(errorClass, " textarea, ").concat(errorClass, " select"));
    if (firstError) {
      firstError.focus();
      firstError.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  },
  /**
   * Manage focus for dynamic content
   */
  manageDynamicFocus: function manageDynamicFocus(newContentRef, announcement) {
    if (newContentRef.current) {
      // Focus the new content
      newContentRef.current.focus();

      // Announce the change
      if (announcement) {
        ariaUtils.announce(announcement);
      }
    }
  }
};

/**
 * Color contrast utilities for WCAG compliance
 */
var contrastUtils = {
  /**
   * Calculate relative luminance
   */
  getLuminance: function getLuminance(color) {
    var rgb = contrastUtils.hexToRgb(color);
    if (!rgb) return 0;
    var _map = [rgb.r, rgb.g, rgb.b].map(function (c) {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      }),
      _map2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_map, 3),
      r = _map2[0],
      g = _map2[1],
      b = _map2[2];
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  },
  /**
   * Convert hex to RGB
   */
  hexToRgb: function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },
  /**
   * Calculate contrast ratio between two colors
   */
  getContrastRatio: function getContrastRatio(color1, color2) {
    var lum1 = contrastUtils.getLuminance(color1);
    var lum2 = contrastUtils.getLuminance(color2);
    var brightest = Math.max(lum1, lum2);
    var darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },
  /**
   * Check if color combination meets WCAG standards
   */
  meetsWCAG: function meetsWCAG(foreground, background) {
    var level = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'AA';
    var size = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'normal';
    var ratio = contrastUtils.getContrastRatio(foreground, background);
    if (level === 'AAA') {
      return size === 'large' ? ratio >= 4.5 : ratio >= 7;
    }
    return size === 'large' ? ratio >= 3 : ratio >= 4.5;
  },
  /**
   * Get accessible text color for background
   */
  getAccessibleTextColor: function getAccessibleTextColor(backgroundColor) {
    var whiteContrast = contrastUtils.getContrastRatio('#FFFFFF', backgroundColor);
    var blackContrast = contrastUtils.getContrastRatio('#000000', backgroundColor);
    return whiteContrast > blackContrast ? '#FFFFFF' : '#000000';
  }
};

/**
 * Accessible drag and drop utilities
 */
var accessibleDragDrop = {
  /**
   * Create keyboard-accessible drag and drop
   */
  createKeyboardDragDrop: function createKeyboardDragDrop(items, onMove) {
    var draggedIndex = null;
    var dropTargetIndex = null;
    var handleKeyDown = function handleKeyDown(e, index) {
      switch (e.key) {
        case keyboardUtils.KEYS.SPACE:
          e.preventDefault();
          if (draggedIndex === null) {
            // Start drag
            draggedIndex = index;
            ariaUtils.announce("Picked up item ".concat(index + 1, ". Use arrow keys to move, space to drop, escape to cancel."));
          } else if (draggedIndex === index) {
            // Drop at current position
            if (dropTargetIndex !== null && dropTargetIndex !== draggedIndex) {
              onMove(draggedIndex, dropTargetIndex);
              ariaUtils.announce("Moved item from position ".concat(draggedIndex + 1, " to position ").concat(dropTargetIndex + 1, "."));
            }
            draggedIndex = null;
            dropTargetIndex = null;
          }
          break;
        case keyboardUtils.KEYS.ESCAPE:
          if (draggedIndex !== null) {
            e.preventDefault();
            draggedIndex = null;
            dropTargetIndex = null;
            ariaUtils.announce('Drag operation cancelled.');
          }
          break;
        case keyboardUtils.KEYS.ARROW_UP:
        case keyboardUtils.KEYS.ARROW_DOWN:
          if (draggedIndex !== null) {
            e.preventDefault();
            var direction = e.key === keyboardUtils.KEYS.ARROW_UP ? -1 : 1;
            var newIndex = Math.max(0, Math.min(items.length - 1, index + direction));
            if (newIndex !== index) {
              dropTargetIndex = newIndex;
              ariaUtils.announce("Moving to position ".concat(newIndex + 1, "."));
            }
          }
          break;
      }
    };
    return {
      handleKeyDown: handleKeyDown,
      getDragAttributes: function getDragAttributes(index) {
        return {
          'aria-grabbed': draggedIndex === index,
          'aria-dropeffect': draggedIndex !== null && draggedIndex !== index ? 'move' : 'none',
          'aria-describedby': "drag-instructions-".concat(index)
        };
      }
    };
  },
  /**
   * Create drag instructions for screen readers
   */
  createDragInstructions: function createDragInstructions(id) {
    return {
      id: "drag-instructions-".concat(id),
      className: 'sr-only',
      children: 'Press space to pick up this item. Use arrow keys to move it to a new position, then press space again to drop it. Press escape to cancel.'
    };
  }
};

/**
 * Screen reader utilities
 */
var screenReaderUtils = {
  /**
   * Create screen reader only content
   */
  srOnly: function srOnly(content) {
    return {
      className: 'sr-only',
      children: content,
      'aria-hidden': false
    };
  },
  /**
   * Hide decorative content from screen readers
   */
  hideFromScreenReader: function hideFromScreenReader() {
    return {
      'aria-hidden': true
    };
  },
  /**
   * Create accessible loading states
   */
  createLoadingState: function createLoadingState(isLoading) {
    var loadingText = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Loading...';
    return {
      'aria-busy': isLoading,
      'aria-live': 'polite',
      'aria-label': isLoading ? loadingText : undefined
    };
  },
  /**
   * Create accessible error states
   */
  createErrorState: function createErrorState(hasError, errorMessage) {
    return {
      'aria-invalid': hasError,
      'aria-describedby': hasError ? 'error-message' : undefined,
      role: hasError ? 'alert' : undefined
    };
  }
};

/**
 * High contrast mode utilities
 */
var highContrastUtils = {
  /**
   * Detect high contrast mode
   */
  isHighContrastMode: function isHighContrastMode() {
    return window.matchMedia('(prefers-contrast: high)').matches;
  },
  /**
   * Create high contrast styles
   */
  createHighContrastStyles: function createHighContrastStyles(baseStyles) {
    return _objectSpread(_objectSpread({}, baseStyles), {}, {
      '@media (prefers-contrast: high)': {
        border: '2px solid',
        outline: '1px solid',
        backgroundColor: 'Canvas',
        color: 'CanvasText'
      }
    });
  },
  /**
   * Ensure minimum contrast in high contrast mode
   */
  ensureHighContrast: function ensureHighContrast(element) {
    if (highContrastUtils.isHighContrastMode()) {
      element.style.border = '2px solid';
      element.style.outline = '1px solid';
    }
  }
};

/**
 * Reduced motion utilities
 */
var reducedMotionUtils = {
  /**
   * Check if user prefers reduced motion
   */
  prefersReducedMotion: function prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },
  /**
   * Create motion-safe animations
   */
  createMotionSafeAnimation: function createMotionSafeAnimation(animation) {
    return {
      animation: reducedMotionUtils.prefersReducedMotion() ? 'none' : animation,
      transition: reducedMotionUtils.prefersReducedMotion() ? 'none' : _design_system__WEBPACK_IMPORTED_MODULE_3__.theme.transitions["default"]
    };
  },
  /**
   * Respect reduced motion preferences
   */
  respectReducedMotion: function respectReducedMotion(styles) {
    return _objectSpread(_objectSpread({}, styles), {}, {
      '@media (prefers-reduced-motion: reduce)': {
        animation: 'none !important',
        transition: 'none !important'
      }
    });
  }
};

/**
 * Accessibility testing utilities
 */
var a11yTestUtils = {
  /**
   * Check if element has proper ARIA attributes
   */
  validateAriaAttributes: function validateAriaAttributes(element) {
    var issues = [];

    // Check for required ARIA attributes
    if (element.getAttribute('role') === 'button' && !element.hasAttribute('aria-label') && !element.textContent.trim()) {
      issues.push('Button missing accessible name');
    }
    if (element.hasAttribute('aria-describedby')) {
      var describedById = element.getAttribute('aria-describedby');
      if (!document.getElementById(describedById)) {
        issues.push("aria-describedby references non-existent element: ".concat(describedById));
      }
    }
    if (element.hasAttribute('aria-labelledby')) {
      var labelledById = element.getAttribute('aria-labelledby');
      if (!document.getElementById(labelledById)) {
        issues.push("aria-labelledby references non-existent element: ".concat(labelledById));
      }
    }
    return issues;
  },
  /**
   * Check keyboard accessibility
   */
  validateKeyboardAccess: function validateKeyboardAccess(element) {
    var issues = [];
    var interactiveElements = ['button', 'input', 'select', 'textarea', 'a'];
    var tagName = element.tagName.toLowerCase();
    var role = element.getAttribute('role');
    if (interactiveElements.includes(tagName) || role === 'button') {
      if (element.tabIndex < 0 && !element.hasAttribute('disabled')) {
        issues.push('Interactive element not keyboard accessible');
      }
    }
    return issues;
  },
  /**
   * Check color contrast
   */
  validateColorContrast: function validateColorContrast(element) {
    var issues = [];
    var computedStyle = window.getComputedStyle(element);
    var color = computedStyle.color;
    var backgroundColor = computedStyle.backgroundColor;
    if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
      var ratio = contrastUtils.getContrastRatio(color, backgroundColor);
      if (ratio < 4.5) {
        issues.push("Insufficient color contrast: ".concat(ratio.toFixed(2), ":1 (minimum 4.5:1)"));
      }
    }
    return issues;
  },
  /**
   * Run comprehensive accessibility audit
   */
  auditElement: function auditElement(element) {
    var issues = [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(a11yTestUtils.validateAriaAttributes(element)), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(a11yTestUtils.validateKeyboardAccess(element)), (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(a11yTestUtils.validateColorContrast(element)));
    return {
      element: element,
      issues: issues,
      isAccessible: issues.length === 0
    };
  }
};

/**
 * Accessibility monitoring utilities
 */
var a11yMonitorUtils = {
  /**
   * Monitor focus changes for debugging
   */
  monitorFocus: function monitorFocus() {
    var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
    if (!enabled) return;
    var previousFocus = null;
    var handleFocusChange = function handleFocusChange(e) {
      console.log('Focus changed:', {
        from: previousFocus,
        to: e.target,
        timestamp: new Date().toISOString()
      });
      previousFocus = e.target;
    };
    document.addEventListener('focusin', handleFocusChange);
    document.addEventListener('focusout', handleFocusChange);
    return function () {
      document.removeEventListener('focusin', handleFocusChange);
      document.removeEventListener('focusout', handleFocusChange);
    };
  },
  /**
   * Monitor ARIA live region announcements
   */
  monitorLiveRegions: function monitorLiveRegions() {
    var enabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
    if (!enabled) return;
    var observer = new MutationObserver(function (mutations) {
      mutations.forEach(function (mutation) {
        if (mutation.type === 'childList') {
          var target = mutation.target;
          var ariaLive = target.getAttribute('aria-live');
          if (ariaLive) {
            console.log('Live region updated:', {
              element: target,
              content: target.textContent,
              priority: ariaLive,
              timestamp: new Date().toISOString()
            });
          }
        }
      });
    });
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    return function () {
      return observer.disconnect();
    };
  },
  /**
   * Log accessibility violations
   */
  logViolations: function logViolations(violations) {
    if (false) {}
  }
};

/**
 * Enhanced contrast utilities for better accessibility
 */
var enhancedContrastUtils = {
  /**
   * Apply enhanced contrast mode for better accessibility
   */
  apply: function apply() {
    var enable = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
    var body = document.body;
    if (enable) {
      body.classList.add('enhanced-contrast');
      localStorage.setItem('accessibility_enhancedContrast', 'true');
    } else {
      body.classList.remove('enhanced-contrast');
      localStorage.setItem('accessibility_enhancedContrast', 'false');
    }
  },
  /**
   * Check if enhanced contrast mode is enabled
   */
  isEnabled: function isEnabled() {
    return localStorage.getItem('accessibility_enhancedContrast') === 'true' || document.body.classList.contains('enhanced-contrast');
  },
  /**
   * Initialize enhanced contrast mode based on user preference
   */
  init: function init() {
    var isEnabled = localStorage.getItem('accessibility_enhancedContrast') === 'true';
    if (isEnabled) {
      enhancedContrastUtils.apply(true);
    }
  },
  /**
   * Toggle enhanced contrast mode
   */
  toggle: function toggle() {
    var isCurrentlyEnabled = enhancedContrastUtils.isEnabled();
    enhancedContrastUtils.apply(!isCurrentlyEnabled);
    return !isCurrentlyEnabled;
  }
};

// Export all utilities
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  aria: ariaUtils,
  keyboard: keyboardUtils,
  focus: focusUtils,
  contrast: contrastUtils,
  dragDrop: accessibleDragDrop,
  screenReader: screenReaderUtils,
  highContrast: highContrastUtils,
  reducedMotion: reducedMotionUtils,
  testing: a11yTestUtils,
  monitoring: a11yMonitorUtils,
  enhancedContrast: enhancedContrastUtils
});

/***/ }),

/***/ 38965:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   _q: () => (/* binding */ handleWebSocketError),
/* harmony export */   li: () => (/* binding */ isServiceWorkerRelatedError)
/* harmony export */ });
/* unused harmony exports setupGlobalWebSocketErrorHandler, handleFetchEventNetworkError */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _serviceWorkerRegistration__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(77362);


/**
 * WebSocket Error Handler
 *
 * This utility provides functions to detect and fix WebSocket connection issues,
 * particularly those related to service worker interference.
 */



/**
 * Error codes that might indicate service worker interference
 */
var SERVICE_WORKER_RELATED_ERRORS = ['NS_ERROR_CORRUPTED_CONTENT', 'NS_ERROR_NET_INADEQUATE_SECURITY', 'SECURITY_ERR', 'NetworkError', 'network error response', 'fetch event', 'resulted in network error', 'promise was rejected'];

/**
 * Check if an error is likely caused by service worker interference
 * @param {Error|string} error - The error object or message
 * @returns {boolean} - True if the error is likely caused by service worker
 */
function isServiceWorkerRelatedError(error) {
  var errorMessage = error instanceof Error ? error.message : String(error);
  return SERVICE_WORKER_RELATED_ERRORS.some(function (code) {
    return errorMessage.includes(code) || errorMessage.toLowerCase().includes('serviceworker') || errorMessage.toLowerCase().includes('service worker');
  });
}

/**
 * Handle WebSocket connection errors
 * @param {Error|string} error - The error that occurred
 * @returns {Promise<boolean>} - True if the error was handled
 */
function handleWebSocketError(_x) {
  return _handleWebSocketError.apply(this, arguments);
}

/**
 * Add global error handler for WebSocket issues
 */
function _handleWebSocketError() {
  _handleWebSocketError = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(error) {
    var shouldFix;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          console.error('WebSocket error occurred:', error);
          if (!isServiceWorkerRelatedError(error)) {
            _context.next = 1;
            break;
          }
          console.log('Detected service worker related WebSocket error');

          // Show a user-friendly error message
          shouldFix = window.confirm('A service worker is interfering with the WebSocket connection. ' + 'Would you like to fix this issue? This will refresh the page.');
          if (!shouldFix) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return", (0,_serviceWorkerRegistration__WEBPACK_IMPORTED_MODULE_2__/* .fixWebSocketIssues */ .jY)());
        case 1:
          return _context.abrupt("return", false);
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _handleWebSocketError.apply(this, arguments);
}
function setupGlobalWebSocketErrorHandler() {
  // Listen for unhandled errors that might be WebSocket related
  window.addEventListener('error', function (event) {
    if (event.message && (event.message.includes('WebSocket') || isServiceWorkerRelatedError(event.message))) {
      console.log('Caught global WebSocket error:', event.message);
      handleWebSocketError(event.message);
    }
  });

  // Listen for unhandled promise rejections
  window.addEventListener('unhandledrejection', function (event) {
    var error = event.reason;
    if (error && (String(error).includes('WebSocket') || isServiceWorkerRelatedError(error))) {
      console.log('Caught unhandled WebSocket promise rejection:', error);
      handleWebSocketError(error);
    }
  });

  // Patch the WebSocket constructor to catch errors
  var OriginalWebSocket = window.WebSocket;
  window.WebSocket = function (url, protocols) {
    try {
      return new OriginalWebSocket(url, protocols);
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      if (isServiceWorkerRelatedError(error)) {
        handleWebSocketError(error);
      }
      throw error;
    }
  };

  // Copy all properties from the original WebSocket
  for (var prop in OriginalWebSocket) {
    if (OriginalWebSocket.hasOwnProperty(prop)) {
      window.WebSocket[prop] = OriginalWebSocket[prop];
    }
  }
  window.WebSocket.prototype = OriginalWebSocket.prototype;
  console.log('Global WebSocket error handler set up');
}

/**
 * Specifically handle the "fetch event resulted in network error" issue
 * @param {string} url - The URL that caused the error
 * @returns {Promise<boolean>} - True if the error was handled
 */
function handleFetchEventNetworkError(_x2) {
  return _handleFetchEventNetworkError.apply(this, arguments);
}
function _handleFetchEventNetworkError() {
  _handleFetchEventNetworkError = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(url) {
    var isWebSocketUrl, shouldFix;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          console.error("Fetch event for \"".concat(url, "\" resulted in network error response, the promise was rejected"));

          // Check if this is a WebSocket URL
          isWebSocketUrl = url.startsWith('ws:') || url.startsWith('wss:') || url.includes('/ws/') || url.includes('/socket');
          if (!isWebSocketUrl) {
            _context2.next = 2;
            break;
          }
          console.log('Detected WebSocket URL with fetch event network error');

          // Show a user-friendly error message
          shouldFix = window.confirm('A service worker is preventing WebSocket connections. ' + 'Would you like to fix this issue? This will refresh the page.');
          if (!shouldFix) {
            _context2.next = 2;
            break;
          }
          _context2.next = 1;
          return (0,_serviceWorkerRegistration__WEBPACK_IMPORTED_MODULE_2__/* .forceCleanServiceWorkers */ .hl)();
        case 1:
          // If that doesn't work, try to fix WebSocket issues
          setTimeout(function () {
            (0,_serviceWorkerRegistration__WEBPACK_IMPORTED_MODULE_2__/* .fixWebSocketIssues */ .jY)();
          }, 1000);
          return _context2.abrupt("return", true);
        case 2:
          return _context2.abrupt("return", false);
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return _handleFetchEventNetworkError.apply(this, arguments);
}
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  handleWebSocketError: handleWebSocketError,
  isServiceWorkerRelatedError: isServiceWorkerRelatedError,
  setupGlobalWebSocketErrorHandler: setupGlobalWebSocketErrorHandler,
  handleFetchEventNetworkError: handleFetchEventNetworkError
});

/***/ }),

/***/ 50263:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Ay: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony exports setCache, getCache, clearCache, getStoreData, dispatchAction, batchActions */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _redux_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(75063);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1807);


/**
 * Data Manager Utility
 * 
 * This utility provides functions for managing data flow between components,
 * optimizing Redux store usage, and handling data operations.
 */




/**
 * Cache for storing temporary data that doesn't need to be in Redux
 */
var dataCache = {
  temporary: new Map(),
  persistent: new Map(),
  expiring: new Map(),
  timestamps: new Map()
};

/**
 * Set data in the cache
 * @param {string} key - The key to store the data under
 * @param {any} data - The data to store
 * @param {Object} options - Options for storing the data
 * @param {string} options.type - The type of cache to use ('temporary', 'persistent', or 'expiring')
 * @param {number} options.expiresIn - Time in milliseconds after which the data expires (for 'expiring' type)
 */
var setCache = function setCache(key, data) {
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {
    type: 'temporary',
    expiresIn: 3600000
  };
  var type = options.type,
    expiresIn = options.expiresIn;
  switch (type) {
    case 'persistent':
      dataCache.persistent.set(key, data);
      try {
        localStorage.setItem("cache_".concat(key), JSON.stringify(data));
      } catch (error) {
        console.warn('Failed to save to localStorage:', error);
      }
      break;
    case 'expiring':
      dataCache.expiring.set(key, data);
      dataCache.timestamps.set(key, Date.now() + expiresIn);
      break;
    case 'temporary':
    default:
      dataCache.temporary.set(key, data);
      break;
  }
};

/**
 * Get data from the cache
 * @param {string} key - The key to retrieve data for
 * @param {string} type - The type of cache to use ('temporary', 'persistent', or 'expiring')
 * @returns {any} The cached data or null if not found
 */
var getCache = function getCache(key) {
  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'temporary';
  switch (type) {
    case 'persistent':
      {
        // Try memory cache first
        if (dataCache.persistent.has(key)) {
          return dataCache.persistent.get(key);
        }

        // Try localStorage
        try {
          var data = localStorage.getItem("cache_".concat(key));
          if (data) {
            var parsed = JSON.parse(data);
            // Update memory cache
            dataCache.persistent.set(key, parsed);
            return parsed;
          }
        } catch (error) {
          console.warn('Failed to retrieve from localStorage:', error);
        }
        return null;
      }
    case 'expiring':
      {
        if (dataCache.expiring.has(key)) {
          var timestamp = dataCache.timestamps.get(key);
          if (timestamp && timestamp > Date.now()) {
            return dataCache.expiring.get(key);
          } else {
            // Expired, clean up
            dataCache.expiring["delete"](key);
            dataCache.timestamps["delete"](key);
          }
        }
        return null;
      }
    case 'temporary':
    default:
      return dataCache.temporary.get(key) || null;
  }
};

/**
 * Clear data from the cache
 * @param {string} key - The key to clear (if not provided, clears all data of the specified type)
 * @param {string} type - The type of cache to clear ('temporary', 'persistent', 'expiring', or 'all')
 */
var clearCache = function clearCache(key) {
  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'temporary';
  if (key) {
    // Clear specific key
    switch (type) {
      case 'persistent':
        dataCache.persistent["delete"](key);
        try {
          localStorage.removeItem("cache_".concat(key));
        } catch (error) {
          console.warn('Failed to remove from localStorage:', error);
        }
        break;
      case 'expiring':
        dataCache.expiring["delete"](key);
        dataCache.timestamps["delete"](key);
        break;
      case 'all':
        dataCache.temporary["delete"](key);
        dataCache.persistent["delete"](key);
        dataCache.expiring["delete"](key);
        dataCache.timestamps["delete"](key);
        try {
          localStorage.removeItem("cache_".concat(key));
        } catch (error) {
          console.warn('Failed to remove from localStorage:', error);
        }
        break;
      case 'temporary':
      default:
        dataCache.temporary["delete"](key);
        break;
    }
  } else {
    // Clear all data of specified type
    switch (type) {
      case 'persistent':
        dataCache.persistent.clear();
        try {
          Object.keys(localStorage).forEach(function (key) {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
        break;
      case 'expiring':
        dataCache.expiring.clear();
        dataCache.timestamps.clear();
        break;
      case 'all':
        dataCache.temporary.clear();
        dataCache.persistent.clear();
        dataCache.expiring.clear();
        dataCache.timestamps.clear();
        try {
          Object.keys(localStorage).forEach(function (key) {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
        break;
      case 'temporary':
      default:
        dataCache.temporary.clear();
        break;
    }
  }
};

/**
 * Get data from Redux store with error handling
 * @param {Function} selector - Redux selector function
 * @param {any} defaultValue - Default value to return if selector fails
 * @returns {any} The selected data or default value
 */
var getStoreData = function getStoreData(selector) {
  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
  try {
    return selector(_redux_store__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.getState());
  } catch (error) {
    console.error('Error accessing Redux store:', error);
    return defaultValue;
  }
};

/**
 * Dispatch an action to Redux store with error handling
 * @param {Object|Function} action - Redux action or thunk
 * @returns {Promise<any>} Promise that resolves with the result of the dispatch
 */
var dispatchAction = /*#__PURE__*/function () {
  var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(action) {
    var _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 1;
          return _redux_store__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.dispatch(action);
        case 1:
          return _context.abrupt("return", _context.sent);
        case 2:
          _context.prev = 2;
          _t = _context["catch"](0);
          console.error('Error dispatching action:', _t);
          antd__WEBPACK_IMPORTED_MODULE_3__/* .message */ .iU.error('An error occurred while updating data');
          throw _t;
        case 3:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 2]]);
  }));
  return function dispatchAction(_x) {
    return _ref.apply(this, arguments);
  };
}();

/**
 * Batch multiple Redux actions into a single update
 * @param {Array<Object|Function>} actions - Array of Redux actions or thunks
 * @returns {Promise<Array<any>>} Promise that resolves with results of all dispatches
 */
var batchActions = /*#__PURE__*/function () {
  var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(actions) {
    var results, promises, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          results = []; // Start all dispatches
          promises = actions.map(function (action) {
            return _redux_store__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.dispatch(action);
          }); // Wait for all to complete
          _context2.next = 1;
          return Promise.all(promises);
        case 1:
          return _context2.abrupt("return", _context2.sent);
        case 2:
          _context2.prev = 2;
          _t2 = _context2["catch"](0);
          console.error('Error in batch actions:', _t2);
          antd__WEBPACK_IMPORTED_MODULE_3__/* .message */ .iU.error('An error occurred while updating multiple items');
          throw _t2;
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 2]]);
  }));
  return function batchActions(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  setCache: setCache,
  getCache: getCache,
  clearCache: clearCache,
  getStoreData: getStoreData,
  dispatchAction: dispatchAction,
  batchActions: batchActions
});

/***/ })

}]);