/**
 * Debug script to check if feature buttons are visible in the App Builder
 * Run this in the browser console to diagnose button visibility issues
 */

function debugFeatureButtons() {
  console.log('🔍 Debugging App Builder Feature Buttons...');
  
  // Check if we're on the right page
  const currentPath = window.location.pathname;
  console.log(`📍 Current path: ${currentPath}`);
  
  if (!currentPath.includes('app-builder')) {
    console.warn('⚠️ Not on app-builder page. Navigate to /app-builder first.');
    return;
  }
  
  // Look for the IntegratedAppBuilder component
  const appBuilderElement = document.querySelector('[data-testid="integrated-app-builder"]') || 
                           document.querySelector('.integrated-app-builder') ||
                           document.querySelector('div[class*="IntegratedContainer"]');
  
  if (!appBuilderElement) {
    console.error('❌ IntegratedAppBuilder component not found');
    return;
  }
  
  console.log('✅ IntegratedAppBuilder component found');
  
  // Look for feature buttons
  const featureButtons = {
    testing: {
      selectors: [
        'button[data-tutorial="testing-tools"]',
        'button:contains("Testing")',
        'button:contains("🧪")'
      ],
      name: 'Testing Tools'
    },
    performance: {
      selectors: [
        'button[data-tutorial="performance-monitor"]',
        'button:contains("Performance")',
        'button:contains("⚡")'
      ],
      name: 'Performance Monitor'
    },
    dataManagement: {
      selectors: [
        'button[data-tutorial="data-management"]',
        'button:contains("Data")',
        'button:contains("📊")'
      ],
      name: 'Data Management'
    },
    enhancedExport: {
      selectors: [
        'button[data-tutorial="code-export"]',
        'button:contains("Export")',
        'button:contains("📤")'
      ],
      name: 'Enhanced Export'
    }
  };
  
  console.log('\n🔍 Searching for feature buttons...');
  
  Object.entries(featureButtons).forEach(([key, config]) => {
    let found = false;
    
    for (const selector of config.selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`✅ ${config.name} button found:`, elements[0]);
        console.log(`   - Selector: ${selector}`);
        console.log(`   - Visible: ${elements[0].offsetParent !== null}`);
        console.log(`   - Text: "${elements[0].textContent.trim()}"`);
        found = true;
        break;
      }
    }
    
    if (!found) {
      console.warn(`❌ ${config.name} button not found`);
    }
  });
  
  // Check for FeatureToggles container
  const featureToggles = document.querySelector('div[class*="FeatureToggles"]') ||
                        document.querySelector('[data-testid="feature-toggles"]');
  
  if (featureToggles) {
    console.log('\n✅ FeatureToggles container found');
    console.log('   - Children count:', featureToggles.children.length);
    console.log('   - All buttons in container:');
    
    const buttons = featureToggles.querySelectorAll('button');
    buttons.forEach((btn, index) => {
      console.log(`     ${index + 1}. "${btn.textContent.trim()}" (visible: ${btn.offsetParent !== null})`);
    });
  } else {
    console.warn('❌ FeatureToggles container not found');
  }
  
  // Check for any React errors
  const reactErrors = document.querySelectorAll('[data-reactroot] .error, .react-error-overlay');
  if (reactErrors.length > 0) {
    console.error('❌ React errors detected:', reactErrors);
  }
  
  // Check enableFeatures prop
  console.log('\n🔍 Checking enableFeatures configuration...');
  
  // Try to find React component instance (this is a hack but useful for debugging)
  const reactInstance = appBuilderElement._reactInternalInstance || 
                       appBuilderElement.__reactInternalInstance ||
                       Object.keys(appBuilderElement).find(key => key.startsWith('__reactInternalInstance'));
  
  if (reactInstance) {
    console.log('✅ React instance found - check component props in React DevTools');
  }
  
  // Final summary
  console.log('\n📋 Summary:');
  console.log('- If buttons are not visible, check:');
  console.log('  1. enableFeatures prop in App.js');
  console.log('  2. CSS styling (display, visibility, z-index)');
  console.log('  3. Component import errors in console');
  console.log('  4. React component rendering errors');
  console.log('\n- To manually test a button, try:');
  console.log('  document.querySelector("button[data-tutorial=\\"testing-tools\\"]").click()');
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  // Wait for React to render
  setTimeout(debugFeatureButtons, 2000);
}

// Export for manual use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = debugFeatureButtons;
} else if (typeof window !== 'undefined') {
  window.debugFeatureButtons = debugFeatureButtons;
}
