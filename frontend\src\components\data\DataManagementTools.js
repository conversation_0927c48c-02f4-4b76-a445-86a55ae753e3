/**
 * Data Management Tools Component
 * 
 * Provides data binding, state management, and data flow visualization tools
 * for components with real-time updates and visual state tracking.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Alert,
  Tree,
  Table,
  Input,
  Select,
  Switch,
  Modal,
  Form,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  Divider,
  notification,
  Badge
} from 'antd';
import {
  DatabaseOutlined,
  NodeIndexOutlined,
  LinkOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  ShareAltOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  MonitorOutlined,
  ApiOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

// Styled Components
const DataContainer = styled.div`
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const DataFlowVisualization = styled.div`
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  padding: 24px;
  margin: 16px 0;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const StateNode = styled.div`
  background: ${props => props.active ? '#e6f7ff' : '#fff'};
  border: 2px solid ${props => props.active ? '#1890ff' : '#d9d9d9'};
  border-radius: 8px;
  padding: 12px;
  margin: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }
`;

const DataMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

// Mock data for demonstration
const generateMockStateData = () => ({
  global: {
    user: { id: 1, name: 'John Doe', role: 'admin' },
    theme: { mode: 'light', primaryColor: '#1890ff' },
    settings: { language: 'en', notifications: true }
  },
  components: {
    'component-1': { visible: true, loading: false, data: { title: 'Header', items: 5 } },
    'component-2': { visible: true, loading: true, data: { content: 'Loading...' } },
    'component-3': { visible: false, loading: false, data: { list: ['item1', 'item2'] } }
  },
  api: {
    '/api/users': { status: 'success', lastFetch: Date.now(), cache: true },
    '/api/projects': { status: 'loading', lastFetch: null, cache: false },
    '/api/settings': { status: 'error', lastFetch: Date.now() - 30000, cache: false }
  }
});

const generateMockDataBindings = () => [
  {
    id: 'binding-1',
    source: 'global.user.name',
    target: 'component-1.props.title',
    type: 'direct',
    active: true,
    lastUpdate: Date.now()
  },
  {
    id: 'binding-2',
    source: 'api./api/users',
    target: 'component-2.data.users',
    type: 'async',
    active: true,
    lastUpdate: Date.now() - 5000
  },
  {
    id: 'binding-3',
    source: 'components.component-1.data.items',
    target: 'components.component-3.props.count',
    type: 'computed',
    active: false,
    lastUpdate: Date.now() - 10000
  }
];

/**
 * DataManagementTools Component
 */
const DataManagementTools = ({
  components = [],
  onDataChange,
  onBindingCreate,
  onBindingUpdate,
  onBindingDelete,
  realTimeUpdates = true,
  showVisualization = true,
  compact = false
}) => {
  // State
  const [activeTab, setActiveTab] = useState('overview');
  const [stateData, setStateData] = useState(generateMockStateData());
  const [dataBindings, setDataBindings] = useState(generateMockDataBindings());
  const [selectedNode, setSelectedNode] = useState(null);
  const [showBindingModal, setShowBindingModal] = useState(false);
  const [editingBinding, setEditingBinding] = useState(null);
  const [monitoringEnabled, setMonitoringEnabled] = useState(true);
  const [form] = Form.useForm();

  // Computed metrics
  const dataMetrics = useMemo(() => {
    const totalBindings = dataBindings.length;
    const activeBindings = dataBindings.filter(b => b.active).length;
    const totalComponents = Object.keys(stateData.components).length;
    const loadingComponents = Object.values(stateData.components).filter(c => c.loading).length;

    return {
      totalBindings,
      activeBindings,
      totalComponents,
      loadingComponents,
      bindingHealth: totalBindings > 0 ? Math.round((activeBindings / totalBindings) * 100) : 100
    };
  }, [dataBindings, stateData]);

  // Generate tree data for state visualization
  const stateTreeData = useMemo(() => {
    const createTreeNode = (key, value, path = '') => {
      const fullPath = path ? `${path}.${key}` : key;

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        return {
          title: (
            <Space>
              <Text strong>{key}</Text>
              <Tag size="small">object</Tag>
            </Space>
          ),
          key: fullPath,
          children: Object.entries(value).map(([k, v]) => createTreeNode(k, v, fullPath))
        };
      }

      return {
        title: (
          <Space>
            <Text>{key}</Text>
            <Text type="secondary">: {JSON.stringify(value)}</Text>
            <Tag size="small" color={typeof value === 'string' ? 'blue' : 'green'}>
              {typeof value}
            </Tag>
          </Space>
        ),
        key: fullPath,
        isLeaf: true
      };
    };

    return Object.entries(stateData).map(([key, value]) => createTreeNode(key, value));
  }, [stateData]);

  // Data binding table columns
  const bindingColumns = [
    {
      title: 'Source',
      dataIndex: 'source',
      key: 'source',
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: 'Target',
      dataIndex: 'target',
      key: 'target',
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'direct' ? 'blue' :
            type === 'async' ? 'orange' : 'purple'
        }>
          {type.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Badge
          status={active ? 'success' : 'default'}
          text={active ? 'Active' : 'Inactive'}
        />
      )
    },
    {
      title: 'Last Update',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
      render: (timestamp) => (
        <Text type="secondary">
          {new Date(timestamp).toLocaleTimeString()}
        </Text>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditBinding(record)}
          />
          <Button
            size="small"
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDeleteBinding(record.id)}
          />
        </Space>
      )
    }
  ];

  // Handle binding operations
  const handleCreateBinding = useCallback(() => {
    setEditingBinding(null);
    form.resetFields();
    setShowBindingModal(true);
  }, [form]);

  const handleEditBinding = useCallback((binding) => {
    setEditingBinding(binding);
    form.setFieldsValue(binding);
    setShowBindingModal(true);
  }, [form]);

  const handleDeleteBinding = useCallback((bindingId) => {
    setDataBindings(prev => prev.filter(b => b.id !== bindingId));
    if (onBindingDelete) {
      onBindingDelete(bindingId);
    }
    notification.success({
      message: 'Binding Deleted',
      description: 'Data binding has been removed successfully.',
      duration: 3
    });
  }, [onBindingDelete]);

  const handleSaveBinding = useCallback(async () => {
    try {
      const values = await form.validateFields();
      const binding = {
        ...values,
        id: editingBinding?.id || `binding-${Date.now()}`,
        lastUpdate: Date.now(),
        active: true
      };

      if (editingBinding) {
        setDataBindings(prev => prev.map(b => b.id === editingBinding.id ? binding : b));
        if (onBindingUpdate) {
          onBindingUpdate(binding);
        }
      } else {
        setDataBindings(prev => [...prev, binding]);
        if (onBindingCreate) {
          onBindingCreate(binding);
        }
      }

      setShowBindingModal(false);
      notification.success({
        message: editingBinding ? 'Binding Updated' : 'Binding Created',
        description: 'Data binding has been saved successfully.',
        duration: 3
      });
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  }, [form, editingBinding, onBindingCreate, onBindingUpdate]);

  // Simulate real-time updates
  useEffect(() => {
    if (!realTimeUpdates || !monitoringEnabled) return;

    const interval = setInterval(() => {
      // Simulate state changes
      setStateData(prev => ({
        ...prev,
        components: {
          ...prev.components,
          [`component-${Math.floor(Math.random() * 3) + 1}`]: {
            ...prev.components[`component-${Math.floor(Math.random() * 3) + 1}`],
            loading: Math.random() > 0.7,
            data: {
              ...prev.components[`component-${Math.floor(Math.random() * 3) + 1}`]?.data,
              lastUpdate: Date.now()
            }
          }
        }
      }));

      // Update binding timestamps
      setDataBindings(prev => prev.map(binding => ({
        ...binding,
        lastUpdate: binding.active ? Date.now() : binding.lastUpdate
      })));
    }, 3000);

    return () => clearInterval(interval);
  }, [realTimeUpdates, monitoringEnabled]);

  return (
    <DataContainer>
      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              <DatabaseOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              Data Management
            </Title>
          </Col>
          <Col>
            <Space>
              <Switch
                checked={monitoringEnabled}
                onChange={setMonitoringEnabled}
                checkedChildren="Live"
                unCheckedChildren="Static"
              />
              <Button
                icon={<SettingOutlined />}
                onClick={() => {/* Settings modal */ }}
              >
                Settings
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateBinding}
              >
                New Binding
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <DataMetrics>
        <Card size="small">
          <Statistic
            title="Total Bindings"
            value={dataMetrics.totalBindings}
            prefix={<LinkOutlined />}
          />
        </Card>
        <Card size="small">
          <Statistic
            title="Active Bindings"
            value={dataMetrics.activeBindings}
            prefix={<ThunderboltOutlined />}
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
        <Card size="small">
          <Statistic
            title="Components"
            value={dataMetrics.totalComponents}
            prefix={<NodeIndexOutlined />}
          />
        </Card>
        <Card size="small">
          <Statistic
            title="Binding Health"
            value={dataMetrics.bindingHealth}
            suffix="%"
            prefix={<MonitorOutlined />}
            valueStyle={{ color: dataMetrics.bindingHealth >= 80 ? '#3f8600' : '#cf1322' }}
          />
        </Card>
      </DataMetrics>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Overview" key="overview">
          {showVisualization && (
            <Card title="Data Flow Visualization" style={{ marginBottom: 16 }}>
              <DataFlowVisualization>
                <div style={{ textAlign: 'center' }}>
                  <BranchesOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                  <div>
                    <Text type="secondary">Data flow visualization will be displayed here</Text>
                    <br />
                    <Text type="secondary">Showing connections between {dataMetrics.totalBindings} bindings</Text>
                  </div>
                </div>
              </DataFlowVisualization>
            </Card>
          )}

          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Recent Activity" size="small">
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  {dataBindings
                    .sort((a, b) => b.lastUpdate - a.lastUpdate)
                    .slice(0, 5)
                    .map(binding => (
                      <div key={binding.id} style={{ marginBottom: 8, padding: 8, background: '#fafafa', borderRadius: 4 }}>
                        <Text strong>{binding.source}</Text>
                        <Text type="secondary"> → </Text>
                        <Text>{binding.target}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {new Date(binding.lastUpdate).toLocaleString()}
                        </Text>
                      </div>
                    ))}
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Component Status" size="small">
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  {Object.entries(stateData.components).map(([id, component]) => (
                    <div key={id} style={{ marginBottom: 8, padding: 8, background: '#fafafa', borderRadius: 4 }}>
                      <Space>
                        <Text strong>{id}</Text>
                        <Badge status={component.loading ? 'processing' : component.visible ? 'success' : 'default'} />
                        {component.loading && <Tag color="orange">Loading</Tag>}
                        {!component.visible && <Tag color="default">Hidden</Tag>}
                      </Space>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="State Tree" key="state">
          <Card title="Application State" extra={
            <Button icon={<ReloadOutlined />} onClick={() => setStateData(generateMockStateData())}>
              Refresh
            </Button>
          }>
            <Tree
              treeData={stateTreeData}
              onSelect={(selectedKeys) => setSelectedNode(selectedKeys[0])}
              showLine
              showIcon={false}
              defaultExpandAll
            />
          </Card>
        </TabPane>

        <TabPane tab="Data Bindings" key="bindings">
          <Card
            title="Data Bindings"
            extra={
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateBinding}>
                Add Binding
              </Button>
            }
          >
            <Table
              dataSource={dataBindings}
              columns={bindingColumns}
              rowKey="id"
              size="small"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="API Monitor" key="api">
          <Card title="API Endpoints">
            <div>
              {Object.entries(stateData.api).map(([endpoint, status]) => (
                <Card key={endpoint} size="small" style={{ marginBottom: 8 }}>
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space>
                        <Text code>{endpoint}</Text>
                        <Badge
                          status={
                            status.status === 'success' ? 'success' :
                              status.status === 'loading' ? 'processing' : 'error'
                          }
                          text={status.status.toUpperCase()}
                        />
                      </Space>
                    </Col>
                    <Col>
                      <Space>
                        {status.cache && <Tag color="blue">Cached</Tag>}
                        {status.lastFetch && (
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {new Date(status.lastFetch).toLocaleTimeString()}
                          </Text>
                        )}
                      </Space>
                    </Col>
                  </Row>
                </Card>
              ))}
            </div>
          </Card>
        </TabPane>
      </Tabs>

      {/* Binding Modal */}
      <Modal
        title={editingBinding ? 'Edit Data Binding' : 'Create Data Binding'}
        open={showBindingModal}
        onCancel={() => setShowBindingModal(false)}
        onOk={handleSaveBinding}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="source"
            label="Source Path"
            rules={[{ required: true, message: 'Please enter source path' }]}
          >
            <Input placeholder="e.g., global.user.name" />
          </Form.Item>
          <Form.Item
            name="target"
            label="Target Path"
            rules={[{ required: true, message: 'Please enter target path' }]}
          >
            <Input placeholder="e.g., component-1.props.title" />
          </Form.Item>
          <Form.Item
            name="type"
            label="Binding Type"
            rules={[{ required: true, message: 'Please select binding type' }]}
          >
            <Select placeholder="Select binding type">
              <Option value="direct">Direct</Option>
              <Option value="async">Async</Option>
              <Option value="computed">Computed</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </DataContainer>
  );
};

export default DataManagementTools;
