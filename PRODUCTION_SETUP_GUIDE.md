# Production Setup and Troubleshooting Guide

## Overview
This guide walks you through setting up and running the App Builder 201 project in production mode locally, identifying common production issues, and troubleshooting problems.

## Prerequisites
- <PERSON><PERSON> and Docker Compose installed
- At least 4GB RAM available
- 50GB+ disk space
- PowerShell (Windows) or Bash (Linux/macOS)

## Step 1: Environment Configuration

### 1.1 Update Production Environment Variables

Before starting, you need to update the production environment files with appropriate values for local testing:

**Backend Environment (`backend/.env.production`):**
```bash
# Update these critical settings for local production testing:
ALLOWED_HOSTS=localhost,127.0.0.1,backend,frontend
DB_PASSWORD=secure_local_prod_password_123
REDIS_PASSWORD=secure_redis_password_123

# For local testing, disable SSL redirects:
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# Update CORS for local testing:
CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1,http://localhost:80
```

**Frontend Environment (`frontend/.env.production`):**
```bash
# Update API URLs for local production testing:
REACT_APP_API_URL=http://localhost/api
REACT_APP_WS_URL=ws://localhost/ws
```

### 1.2 Set Environment Variables
Create a `.env` file in the root directory:
```bash
DB_PASSWORD=secure_local_prod_password_123
REDIS_PASSWORD=secure_redis_password_123
```

## Step 2: Build Production Images

### 2.1 Clean Previous Builds
```powershell
# Stop any running containers
docker-compose -f docker-compose.prod.yml down -v

# Remove old images (optional)
docker system prune -f
```

### 2.2 Build Production Images
```powershell
# Build all production images
docker-compose -f docker-compose.prod.yml build --no-cache
```

**Expected Output:**
- Backend image builds successfully with Django static files collected
- Frontend image builds with optimized React bundle
- All dependencies installed without errors

## Step 3: Start Production Environment

### 3.1 Start Services
```powershell
# Start all services in detached mode
docker-compose -f docker-compose.prod.yml up -d
```

### 3.2 Monitor Startup
```powershell
# Watch logs for all services
docker-compose -f docker-compose.prod.yml logs -f

# Check specific service logs
docker-compose -f docker-compose.prod.yml logs backend
docker-compose -f docker-compose.prod.yml logs frontend
docker-compose -f docker-compose.prod.yml logs nginx
```

## Step 4: Verify Production Setup

### 4.1 Check Container Health
```powershell
# Check container status
docker-compose -f docker-compose.prod.yml ps

# Check health status
docker-compose -f docker-compose.prod.yml exec backend curl -f http://localhost:8000/health/
```

### 4.2 Test Application Access
1. **Frontend:** http://localhost
2. **Backend API:** http://localhost/api/
3. **Admin Panel:** http://localhost/admin/
4. **Health Check:** http://localhost/health

## Step 5: Common Production Issues and Solutions

### 5.1 Database Connection Issues

**Symptoms:**
- Backend fails to start
- Database connection errors in logs

**Solutions:**
```powershell
# Check database container
docker-compose -f docker-compose.prod.yml logs db

# Verify database is ready
docker-compose -f docker-compose.prod.yml exec db pg_isready -U app_builder_user

# Run migrations manually if needed
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate
```

### 5.2 Static Files Not Loading

**Symptoms:**
- CSS/JS files return 404 errors
- Admin panel appears unstyled

**Solutions:**
```powershell
# Rebuild backend with static files
docker-compose -f docker-compose.prod.yml exec backend python manage.py collectstatic --noinput

# Check static files volume
docker volume inspect app-builder-201_static_volume
```

### 5.3 Frontend Build Failures

**Symptoms:**
- Frontend container fails to start
- Build errors in logs

**Solutions:**
```powershell
# Check frontend build logs
docker-compose -f docker-compose.prod.yml logs frontend

# Rebuild frontend container
docker-compose -f docker-compose.prod.yml build frontend --no-cache
```

### 5.4 WebSocket Connection Issues

**Symptoms:**
- Real-time features not working
- WebSocket connection errors in browser console

**Solutions:**
```powershell
# Check nginx WebSocket configuration
docker-compose -f docker-compose.prod.yml exec nginx nginx -t

# Test WebSocket endpoint
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost/ws/
```

### 5.5 Memory and Performance Issues

**Symptoms:**
- Containers running out of memory
- Slow response times

**Solutions:**
```powershell
# Monitor resource usage
docker stats

# Check container resource limits
docker-compose -f docker-compose.prod.yml config
```

## Step 6: Production Monitoring

### 6.1 Health Checks
```powershell
# Automated health check script
./scripts/check-containers.ps1
```

### 6.2 Log Monitoring
```powershell
# Monitor all logs
docker-compose -f docker-compose.prod.yml logs -f --tail=100

# Monitor specific service
docker-compose -f docker-compose.prod.yml logs -f backend
```

### 6.3 Performance Monitoring
```powershell
# Check resource usage
docker-compose -f docker-compose.prod.yml top

# Monitor network connections
docker-compose -f docker-compose.prod.yml exec backend netstat -tulpn
```

## Step 7: Troubleshooting Commands

### 7.1 Container Debugging
```powershell
# Access container shell
docker-compose -f docker-compose.prod.yml exec backend bash
docker-compose -f docker-compose.prod.yml exec frontend sh

# Check container processes
docker-compose -f docker-compose.prod.yml exec backend ps aux
```

### 7.2 Network Debugging
```powershell
# Test internal connectivity
docker-compose -f docker-compose.prod.yml exec backend ping db
docker-compose -f docker-compose.prod.yml exec frontend ping backend

# Check port bindings
docker-compose -f docker-compose.prod.yml port nginx 80
```

### 7.3 Volume Debugging
```powershell
# List volumes
docker volume ls

# Inspect volume contents
docker-compose -f docker-compose.prod.yml exec backend ls -la /usr/src/app/staticfiles/
```

## Step 8: Production vs Development Differences

### Key Differences to Watch For:
1. **Debug Mode:** Disabled in production (DEBUG=False)
2. **Static Files:** Served by nginx instead of Django
3. **Database:** PostgreSQL instead of SQLite
4. **Caching:** Redis caching enabled
5. **Security:** HTTPS redirects and secure cookies (disabled for local testing)
6. **Error Handling:** Production error pages instead of debug traces

## Next Steps

After successfully running in production mode locally:
1. Test all application features
2. Verify WebSocket functionality
3. Check performance metrics
4. Test error scenarios
5. Validate security headers
6. Prepare for actual production deployment

## Quick Commands Reference

```powershell
# Start production environment
docker-compose -f docker-compose.prod.yml up -d

# Stop production environment
docker-compose -f docker-compose.prod.yml down

# View logs
docker-compose -f docker-compose.prod.yml logs -f

# Rebuild and restart
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

# Clean everything
docker-compose -f docker-compose.prod.yml down -v
docker system prune -f
```
