import React, { useState, useEffect, useRef } from 'react';
import { Avatar, Tooltip } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import styled, { keyframes } from 'styled-components';

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
`;

const CursorContainer = styled.div`
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  transition: all 0.1s ease-out;
  animation: ${fadeIn} 0.3s ease-out;
`;

const CursorPointer = styled.div`
  width: 0;
  height: 0;
  border-left: 8px solid ${props => props.color || '#1890ff'};
  border-right: 8px solid transparent;
  border-bottom: 12px solid ${props => props.color || '#1890ff'};
  transform: rotate(-45deg);
  position: relative;
`;

const Cursor<PERSON>abel = styled.div`
  position: absolute;
  top: 15px;
  left: 15px;
  background: ${props => props.color || '#1890ff'};
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: ${pulse} 2s infinite;
  
  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 8px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid ${props => props.color || '#1890ff'};
  }
`;

const SelectionHighlight = styled.div`
  position: absolute;
  border: 2px solid ${props => props.color || '#1890ff'};
  border-radius: 4px;
  background: ${props => props.color || '#1890ff'}20;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.2s ease-out;
`;

const UserColors = [
  '#1890ff', // Blue
  '#52c41a', // Green
  '#fa8c16', // Orange
  '#eb2f96', // Pink
  '#722ed1', // Purple
  '#13c2c2', // Cyan
  '#faad14', // Gold
  '#f5222d', // Red
];

const LiveCursors = ({
  collaborators = [],
  currentUser = null,
  containerRef = null,
  showSelections = true,
  showLabels = true,
  fadeTimeout = 3000
}) => {
  const [visibleCursors, setVisibleCursors] = useState(new Map());
  const [selections, setSelections] = useState(new Map());
  const timeoutRefs = useRef(new Map());

  // Mock data for demonstration
  const mockCollaborators = collaborators.length > 0 ? collaborators : [
    {
      id: 'user-1',
      name: 'John Doe',
      cursor: { x: 300, y: 150 },
      selection: { x: 280, y: 200, width: 120, height: 40 },
      lastUpdate: Date.now(),
      online: true
    },
    {
      id: 'user-2',
      name: 'Jane Smith',
      cursor: { x: 500, y: 300 },
      selection: null,
      lastUpdate: Date.now() - 1000,
      online: true
    }
  ];

  // Assign colors to users
  const getUserColor = (userId) => {
    const index = userId.charCodeAt(userId.length - 1) % UserColors.length;
    return UserColors[index];
  };

  // Update cursor positions
  useEffect(() => {
    const activeCursors = new Map();
    const activeSelections = new Map();

    mockCollaborators.forEach(user => {
      if (user.online && user.id !== currentUser?.id && user.cursor) {
        activeCursors.set(user.id, {
          ...user,
          color: getUserColor(user.id)
        });

        if (user.selection && showSelections) {
          activeSelections.set(user.id, {
            ...user.selection,
            color: getUserColor(user.id),
            userName: user.name
          });
        }

        // Set fade timeout
        if (timeoutRefs.current.has(user.id)) {
          clearTimeout(timeoutRefs.current.get(user.id));
        }

        const timeoutId = setTimeout(() => {
          setVisibleCursors(prev => {
            const newMap = new Map(prev);
            newMap.delete(user.id);
            return newMap;
          });
          setSelections(prev => {
            const newMap = new Map(prev);
            newMap.delete(user.id);
            return newMap;
          });
        }, fadeTimeout);

        timeoutRefs.current.set(user.id, timeoutId);
      }
    });

    setVisibleCursors(activeCursors);
    setSelections(activeSelections);

    return () => {
      timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutRefs.current.clear();
    };
  }, [collaborators, currentUser, showSelections, fadeTimeout]);

  // Calculate relative position if container is provided
  const getRelativePosition = (absolutePos) => {
    if (!containerRef?.current) return absolutePos;
    
    const containerRect = containerRef.current.getBoundingClientRect();
    return {
      x: absolutePos.x - containerRect.left,
      y: absolutePos.y - containerRect.top
    };
  };

  return (
    <>
      {/* Render selections */}
      {Array.from(selections.entries()).map(([userId, selection]) => (
        <SelectionHighlight
          key={`selection-${userId}`}
          color={selection.color}
          style={{
            left: selection.x,
            top: selection.y,
            width: selection.width,
            height: selection.height
          }}
        />
      ))}

      {/* Render cursors */}
      {Array.from(visibleCursors.entries()).map(([userId, user]) => {
        const position = getRelativePosition(user.cursor);
        
        return (
          <CursorContainer
            key={`cursor-${userId}`}
            style={{
              left: position.x,
              top: position.y
            }}
          >
            <CursorPointer color={user.color} />
            {showLabels && (
              <Tooltip title={`${user.name} is here`} placement="topLeft">
                <CursorLabel color={user.color}>
                  {user.name}
                </CursorLabel>
              </Tooltip>
            )}
          </CursorContainer>
        );
      })}
    </>
  );
};

// Hook for managing cursor tracking
export const useLiveCursors = (websocketConnection, currentUser) => {
  const [collaborators, setCollaborators] = useState([]);
  const [myPosition, setMyPosition] = useState({ x: 0, y: 0 });

  // Track mouse movement
  useEffect(() => {
    const handleMouseMove = (e) => {
      const newPosition = { x: e.clientX, y: e.clientY };
      setMyPosition(newPosition);
      
      // Send cursor position via WebSocket
      if (websocketConnection && websocketConnection.readyState === WebSocket.OPEN) {
        websocketConnection.send(JSON.stringify({
          type: 'cursor_move',
          userId: currentUser?.id,
          position: newPosition,
          timestamp: Date.now()
        }));
      }
    };

    const throttledMouseMove = throttle(handleMouseMove, 50); // Throttle to 20fps
    document.addEventListener('mousemove', throttledMouseMove);

    return () => {
      document.removeEventListener('mousemove', throttledMouseMove);
    };
  }, [websocketConnection, currentUser]);

  // Listen for WebSocket messages
  useEffect(() => {
    if (!websocketConnection) return;

    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'cursor_move' && data.userId !== currentUser?.id) {
          setCollaborators(prev => {
            const updated = prev.filter(user => user.id !== data.userId);
            return [...updated, {
              id: data.userId,
              name: data.userName || `User ${data.userId}`,
              cursor: data.position,
              lastUpdate: data.timestamp,
              online: true
            }];
          });
        }
        
        if (data.type === 'user_disconnect') {
          setCollaborators(prev => prev.filter(user => user.id !== data.userId));
        }
      } catch (error) {
        console.warn('Failed to parse WebSocket message:', error);
      }
    };

    websocketConnection.addEventListener('message', handleMessage);

    return () => {
      websocketConnection.removeEventListener('message', handleMessage);
    };
  }, [websocketConnection, currentUser]);

  return {
    collaborators,
    myPosition,
    setCollaborators
  };
};

// Utility function for throttling
const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

export default LiveCursors;
