import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, Space, Typo<PERSON>, Badge, Toolt<PERSON>, Al<PERSON>, Spin } from 'antd';
import {
  LayoutOutlined,
  BulbOutlined,
  CheckOutlined,
  ReloadOutlined,
  StarOutlined,
  ThunderboltOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Text, Title } = Typography;

const SuggestionContainer = styled.div`
  padding: 16px;
`;

const SuggestionCard = styled(Card)`
  margin-bottom: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
`;

const LayoutPreview = styled.div`
  width: 100%;
  height: 80px;
  background: #f5f5f5;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 0;
  position: relative;
  overflow: hidden;
`;

const LayoutElement = styled.div`
  background: ${props => props.color || '#1890ff'};
  border-radius: 2px;
  position: absolute;
  opacity: 0.7;
`;

const ScoreIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: #52c41a;
  font-weight: 500;
`;

const AILayoutSuggestions = ({
  components = [],
  currentLayout = null,
  onApplySuggestion = () => {},
  onRefresh = () => {},
  loading = false,
  enabled = true
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [appliedSuggestions, setAppliedSuggestions] = useState(new Set());
  const [isLoading, setIsLoading] = useState(false);

  // Mock AI suggestions based on current components
  const generateSuggestions = () => {
    const mockSuggestions = [
      {
        id: 'grid-layout',
        name: 'Grid Layout',
        description: 'Organize components in a responsive grid system',
        type: 'layout',
        score: 95,
        reasoning: 'Your components would benefit from a structured grid layout for better visual hierarchy',
        layout: {
          type: 'grid',
          columns: 3,
          gap: 16,
          responsive: true
        },
        preview: [
          { x: 5, y: 10, width: 25, height: 15, color: '#1890ff' },
          { x: 35, y: 10, width: 25, height: 15, color: '#52c41a' },
          { x: 65, y: 10, width: 25, height: 15, color: '#fa8c16' },
          { x: 5, y: 35, width: 25, height: 15, color: '#eb2f96' },
          { x: 35, y: 35, width: 25, height: 15, color: '#722ed1' },
          { x: 65, y: 35, width: 25, height: 15, color: '#13c2c2' }
        ]
      },
      {
        id: 'sidebar-layout',
        name: 'Sidebar Layout',
        description: 'Create a sidebar navigation with main content area',
        type: 'layout',
        score: 88,
        reasoning: 'Perfect for applications with navigation and content sections',
        layout: {
          type: 'sidebar',
          sidebarWidth: 250,
          position: 'left'
        },
        preview: [
          { x: 5, y: 10, width: 20, height: 60, color: '#1890ff' },
          { x: 30, y: 10, width: 65, height: 25, color: '#52c41a' },
          { x: 30, y: 40, width: 65, height: 30, color: '#fa8c16' }
        ]
      },
      {
        id: 'hero-layout',
        name: 'Hero Section Layout',
        description: 'Feature a prominent hero section with supporting content',
        type: 'layout',
        score: 82,
        reasoning: 'Great for landing pages and marketing content',
        layout: {
          type: 'hero',
          heroHeight: '50vh',
          contentSections: 3
        },
        preview: [
          { x: 5, y: 5, width: 90, height: 30, color: '#1890ff' },
          { x: 5, y: 40, width: 28, height: 25, color: '#52c41a' },
          { x: 36, y: 40, width: 28, height: 25, color: '#fa8c16' },
          { x: 67, y: 40, width: 28, height: 25, color: '#eb2f96' }
        ]
      },
      {
        id: 'dashboard-layout',
        name: 'Dashboard Layout',
        description: 'Organize data and controls in a dashboard format',
        type: 'layout',
        score: 90,
        reasoning: 'Ideal for data-heavy applications with multiple widgets',
        layout: {
          type: 'dashboard',
          widgets: true,
          responsive: true
        },
        preview: [
          { x: 5, y: 5, width: 42, height: 20, color: '#1890ff' },
          { x: 52, y: 5, width: 43, height: 20, color: '#52c41a' },
          { x: 5, y: 30, width: 28, height: 35, color: '#fa8c16' },
          { x: 38, y: 30, width: 28, height: 35, color: '#eb2f96' },
          { x: 71, y: 30, width: 24, height: 35, color: '#722ed1' }
        ]
      }
    ];

    // Filter suggestions based on component count and types
    const componentCount = components.length;
    let filteredSuggestions = mockSuggestions;

    if (componentCount < 3) {
      filteredSuggestions = mockSuggestions.filter(s => s.id !== 'dashboard-layout');
    }

    if (componentCount > 6) {
      filteredSuggestions = filteredSuggestions.map(s => ({
        ...s,
        score: s.id === 'dashboard-layout' ? s.score + 5 : s.score
      }));
    }

    return filteredSuggestions.sort((a, b) => b.score - a.score);
  };

  useEffect(() => {
    if (enabled && components.length > 0) {
      setIsLoading(true);
      // Simulate AI processing time
      setTimeout(() => {
        setSuggestions(generateSuggestions());
        setIsLoading(false);
      }, 1000);
    } else {
      setSuggestions([]);
    }
  }, [components, enabled]);

  const handleApplySuggestion = (suggestion) => {
    onApplySuggestion(suggestion);
    setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
  };

  const handleRefresh = () => {
    setIsLoading(true);
    onRefresh();
    setTimeout(() => {
      setSuggestions(generateSuggestions());
      setIsLoading(false);
    }, 800);
  };

  const renderLayoutPreview = (preview) => (
    <LayoutPreview>
      {preview.map((element, index) => (
        <LayoutElement
          key={index}
          color={element.color}
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: `${element.width}%`,
            height: `${element.height}%`
          }}
        />
      ))}
    </LayoutPreview>
  );

  if (!enabled) {
    return (
      <Alert
        message="AI Layout Suggestions Disabled"
        description="Enable AI features to get intelligent layout suggestions."
        type="info"
        showIcon
      />
    );
  }

  return (
    <SuggestionContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={4} style={{ margin: 0 }}>
          <BulbOutlined style={{ marginRight: 8, color: '#faad14' }} />
          AI Layout Suggestions
        </Title>
        <Tooltip title="Refresh suggestions">
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={isLoading || loading}
          />
        </Tooltip>
      </div>

      {components.length === 0 ? (
        <Alert
          message="No Components"
          description="Add some components to your app to get AI-powered layout suggestions."
          type="info"
          showIcon
          icon={<AppstoreOutlined />}
        />
      ) : isLoading || loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">Analyzing your components...</Text>
          </div>
        </div>
      ) : (
        <div>
          {suggestions.map((suggestion) => (
            <SuggestionCard
              key={suggestion.id}
              size="small"
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Space>
                    <LayoutOutlined />
                    <Text strong>{suggestion.name}</Text>
                    <ScoreIndicator>
                      <StarOutlined />
                      {suggestion.score}%
                    </ScoreIndicator>
                  </Space>
                  <Button
                    type={appliedSuggestions.has(suggestion.id) ? 'default' : 'primary'}
                    size="small"
                    icon={appliedSuggestions.has(suggestion.id) ? <CheckOutlined /> : <ThunderboltOutlined />}
                    onClick={() => handleApplySuggestion(suggestion)}
                    disabled={appliedSuggestions.has(suggestion.id)}
                  >
                    {appliedSuggestions.has(suggestion.id) ? 'Applied' : 'Apply'}
                  </Button>
                </div>
              }
            >
              <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: 8 }}>
                {suggestion.description}
              </Text>
              
              {renderLayoutPreview(suggestion.preview)}
              
              <div style={{ marginTop: 8 }}>
                <Text style={{ fontSize: '11px', fontStyle: 'italic', color: '#666' }}>
                  💡 {suggestion.reasoning}
                </Text>
              </div>
            </SuggestionCard>
          ))}

          {suggestions.length === 0 && (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              <BulbOutlined style={{ fontSize: '24px', marginBottom: 8 }} />
              <div>No layout suggestions available at the moment.</div>
              <div style={{ fontSize: '12px' }}>Try adding more components or changing your current layout.</div>
            </div>
          )}
        </div>
      )}
    </SuggestionContainer>
  );
};

export default AILayoutSuggestions;
