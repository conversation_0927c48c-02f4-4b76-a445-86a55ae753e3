/**
 * End-to-End Tests for Complete App Builder Workflow
 * 
 * Tests the entire user journey from template selection to code export
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import components
import IntegratedAppBuilder from '../../components/builder/IntegratedAppBuilder';
import TemplateManager from '../../components/templates/TemplateManager';
import ExportPreview from '../../components/export/ExportPreview';

// Import reducers
import appReducer from '../../store/slices/appSlice';
import uiReducer from '../../store/slices/uiSlice';
import templateReducer from '../../store/slices/templateSlice';

// Mock API calls
jest.mock('../../services/api', () => ({
  templateService: {
    getTemplates: jest.fn(() => Promise.resolve({
      data: [
        {
          id: 1,
          name: '[Default] Hello World Starter',
          description: 'A simple starter template',
          app_category: 'other',
          components: {
            pages: [{
              name: 'home',
              components: [
                {
                  id: 'header-1',
                  type: 'header',
                  props: { title: 'Hello World!' }
                }
              ]
            }]
          },
          is_public: true
        }
      ]
    })),
    loadTemplate: jest.fn(() => Promise.resolve({ data: { success: true } })),
    saveTemplate: jest.fn(() => Promise.resolve({ data: { id: 2 } }))
  },
  exportService: {
    exportApp: jest.fn(() => Promise.resolve({
      data: {
        code: 'import React from "react";\n\nfunction App() {\n  return <div>Hello World!</div>;\n}\n\nexport default App;',
        format: 'react',
        files: {
          'App.jsx': 'import React from "react";\n\nfunction App() {\n  return <div>Hello World!</div>;\n}\n\nexport default App;',
          'package.json': '{"name": "my-app", "version": "1.0.0"}'
        }
      }
    }))
  }
}));

// Mock WebSocket
const mockWebSocket = {
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1
};

global.WebSocket = jest.fn(() => mockWebSocket);

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(() => Promise.resolve())
  }
});

// Mock file download
global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
global.URL.revokeObjectURL = jest.fn();

// Test store setup
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: appReducer,
      ui: uiReducer,
      templates: templateReducer
    },
    preloadedState: {
      app: {
        components: [],
        selectedComponent: null,
        layouts: [],
        styles: {},
        isDirty: false,
        ...initialState.app
      },
      ui: {
        sidebarOpen: true,
        currentView: 'builder',
        previewMode: false,
        theme: 'light',
        notifications: [],
        ...initialState.ui
      },
      templates: {
        templates: [],
        loading: false,
        error: null,
        ...initialState.templates
      }
    }
  });
};

// Test wrapper component
const TestWrapper = ({ children, store }) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('Complete App Builder Workflow E2E Tests', () => {
  let store;
  let user;

  beforeEach(() => {
    store = createTestStore();
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('1. Template Selection and Loading', () => {
    test('loads default Hello World template successfully', async () => {
      render(
        <TestWrapper store={store}>
          <TemplateManager />
        </TestWrapper>
      );

      // Open template manager
      const openButton = screen.getByText(/template/i);
      await user.click(openButton);

      // Wait for templates to load
      await waitFor(() => {
        expect(screen.getByText('[Default] Hello World Starter')).toBeInTheDocument();
      });

      // Select the Hello World template
      const helloWorldTemplate = screen.getByText('[Default] Hello World Starter');
      await user.click(helloWorldTemplate);

      // Click load button
      const loadButton = screen.getByText(/load/i);
      await user.click(loadButton);

      // Verify template is loaded
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.components.length).toBeGreaterThan(0);
      });
    });

    test('displays template preview correctly', async () => {
      render(
        <TestWrapper store={store}>
          <TemplateManager />
        </TestWrapper>
      );

      // Open template manager
      const openButton = screen.getByText(/template/i);
      await user.click(openButton);

      // Wait for template and click preview
      await waitFor(() => {
        expect(screen.getByText('[Default] Hello World Starter')).toBeInTheDocument();
      });

      const previewButton = screen.getByLabelText(/preview/i);
      await user.click(previewButton);

      // Verify preview modal opens
      expect(screen.getByText(/preview/i)).toBeInTheDocument();
    });
  });

  describe('2. Component Addition and Management', () => {
    test('adds new components via drag and drop simulation', async () => {
      // Pre-load a template
      store = createTestStore({
        app: {
          components: [
            {
              id: 'header-1',
              type: 'header',
              props: { title: 'Hello World!' },
              position: { x: 0, y: 0, width: 12, height: 3 }
            }
          ]
        }
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Simulate adding a button component
      const addButtonBtn = screen.getByText(/add.*button/i);
      await user.click(addButtonBtn);

      // Verify component is added
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.components.length).toBe(2);
        expect(state.app.components.some(c => c.type === 'button')).toBe(true);
      });
    });

    test('selects and deselects components', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'header-1',
              type: 'header',
              props: { title: 'Hello World!' }
            }
          ]
        }
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Click on a component to select it
      const headerComponent = screen.getByText('Hello World!');
      await user.click(headerComponent);

      // Verify component is selected
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.selectedComponent).toBe('header-1');
      });

      // Click elsewhere to deselect
      const canvas = screen.getByTestId('app-canvas');
      await user.click(canvas);

      // Verify component is deselected
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.selectedComponent).toBeNull();
      });
    });
  });

  describe('3. Component Configuration and Property Editing', () => {
    test('edits component properties successfully', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'button-1',
              type: 'button',
              props: { text: 'Click me', backgroundColor: '#1890ff' }
            }
          ],
          selectedComponent: 'button-1'
        }
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Find and edit the text property
      const textInput = screen.getByDisplayValue('Click me');
      await user.clear(textInput);
      await user.type(textInput, 'Updated Button Text');

      // Verify property is updated
      await waitFor(() => {
        const state = store.getState();
        const button = state.app.components.find(c => c.id === 'button-1');
        expect(button.props.text).toBe('Updated Button Text');
      });
    });

    test('changes component styling properties', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'button-1',
              type: 'button',
              props: { text: 'Button', backgroundColor: '#1890ff' }
            }
          ],
          selectedComponent: 'button-1'
        }
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Find color picker and change color
      const colorInput = screen.getByDisplayValue('#1890ff');
      await user.clear(colorInput);
      await user.type(colorInput, '#52c41a');

      // Verify color is updated
      await waitFor(() => {
        const state = store.getState();
        const button = state.app.components.find(c => c.id === 'button-1');
        expect(button.props.backgroundColor).toBe('#52c41a');
      });
    });
  });

  describe('4. Layout Management and Positioning', () => {
    test('repositions components in layout', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'text-1',
              type: 'text',
              props: { content: 'Test text' },
              position: { x: 0, y: 0, width: 6, height: 2 }
            }
          ],
          selectedComponent: 'text-1'
        }
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Simulate drag and drop to new position
      const component = screen.getByText('Test text');

      // Mock drag events
      fireEvent.dragStart(component);
      fireEvent.dragOver(component);
      fireEvent.drop(component);

      // Verify position change (this would be handled by the drag and drop logic)
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.components[0].position).toBeDefined();
      });
    });

    test('resizes components', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'container-1',
              type: 'container',
              props: {},
              position: { x: 0, y: 0, width: 6, height: 4 }
            }
          ],
          selectedComponent: 'container-1'
        }
      });

      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Find resize handle and simulate resize
      const resizeHandle = screen.getByTestId('resize-handle');

      fireEvent.mouseDown(resizeHandle);
      fireEvent.mouseMove(resizeHandle, { clientX: 100, clientY: 100 });
      fireEvent.mouseUp(resizeHandle);

      // Verify size change
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.components[0].position.width).toBeDefined();
      });
    });
  });

  describe('5. Theme Application and Styling', () => {
    test('applies different themes to the app', async () => {
      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Open theme manager
      const themeButton = screen.getByText(/theme/i);
      await user.click(themeButton);

      // Select dark theme
      const darkThemeOption = screen.getByText(/dark/i);
      await user.click(darkThemeOption);

      // Verify theme is applied
      await waitFor(() => {
        const state = store.getState();
        expect(state.ui.theme).toBe('dark');
      });
    });

    test('customizes theme colors', async () => {
      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // Open theme customization
      const customizeButton = screen.getByText(/customize/i);
      await user.click(customizeButton);

      // Change primary color
      const primaryColorInput = screen.getByLabelText(/primary color/i);
      await user.clear(primaryColorInput);
      await user.type(primaryColorInput, '#722ed1');

      // Verify color is updated
      await waitFor(() => {
        const state = store.getState();
        expect(state.app.styles.primaryColor).toBe('#722ed1');
      });
    });
  });

  describe('6. Code Generation and Export', () => {
    test('generates React code successfully', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'header-1',
              type: 'header',
              props: { title: 'Hello World!' }
            },
            {
              id: 'button-1',
              type: 'button',
              props: { text: 'Click me' }
            }
          ]
        }
      });

      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={store.getState().app.components}
            format="react"
          />
        </TestWrapper>
      );

      // Wait for code generation
      await waitFor(() => {
        expect(screen.getByText(/import React/)).toBeInTheDocument();
      });

      // Verify React code structure
      expect(screen.getByText(/function App/)).toBeInTheDocument();
      expect(screen.getByText(/export default App/)).toBeInTheDocument();
    });

    test('generates Vue code successfully', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'text-1',
              type: 'text',
              props: { content: 'Hello Vue!' }
            }
          ]
        }
      });

      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={store.getState().app.components}
            format="vue"
          />
        </TestWrapper>
      );

      // Wait for Vue code generation
      await waitFor(() => {
        expect(screen.getByText(/<template>/)).toBeInTheDocument();
      });

      // Verify Vue code structure
      expect(screen.getByText(/<script>/)).toBeInTheDocument();
      expect(screen.getByText(/<style>/)).toBeInTheDocument();
    });

    test('generates HTML code successfully', async () => {
      store = createTestStore({
        app: {
          components: [
            {
              id: 'header-1',
              type: 'header',
              props: { title: 'Static Site' }
            }
          ]
        }
      });

      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={store.getState().app.components}
            format="html"
          />
        </TestWrapper>
      );

      // Wait for HTML code generation
      await waitFor(() => {
        expect(screen.getByText(/<!DOCTYPE html>/)).toBeInTheDocument();
      });

      // Verify HTML structure
      expect(screen.getByText(/<html>/)).toBeInTheDocument();
      expect(screen.getByText(/<body>/)).toBeInTheDocument();
    });
  });

  describe('7. Code Preview and Syntax Highlighting', () => {
    test('displays syntax highlighted code correctly', async () => {
      const mockCode = 'import React from "react";\n\nfunction App() {\n  return <div>Hello</div>;\n}';

      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={[]}
            format="react"
          />
        </TestWrapper>
      );

      // Wait for syntax highlighter to render
      await waitFor(() => {
        const codeElement = screen.getByTestId('syntax-highlighter');
        expect(codeElement).toBeInTheDocument();
      });

      // Verify syntax highlighting is applied
      const highlightedCode = screen.getByTestId('syntax-highlighter');
      expect(highlightedCode).toHaveClass('language-jsx');
    });

    test('switches between different file tabs', async () => {
      const mockFiles = {
        'App.jsx': 'import React from "react";',
        'package.json': '{"name": "my-app"}',
        'README.md': '# My App'
      };

      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={[]}
            format="react"
            files={mockFiles}
          />
        </TestWrapper>
      );

      // Click on package.json tab
      const packageTab = screen.getByText('package.json');
      await user.click(packageTab);

      // Verify content switches
      await waitFor(() => {
        expect(screen.getByText(/"name": "my-app"/)).toBeInTheDocument();
      });

      // Click on README tab
      const readmeTab = screen.getByText('README.md');
      await user.click(readmeTab);

      // Verify README content
      await waitFor(() => {
        expect(screen.getByText(/# My App/)).toBeInTheDocument();
      });
    });
  });

  describe('8. Download and Copy Functionality', () => {
    test('copies code to clipboard successfully', async () => {
      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={[{ id: '1', type: 'text', props: { content: 'Test' } }]}
            format="react"
          />
        </TestWrapper>
      );

      // Click copy button
      const copyButton = screen.getByLabelText(/copy/i);
      await user.click(copyButton);

      // Verify clipboard API was called
      expect(navigator.clipboard.writeText).toHaveBeenCalled();

      // Verify success message
      await waitFor(() => {
        expect(screen.getByText(/copied/i)).toBeInTheDocument();
      });
    });

    test('downloads files successfully', async () => {
      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={[{ id: '1', type: 'button', props: { text: 'Download Test' } }]}
            format="react"
          />
        </TestWrapper>
      );

      // Click download button
      const downloadButton = screen.getByLabelText(/download/i);
      await user.click(downloadButton);

      // Verify download was initiated
      expect(global.URL.createObjectURL).toHaveBeenCalled();
    });
  });

  describe('9. Error Handling and Edge Cases', () => {
    test('handles empty component list gracefully', async () => {
      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={[]}
            format="react"
          />
        </TestWrapper>
      );

      // Should still generate basic app structure
      await waitFor(() => {
        expect(screen.getByText(/import React/)).toBeInTheDocument();
      });
    });

    test('handles invalid component data gracefully', async () => {
      const invalidComponents = [
        { id: '1', type: 'invalid-type', props: null }
      ];

      render(
        <TestWrapper store={store}>
          <ExportPreview
            components={invalidComponents}
            format="react"
          />
        </TestWrapper>
      );

      // Should not crash and should show some fallback
      await waitFor(() => {
        expect(screen.getByText(/export default/)).toBeInTheDocument();
      });
    });

    test('handles network errors during template loading', async () => {
      // Mock API to reject
      const mockTemplateService = require('../../services/api').templateService;
      mockTemplateService.getTemplates.mockRejectedValueOnce(new Error('Network error'));

      render(
        <TestWrapper store={store}>
          <TemplateManager />
        </TestWrapper>
      );

      // Open template manager
      const openButton = screen.getByText(/template/i);
      await user.click(openButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
    });
  });

  describe('10. Complete End-to-End Workflow', () => {
    test('complete workflow from template to export', async () => {
      // Start with template selection
      render(
        <TestWrapper store={store}>
          <IntegratedAppBuilder />
        </TestWrapper>
      );

      // 1. Load template
      const templateButton = screen.getByText(/template/i);
      await user.click(templateButton);

      await waitFor(() => {
        expect(screen.getByText('[Default] Hello World Starter')).toBeInTheDocument();
      });

      const loadButton = screen.getByText(/load/i);
      await user.click(loadButton);

      // 2. Add a component
      await waitFor(() => {
        const addButton = screen.getByText(/add.*button/i);
        user.click(addButton);
      });

      // 3. Edit component properties
      await waitFor(() => {
        const textInput = screen.getByDisplayValue(/click me/i);
        user.clear(textInput);
        user.type(textInput, 'My Custom Button');
      });

      // 4. Export the app
      const exportButton = screen.getByText(/export/i);
      await user.click(exportButton);

      // 5. Verify export
      await waitFor(() => {
        expect(screen.getByText(/import React/)).toBeInTheDocument();
        expect(screen.getByText(/My Custom Button/)).toBeInTheDocument();
      });

      // 6. Download the code
      const downloadButton = screen.getByLabelText(/download/i);
      await user.click(downloadButton);

      expect(global.URL.createObjectURL).toHaveBeenCalled();
    });
  });
});
