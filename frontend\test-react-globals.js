/**
 * Test script to verify React globals are working
 * This script can be run with Node.js to test the React setup
 */

const puppeteer = require('puppeteer');

async function testReactGlobals() {
  console.log('🚀 Starting React Globals Test...');

  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture console output
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`[${msg.type()}] ${msg.text()}`);
    });

    // Capture errors
    const errors = [];
    page.on('error', error => {
      errors.push(error.message);
    });

    page.on('pageerror', error => {
      errors.push(error.message);
    });

    // Navigate to the app
    console.log('📱 Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for React to load
    console.log('⏳ Waiting for React to load...');

    // Wait for the app to be loaded
    try {
      await page.waitForFunction(
        () => window.__APP_LOADED__ === true || window.__REACT_LOADED__ === true,
        { timeout: 10000 }
      );
      console.log('✅ App loading flag detected');
    } catch (error) {
      console.log('⚠️ App loading flag timeout, continuing anyway...');
    }

    // Additional wait for React globals
    try {
      await page.waitForFunction(
        () => typeof window.React !== 'undefined',
        { timeout: 5000 }
      );
      console.log('✅ React global detected');
    } catch (error) {
      console.log('⚠️ React global timeout, continuing anyway...');
    }

    await page.waitForTimeout(2000);

    // Test React global availability
    const reactTest = await page.evaluate(() => {
      return {
        reactAvailable: typeof window.React !== 'undefined',
        reactVersion: window.React?.version || null,
        reactDOMAvailable: typeof window.ReactDOM !== 'undefined',
        reactDOMKeys: window.ReactDOM ? Object.keys(window.ReactDOM) : [],
        rootElement: !!document.getElementById('root'),
        rootHasContent: document.getElementById('root')?.innerHTML?.length > 0,
        // Additional debugging info
        appLoaded: window.__APP_LOADED__,
        reactLoaded: window.__REACT_LOADED__,
        reactVersionGlobal: window.__REACT_VERSION__,
        windowKeys: Object.keys(window).filter(k => k.toLowerCase().includes('react')),
        globalReactType: typeof window.React,
        globalReactDOMType: typeof window.ReactDOM
      };
    });

    // Test bundle loading
    const bundleTest = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script[src]'));
      const mainScripts = scripts.filter(script =>
        script.src.includes('main') ||
        script.src.includes('bundle') ||
        script.src.includes('chunk')
      );

      return {
        totalScripts: scripts.length,
        mainScripts: mainScripts.length,
        mainScriptUrls: mainScripts.map(s => s.src.split('/').pop())
      };
    });

    // Display results
    console.log('\n📊 Test Results:');
    console.log('================');

    console.log(`✅ React Available: ${reactTest.reactAvailable ? '✅ YES' : '❌ NO'}`);
    if (reactTest.reactAvailable) {
      console.log(`   Version: ${reactTest.reactVersion || 'Unknown'}`);
    }

    console.log(`✅ ReactDOM Available: ${reactTest.reactDOMAvailable ? '✅ YES' : '❌ NO'}`);
    if (reactTest.reactDOMAvailable) {
      console.log(`   Keys: ${reactTest.reactDOMKeys.join(', ')}`);
    }

    // Debug info
    console.log('\n🔍 Debug Information:');
    console.log(`   App Loaded: ${reactTest.appLoaded}`);
    console.log(`   React Loaded Flag: ${reactTest.reactLoaded}`);
    console.log(`   React Version Global: ${reactTest.reactVersionGlobal}`);
    console.log(`   Global React Type: ${reactTest.globalReactType}`);
    console.log(`   Global ReactDOM Type: ${reactTest.globalReactDOMType}`);
    console.log(`   React-related window keys: ${reactTest.windowKeys.join(', ')}`);
    console.log('');

    console.log(`✅ Root Element: ${reactTest.rootElement ? '✅ YES' : '❌ NO'}`);
    console.log(`✅ Root Has Content: ${reactTest.rootHasContent ? '✅ YES' : '❌ NO'}`);

    console.log(`✅ Total Scripts: ${bundleTest.totalScripts}`);
    console.log(`✅ Main Scripts: ${bundleTest.mainScripts}`);
    if (bundleTest.mainScriptUrls.length > 0) {
      console.log(`   Files: ${bundleTest.mainScriptUrls.join(', ')}`);
    }

    // Show console messages
    if (consoleMessages.length > 0) {
      console.log('\n📝 Console Messages:');
      consoleMessages.slice(-10).forEach(msg => console.log(`   ${msg}`));
    }

    // Show errors
    if (errors.length > 0) {
      console.log('\n❌ Errors:');
      errors.forEach(error => console.log(`   ${error}`));
    }

    // Overall status
    const allPassed = reactTest.reactAvailable &&
      reactTest.reactDOMAvailable &&
      reactTest.rootElement &&
      bundleTest.mainScripts > 0;

    console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

    return allPassed;

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testReactGlobals()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = testReactGlobals;
