// Script to restore the original React app from backup
const fs = require('fs');
const path = require('path');

const indexPath = path.join(__dirname, 'src', 'index.js');
const backupPath = path.join(__dirname, 'src', 'index.backup.js');

try {
  // Check if backup exists
  if (!fs.existsSync(backupPath)) {
    console.error('❌ No backup file found at index.backup.js');
    console.log('Cannot restore original app without backup');
    process.exit(1);
  }

  // Restore from backup
  console.log('🔄 Restoring original React app from backup...');
  fs.copyFileSync(backupPath, indexPath);
  console.log('✅ Original React app restored');
  
  console.log('\n📋 Next steps:');
  console.log('1. The webpack dev server should automatically reload');
  console.log('2. Check http://localhost:3000 to see the original app');
  
} catch (error) {
  console.error('❌ Error restoring original app:', error.message);
  process.exit(1);
}
