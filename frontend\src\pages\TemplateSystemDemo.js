import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout,
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Spin,
  Alert,
  Badge,
  Tag,
  Divider,
  Modal,
  Input,
  Select,
  Upload,
  message,
  Collapse,
  Tree,
  Table,
  Tooltip,
  Progress,
  Descriptions,
  Empty,
  Switch,
  Radio,
  Drawer,
  List,
  Avatar,
  Statistic,
  Timeline
} from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  AppstoreAddOutlined,
  EyeOutlined,
  DownloadOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  CodeOutlined,
  SettingOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  StarOutlined,
  HeartOutlined,
  ShareAltOutlined,
  FolderOutlined,
  TagsOutlined,
  ClockCircleOutlined,
  UserOutlined,
  GlobalOutlined,
  LockOutlined,
  UnlockOutlined,
  PlayCircleOutlined,
  <PERSON>use<PERSON><PERSON>cleOutlined,
  SyncOutlined,
  <PERSON>Outlined,
  <PERSON>pi<PERSON>utlined,
  <PERSON>U<PERSON>loadOutlined,
  CloudDownloadOutlined,
  <PERSON>esOutlined,
  NodeIndexOutlined,
  ApartmentOutlined,
  ProjectOutlined,
  BuildOutlined,
  RocketOutlined,
  ExperimentOutlined,
  ToolOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import axios from 'axios';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const { Content, Sider } = Layout;
const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;
const { Search } = Input;
const { DirectoryTree } = Tree;

// Styled Components
const DemoContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
`;

const DemoHeader = styled.div`
  text-align: center;
  margin-bottom: 32px;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
`;

const TemplateCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  }
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
  }
`;

const MetadataContainer = styled.div`
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
`;

const JsonViewer = styled.div`
  background: #1e1e1e;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  max-height: 400px;
  overflow-y: auto;
  
  pre {
    margin: 0;
    color: #d4d4d4;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 24px 0;
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 24px 0;
`;

const HierarchyVisualization = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin: 24px 0;
`;

const InteractiveDemo = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin: 24px 0;
  min-height: 400px;
`;

const PreviewContainer = styled.div`
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  background: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const ComponentPreview = styled.div`
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`;

/**
 * Template System Demonstration Page
 * 
 * A comprehensive demonstration interface showcasing the hierarchical template system
 * functionality including LayoutTemplate and AppTemplate models, their relationships,
 * and integration with the App Builder Enhanced application.
 */
const TemplateSystemDemo = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState({
    layout: [],
    app: [],
    component: []
  });
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [previewMode, setPreviewMode] = useState('visual');
  const [showHierarchy, setShowHierarchy] = useState(true);
  const [demoMode, setDemoMode] = useState('browse');
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [systemStats, setSystemStats] = useState({});
  const [error, setError] = useState(null);

  // Template categories and types
  const templateCategories = {
    layout: ['grid', 'flex', 'sidebar', 'header', 'footer', 'navigation', 'landing', 'dashboard'],
    app: ['business', 'ecommerce', 'portfolio', 'dashboard', 'landing', 'blog', 'social', 'education', 'healthcare', 'finance', 'other'],
    component: ['button', 'input', 'card', 'modal', 'form', 'chart', 'table', 'navigation', 'media', 'layout']
  };

  // Load templates from API
  const loadTemplates = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const [layoutResponse, appResponse, componentResponse] = await Promise.all([
        axios.get('/api/layout-templates/'),
        axios.get('/api/app-templates/'),
        axios.get('/api/component-templates/')
      ]);

      setTemplates({
        layout: layoutResponse.data.results || layoutResponse.data || [],
        app: appResponse.data.results || appResponse.data || [],
        component: componentResponse.data.results || componentResponse.data || []
      });

      // Calculate system statistics
      const totalTemplates =
        (layoutResponse.data.results?.length || 0) +
        (appResponse.data.results?.length || 0) +
        (componentResponse.data.results?.length || 0);

      setSystemStats({
        totalTemplates,
        layoutTemplates: layoutResponse.data.results?.length || 0,
        appTemplates: appResponse.data.results?.length || 0,
        componentTemplates: componentResponse.data.results?.length || 0,
        publicTemplates: [
          ...(layoutResponse.data.results || []),
          ...(appResponse.data.results || []),
          ...(componentResponse.data.results || [])
        ].filter(t => t.is_public).length
      });

    } catch (err) {
      console.error('Error loading templates:', err);
      setError('Failed to load templates. Please check your connection and try again.');
      message.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  }, []);

  // Load featured templates
  const loadFeaturedTemplates = useCallback(async () => {
    try {
      const response = await axios.get('/api/featured-templates/');
      return response.data;
    } catch (err) {
      console.error('Error loading featured templates:', err);
      return { components: [], layouts: [], apps: [] };
    }
  }, []);

  // Search templates
  const searchTemplates = useCallback(async (query, type = '', category = '') => {
    try {
      const params = new URLSearchParams();
      if (query) params.append('q', query);
      if (type) params.append('type', type);
      if (category) params.append('category', category);

      const response = await axios.get(`/api/template-search/?${params.toString()}`);
      return response.data;
    } catch (err) {
      console.error('Error searching templates:', err);
      return { components: [], layouts: [], apps: [] };
    }
  }, []);

  // Test template functionality
  const testTemplateFunction = useCallback(async (functionName, templateId = null) => {
    setTestResults(prev => ({ ...prev, [functionName]: { loading: true } }));

    try {
      let result;

      switch (functionName) {
        case 'load':
          if (templateId) {
            const template = [...templates.layout, ...templates.app, ...templates.component]
              .find(t => t.id === templateId);
            result = { success: true, data: template };
          }
          break;

        case 'search':
          result = await searchTemplates(searchTerm, filterType, filterCategory);
          break;

        case 'featured':
          result = await loadFeaturedTemplates();
          break;

        case 'categories':
          const response = await axios.get('/api/template-categories/');
          result = response.data;
          break;

        default:
          result = { success: false, error: 'Unknown function' };
      }

      setTestResults(prev => ({
        ...prev,
        [functionName]: {
          loading: false,
          success: true,
          data: result,
          timestamp: new Date().toISOString()
        }
      }));

      message.success(`${functionName} test completed successfully`);

    } catch (err) {
      setTestResults(prev => ({
        ...prev,
        [functionName]: {
          loading: false,
          success: false,
          error: err.message,
          timestamp: new Date().toISOString()
        }
      }));

      message.error(`${functionName} test failed: ${err.message}`);
    }
  }, [templates, searchTerm, filterType, filterCategory, searchTemplates, loadFeaturedTemplates]);

  // Initialize demo
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Filter templates based on search and filters
  const filteredTemplates = React.useMemo(() => {
    const filterTemplate = (template) => {
      const matchesSearch = !searchTerm ||
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = filterCategory === 'all' ||
        template.layout_type === filterCategory ||
        template.app_category === filterCategory ||
        template.component_type === filterCategory;

      return matchesSearch && matchesCategory;
    };

    return {
      layout: templates.layout.filter(filterTemplate),
      app: templates.app.filter(filterTemplate),
      component: templates.component.filter(filterTemplate)
    };
  }, [templates, searchTerm, filterCategory]);

  // Render template preview
  const renderTemplatePreview = (template, type) => {
    if (!template) return null;

    const getTemplateIcon = (type) => {
      switch (type) {
        case 'layout': return <LayoutOutlined />;
        case 'app': return <ProjectOutlined />;
        case 'component': return <AppstoreAddOutlined />;
        default: return <AppstoreOutlined />;
      }
    };

    const getTemplateColor = (type) => {
      switch (type) {
        case 'layout': return '#52c41a';
        case 'app': return '#1890ff';
        case 'component': return '#722ed1';
        default: return '#666';
      }
    };

    return (
      <TemplateCard
        title={
          <Space>
            {getTemplateIcon(type)}
            <span>{template.name}</span>
            <Tag color={getTemplateColor(type)}>{type}</Tag>
            {template.is_public && <Tag color="green">Public</Tag>}
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="Preview Template">
              <Button
                icon={<EyeOutlined />}
                onClick={() => setSelectedTemplate({ ...template, type })}
              />
            </Tooltip>
            <Tooltip title="Test Load">
              <Button
                icon={<PlayCircleOutlined />}
                onClick={() => testTemplateFunction('load', template.id)}
              />
            </Tooltip>
            <Tooltip title="Export Template">
              <Button
                icon={<DownloadOutlined />}
                onClick={() => {
                  const dataStr = JSON.stringify(template, null, 2);
                  const dataBlob = new Blob([dataStr], { type: 'application/json' });
                  const url = URL.createObjectURL(dataBlob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `${template.name.replace(/\s+/g, '_')}_template.json`;
                  link.click();
                  URL.revokeObjectURL(url);
                }}
              />
            </Tooltip>
          </Space>
        }
      >
        <Descriptions size="small" column={1}>
          <Descriptions.Item label="Description">
            {template.description || 'No description available'}
          </Descriptions.Item>
          <Descriptions.Item label="Category">
            <Tag>{template.layout_type || template.app_category || template.component_type || 'Unknown'}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Created">
            <Text type="secondary">
              <ClockCircleOutlined style={{ marginRight: 4 }} />
              {new Date(template.created_at).toLocaleDateString()}
            </Text>
          </Descriptions.Item>
          <Descriptions.Item label="Components">
            <Badge count={
              template.components ?
                (Array.isArray(template.components) ? template.components.length :
                  typeof template.components === 'object' ? Object.keys(template.components).length : 0) : 0
            } />
          </Descriptions.Item>
        </Descriptions>

        {previewMode === 'json' && (
          <JsonViewer>
            <SyntaxHighlighter
              language="json"
              style={vscDarkPlus}
              customStyle={{ background: 'transparent', padding: 0 }}
            >
              {JSON.stringify(template.components || {}, null, 2)}
            </SyntaxHighlighter>
          </JsonViewer>
        )}

        {previewMode === 'visual' && (
          <PreviewContainer>
            <AppstoreAddOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
            <Text type="secondary">Visual preview would render here</Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Component structure: {JSON.stringify(template.components || {}).length} characters
            </Text>
          </PreviewContainer>
        )}
      </TemplateCard>
    );
  };

  return (
    <DemoContainer>
      <DemoHeader>
        <Title level={1} style={{ color: '#1890ff', marginBottom: 16 }}>
          <DatabaseOutlined style={{ marginRight: 16 }} />
          Template System Demonstration
        </Title>
        <Paragraph style={{ fontSize: 18, color: '#666', maxWidth: 800, margin: '0 auto' }}>
          Comprehensive demonstration of the hierarchical template system featuring LayoutTemplate and AppTemplate models,
          their relationships, and integration with the App Builder Enhanced application.
        </Paragraph>

        <Space size="large" style={{ marginTop: 24 }}>
          <Statistic
            title="Total Templates"
            value={systemStats.totalTemplates || 0}
            prefix={<AppstoreOutlined />}
          />
          <Statistic
            title="Layout Templates"
            value={systemStats.layoutTemplates || 0}
            prefix={<LayoutOutlined />}
          />
          <Statistic
            title="App Templates"
            value={systemStats.appTemplates || 0}
            prefix={<ProjectOutlined />}
          />
          <Statistic
            title="Component Templates"
            value={systemStats.componentTemplates || 0}
            prefix={<AppstoreAddOutlined />}
          />
        </Space>
      </DemoHeader>

      {error && (
        <Alert
          message="Error Loading Templates"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" onClick={loadTemplates}>
              <ReloadOutlined /> Retry
            </Button>
          }
        />
      )}

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={6}>
          <Card title="Navigation & Controls" style={{ position: 'sticky', top: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div>
                <Text strong>Demo Mode:</Text>
                <Radio.Group
                  value={demoMode}
                  onChange={(e) => setDemoMode(e.target.value)}
                  style={{ marginTop: 8, width: '100%' }}
                >
                  <Radio.Button value="browse">Browse</Radio.Button>
                  <Radio.Button value="test">Test</Radio.Button>
                  <Radio.Button value="hierarchy">Hierarchy</Radio.Button>
                </Radio.Group>
              </div>

              <div>
                <Text strong>Search Templates:</Text>
                <Search
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ marginTop: 8 }}
                />
              </div>

              <div>
                <Text strong>Filter by Category:</Text>
                <Select
                  value={filterCategory}
                  onChange={setFilterCategory}
                  style={{ width: '100%', marginTop: 8 }}
                >
                  <Option value="all">All Categories</Option>
                  {Object.entries(templateCategories).map(([type, categories]) =>
                    categories.map(cat => (
                      <Option key={`${type}-${cat}`} value={cat}>
                        {cat.charAt(0).toUpperCase() + cat.slice(1)} ({type})
                      </Option>
                    ))
                  )}
                </Select>
              </div>

              <div>
                <Text strong>Filter by Type:</Text>
                <Select
                  value={filterType}
                  onChange={setFilterType}
                  style={{ width: '100%', marginTop: 8 }}
                >
                  <Option value="all">All Types</Option>
                  <Option value="layout">Layout Templates</Option>
                  <Option value="app">App Templates</Option>
                  <Option value="component">Component Templates</Option>
                </Select>
              </div>

              <Divider />

              <div>
                <Text strong>Quick Actions:</Text>
                <Space direction="vertical" style={{ width: '100%', marginTop: 8 }}>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={loadTemplates}
                    loading={loading}
                    block
                  >
                    Refresh Templates
                  </Button>
                  <Button
                    icon={<UploadOutlined />}
                    onClick={() => setImportModalVisible(true)}
                    block
                  >
                    Import Template
                  </Button>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={() => setExportModalVisible(true)}
                    block
                  >
                    Export Templates
                  </Button>
                </Space>
              </div>

              <Divider />

              <div>
                <Text strong>View Options:</Text>
                <Space direction="vertical" style={{ width: '100%', marginTop: 8 }}>
                  <div>
                    <Text>Preview Mode:</Text>
                    <Radio.Group
                      value={previewMode}
                      onChange={(e) => setPreviewMode(e.target.value)}
                      size="small"
                      style={{ marginTop: 4 }}
                    >
                      <Radio.Button value="visual">Visual</Radio.Button>
                      <Radio.Button value="code">Code</Radio.Button>
                      <Radio.Button value="json">JSON</Radio.Button>
                    </Radio.Group>
                  </div>
                  <div>
                    <Switch
                      checked={showHierarchy}
                      onChange={setShowHierarchy}
                      checkedChildren="Hierarchy"
                      unCheckedChildren="List"
                    />
                  </div>
                </Space>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={18}>
          <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
            <TabPane
              tab={
                <span>
                  <InfoCircleOutlined />
                  System Overview
                </span>
              }
              key="overview"
            >
              <Row gutter={[24, 24]}>
                <Col span={24}>
                  <Card title="Template System Architecture" style={{ marginBottom: 24 }}>
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={8}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <LayoutOutlined style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }} />
                          <Title level={4}>Layout Templates</Title>
                          <Paragraph>
                            Define reusable layout structures including grid, flex, sidebar, and navigation layouts.
                            Store component references and configurations in JSONField.
                          </Paragraph>
                          <Tag color="green">Grid • Flex • Sidebar</Tag>
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <ProjectOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                          <Title level={4}>App Templates</Title>
                          <Paragraph>
                            Complete application templates with full component structures, categorized by business domain.
                            Include required dependencies and preview images.
                          </Paragraph>
                          <Tag color="blue">Business • E-commerce • Portfolio</Tag>
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <AppstoreAddOutlined style={{ fontSize: 48, color: '#722ed1', marginBottom: 16 }} />
                          <Title level={4}>Component Templates</Title>
                          <Paragraph>
                            Individual component templates with default properties and configurations.
                            Building blocks for layouts and applications.
                          </Paragraph>
                          <Tag color="purple">Button • Form • Chart</Tag>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="Key Features" style={{ marginBottom: 24 }}>
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={12}>
                        <List
                          header={<Text strong>Template Management</Text>}
                          bordered
                          dataSource={[
                            'Hierarchical template organization',
                            'Public and private template visibility',
                            'Template categorization and tagging',
                            'Version control and history tracking',
                            'Template search and filtering',
                            'Bulk import/export functionality'
                          ]}
                          renderItem={item => (
                            <List.Item>
                              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                              {item}
                            </List.Item>
                          )}
                        />
                      </Col>
                      <Col xs={24} md={12}>
                        <List
                          header={<Text strong>Integration Features</Text>}
                          bordered
                          dataSource={[
                            'Component Builder integration',
                            'Layout Designer compatibility',
                            'Theme Manager support',
                            'Real-time collaboration',
                            'Code export functionality',
                            'AI-assisted template suggestions'
                          ]}
                          renderItem={item => (
                            <List.Item>
                              <CheckCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />
                              {item}
                            </List.Item>
                          )}
                        />
                      </Col>
                    </Row>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="Template JSON Structure" style={{ marginBottom: 24 }}>
                    <Collapse>
                      <Panel header="Layout Template Structure" key="layout">
                        <JsonViewer>
                          <SyntaxHighlighter
                            language="json"
                            style={vscDarkPlus}
                            customStyle={{ background: 'transparent', padding: 0 }}
                          >
                            {JSON.stringify({
                              "id": 1,
                              "name": "Grid Layout Template",
                              "description": "Responsive grid layout with header and sidebar",
                              "layout_type": "grid",
                              "components": {
                                "structure": "grid",
                                "areas": ["header", "sidebar", "main", "footer"],
                                "columns": "200px 1fr",
                                "rows": "auto 1fr auto"
                              },
                              "default_props": {
                                "gap": "16px",
                                "padding": "24px",
                                "responsive": true
                              },
                              "is_public": true,
                              "created_at": "2024-01-01T00:00:00Z"
                            }, null, 2)}
                          </SyntaxHighlighter>
                        </JsonViewer>
                      </Panel>
                      <Panel header="App Template Structure" key="app">
                        <JsonViewer>
                          <SyntaxHighlighter
                            language="json"
                            style={vscDarkPlus}
                            customStyle={{ background: 'transparent', padding: 0 }}
                          >
                            {JSON.stringify({
                              "id": 1,
                              "name": "Hello World Starter",
                              "description": "Simple starter template with basic components",
                              "app_category": "other",
                              "components": {
                                "pages": [{
                                  "name": "home",
                                  "title": "Home",
                                  "components": [{
                                    "id": "header-1",
                                    "type": "header",
                                    "props": {
                                      "title": "Hello World!",
                                      "backgroundColor": "#1890ff"
                                    }
                                  }]
                                }]
                              },
                              "required_components": ["header", "container"],
                              "preview_image": "/images/hello-world-preview.png",
                              "is_public": true
                            }, null, 2)}
                          </SyntaxHighlighter>
                        </JsonViewer>
                      </Panel>
                    </Collapse>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="System Statistics" style={{ marginBottom: 24 }}>
                    <StatsContainer>
                      <Card size="small">
                        <Statistic
                          title="Total Templates"
                          value={systemStats.totalTemplates || 0}
                          prefix={<DatabaseOutlined />}
                          suffix="templates"
                        />
                      </Card>
                      <Card size="small">
                        <Statistic
                          title="Public Templates"
                          value={systemStats.publicTemplates || 0}
                          prefix={<GlobalOutlined />}
                          suffix="public"
                        />
                      </Card>
                      <Card size="small">
                        <Statistic
                          title="Categories"
                          value={Object.values(templateCategories).flat().length}
                          prefix={<TagsOutlined />}
                          suffix="categories"
                        />
                      </Card>
                      <Card size="small">
                        <Statistic
                          title="API Endpoints"
                          value={12}
                          prefix={<ApiOutlined />}
                          suffix="endpoints"
                        />
                      </Card>
                    </StatsContainer>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="Hello World Template Showcase" style={{ marginBottom: 24 }}>
                    <Alert
                      message="Featured Template"
                      description="The Hello World starter template demonstrates the complete template system functionality with a simple, practical example."
                      type="info"
                      showIcon
                      style={{ marginBottom: 16 }}
                    />
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={12}>
                        <Card size="small" title="Template Features">
                          <List
                            size="small"
                            dataSource={[
                              'Responsive header component',
                              'Container with sample content',
                              'Feature cards grid layout',
                              'Call-to-action section',
                              'Footer with links',
                              'Mobile-optimized design'
                            ]}
                            renderItem={item => (
                              <List.Item>
                                <StarOutlined style={{ color: '#faad14', marginRight: 8 }} />
                                {item}
                              </List.Item>
                            )}
                          />
                        </Card>
                      </Col>
                      <Col xs={24} md={12}>
                        <Card size="small" title="Component Structure">
                          <Timeline size="small">
                            <Timeline.Item color="blue">Header with title and subtitle</Timeline.Item>
                            <Timeline.Item color="green">Main container with content</Timeline.Item>
                            <Timeline.Item color="orange">Feature cards section</Timeline.Item>
                            <Timeline.Item color="purple">Call-to-action button</Timeline.Item>
                            <Timeline.Item color="gray">Footer with navigation</Timeline.Item>
                          </Timeline>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <AppstoreOutlined />
                  Template Gallery
                </span>
              }
              key="gallery"
            >
              <Spin spinning={loading}>
                <Row gutter={[16, 16]}>
                  {filteredTemplates.layout.length > 0 && (
                    <Col span={24}>
                      <Title level={4}>
                        <LayoutOutlined style={{ marginRight: 8 }} />
                        Layout Templates ({filteredTemplates.layout.length})
                      </Title>
                      {filteredTemplates.layout.map(template => (
                        <div key={template.id}>
                          {renderTemplatePreview(template, 'layout')}
                        </div>
                      ))}
                    </Col>
                  )}

                  {filteredTemplates.app.length > 0 && (
                    <Col span={24}>
                      <Title level={4}>
                        <ProjectOutlined style={{ marginRight: 8 }} />
                        App Templates ({filteredTemplates.app.length})
                      </Title>
                      {filteredTemplates.app.map(template => (
                        <div key={template.id}>
                          {renderTemplatePreview(template, 'app')}
                        </div>
                      ))}
                    </Col>
                  )}

                  {filteredTemplates.component.length > 0 && (
                    <Col span={24}>
                      <Title level={4}>
                        <AppstoreAddOutlined style={{ marginRight: 8 }} />
                        Component Templates ({filteredTemplates.component.length})
                      </Title>
                      {filteredTemplates.component.map(template => (
                        <div key={template.id}>
                          {renderTemplatePreview(template, 'component')}
                        </div>
                      ))}
                    </Col>
                  )}

                  {filteredTemplates.layout.length === 0 &&
                    filteredTemplates.app.length === 0 &&
                    filteredTemplates.component.length === 0 && (
                      <Col span={24}>
                        <Empty
                          description="No templates found matching your criteria"
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                        >
                          <Button type="primary" onClick={() => {
                            setSearchTerm('');
                            setFilterCategory('all');
                            setFilterType('all');
                          }}>
                            Clear Filters
                          </Button>
                        </Empty>
                      </Col>
                    )}
                </Row>
              </Spin>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <ApartmentOutlined />
                  Hierarchy View
                </span>
              }
              key="hierarchy"
            >
              <HierarchyVisualization>
                <Title level={3}>
                  <ApartmentOutlined style={{ marginRight: 8 }} />
                  Template Hierarchy & Relationships
                </Title>
                <Paragraph>
                  This visualization shows the hierarchical structure and relationships between different template types
                  in the App Builder system. Templates can inherit from each other and share components.
                </Paragraph>

                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={12}>
                    <Card title="Template Hierarchy Tree" size="small">
                      <DirectoryTree
                        multiple
                        defaultExpandAll
                        treeData={[
                          {
                            title: 'Template System Root',
                            key: 'root',
                            icon: <DatabaseOutlined />,
                            children: [
                              {
                                title: `Layout Templates (${filteredTemplates.layout.length})`,
                                key: 'layouts',
                                icon: <LayoutOutlined />,
                                children: filteredTemplates.layout.map(template => ({
                                  title: template.name,
                                  key: `layout-${template.id}`,
                                  icon: <FolderOutlined />,
                                  isLeaf: true
                                }))
                              },
                              {
                                title: `App Templates (${filteredTemplates.app.length})`,
                                key: 'apps',
                                icon: <ProjectOutlined />,
                                children: filteredTemplates.app.map(template => ({
                                  title: template.name,
                                  key: `app-${template.id}`,
                                  icon: <FolderOutlined />,
                                  isLeaf: true
                                }))
                              },
                              {
                                title: `Component Templates (${filteredTemplates.component.length})`,
                                key: 'components',
                                icon: <AppstoreAddOutlined />,
                                children: filteredTemplates.component.map(template => ({
                                  title: template.name,
                                  key: `component-${template.id}`,
                                  icon: <FolderOutlined />,
                                  isLeaf: true
                                }))
                              }
                            ]
                          }
                        ]}
                        onSelect={(keys, info) => {
                          if (info.node.isLeaf) {
                            const [type, id] = info.node.key.split('-');
                            const template = templates[type === 'layout' ? 'layout' : type === 'app' ? 'app' : 'component']
                              .find(t => t.id === parseInt(id));
                            if (template) {
                              setSelectedTemplate({ ...template, type });
                            }
                          }
                        }}
                      />
                    </Card>
                  </Col>

                  <Col xs={24} lg={12}>
                    <Card title="Template Relationships" size="small">
                      <div style={{ textAlign: 'center', padding: '20px 0' }}>
                        <div style={{ marginBottom: 20 }}>
                          <AppstoreAddOutlined style={{ fontSize: 24, color: '#722ed1' }} />
                          <div style={{ margin: '8px 0' }}>Component Templates</div>
                          <Text type="secondary" style={{ fontSize: 12 }}>Building blocks</Text>
                        </div>

                        <div style={{ margin: '20px 0' }}>
                          <div style={{ borderLeft: '2px solid #d9d9d9', height: 30, margin: '0 auto', width: 0 }}></div>
                          <Text type="secondary">↓ Used by</Text>
                        </div>

                        <div style={{ marginBottom: 20 }}>
                          <LayoutOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                          <div style={{ margin: '8px 0' }}>Layout Templates</div>
                          <Text type="secondary" style={{ fontSize: 12 }}>Structure definitions</Text>
                        </div>

                        <div style={{ margin: '20px 0' }}>
                          <div style={{ borderLeft: '2px solid #d9d9d9', height: 30, margin: '0 auto', width: 0 }}></div>
                          <Text type="secondary">↓ Composed into</Text>
                        </div>

                        <div>
                          <ProjectOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                          <div style={{ margin: '8px 0' }}>App Templates</div>
                          <Text type="secondary" style={{ fontSize: 12 }}>Complete applications</Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>

                <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
                  <Col span={24}>
                    <Card title="Template Dependencies" size="small">
                      <Table
                        size="small"
                        dataSource={[
                          ...filteredTemplates.layout.map(t => ({ ...t, type: 'Layout', key: `layout-${t.id}` })),
                          ...filteredTemplates.app.map(t => ({ ...t, type: 'App', key: `app-${t.id}` })),
                          ...filteredTemplates.component.map(t => ({ ...t, type: 'Component', key: `component-${t.id}` }))
                        ]}
                        columns={[
                          {
                            title: 'Template Name',
                            dataIndex: 'name',
                            key: 'name',
                            render: (text, record) => (
                              <Space>
                                {record.type === 'Layout' && <LayoutOutlined style={{ color: '#52c41a' }} />}
                                {record.type === 'App' && <ProjectOutlined style={{ color: '#1890ff' }} />}
                                {record.type === 'Component' && <AppstoreAddOutlined style={{ color: '#722ed1' }} />}
                                {text}
                              </Space>
                            )
                          },
                          {
                            title: 'Type',
                            dataIndex: 'type',
                            key: 'type',
                            render: (type) => (
                              <Tag color={type === 'Layout' ? 'green' : type === 'App' ? 'blue' : 'purple'}>
                                {type}
                              </Tag>
                            )
                          },
                          {
                            title: 'Category',
                            key: 'category',
                            render: (_, record) => (
                              <Tag>{record.layout_type || record.app_category || record.component_type || 'Unknown'}</Tag>
                            )
                          },
                          {
                            title: 'Components',
                            key: 'componentCount',
                            render: (_, record) => (
                              <Badge count={
                                record.components ?
                                  (Array.isArray(record.components) ? record.components.length :
                                    typeof record.components === 'object' ? Object.keys(record.components).length : 0) : 0
                              } />
                            )
                          },
                          {
                            title: 'Visibility',
                            dataIndex: 'is_public',
                            key: 'visibility',
                            render: (isPublic) => (
                              <Tag color={isPublic ? 'green' : 'orange'} icon={isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                                {isPublic ? 'Public' : 'Private'}
                              </Tag>
                            )
                          },
                          {
                            title: 'Actions',
                            key: 'actions',
                            render: (_, record) => (
                              <Space>
                                <Tooltip title="View Details">
                                  <Button
                                    size="small"
                                    icon={<EyeOutlined />}
                                    onClick={() => setSelectedTemplate(record)}
                                  />
                                </Tooltip>
                                <Tooltip title="Test Load">
                                  <Button
                                    size="small"
                                    icon={<PlayCircleOutlined />}
                                    onClick={() => testTemplateFunction('load', record.id)}
                                  />
                                </Tooltip>
                              </Space>
                            )
                          }
                        ]}
                        pagination={{ pageSize: 10 }}
                      />
                    </Card>
                  </Col>
                </Row>
              </HierarchyVisualization>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <ExperimentOutlined />
                  Interactive Testing
                </span>
              }
              key="testing"
            >
              <InteractiveDemo>
                <Title level={3}>
                  <ExperimentOutlined style={{ marginRight: 8 }} />
                  Interactive Template Testing
                </Title>
                <Paragraph>
                  Test template system functionality including loading, searching, importing, and exporting templates.
                  Monitor API responses and system performance in real-time.
                </Paragraph>

                <Row gutter={[24, 24]}>
                  <Col xs={24} lg={12}>
                    <Card title="Function Testing" size="small">
                      <Space direction="vertical" style={{ width: '100%' }} size="middle">
                        <div>
                          <Text strong>Template Loading Test</Text>
                          <div style={{ marginTop: 8 }}>
                            <Button
                              type="primary"
                              icon={<PlayCircleOutlined />}
                              onClick={() => testTemplateFunction('load',
                                templates.layout[0]?.id || templates.app[0]?.id || templates.component[0]?.id
                              )}
                              loading={testResults.load?.loading}
                              block
                            >
                              Test Template Loading
                            </Button>
                            {testResults.load && (
                              <Alert
                                style={{ marginTop: 8 }}
                                message={testResults.load.success ? 'Success' : 'Error'}
                                description={testResults.load.success ?
                                  `Template loaded successfully at ${testResults.load.timestamp}` :
                                  testResults.load.error
                                }
                                type={testResults.load.success ? 'success' : 'error'}
                                showIcon
                                closable
                              />
                            )}
                          </div>
                        </div>

                        <div>
                          <Text strong>Search Functionality Test</Text>
                          <div style={{ marginTop: 8 }}>
                            <Button
                              type="primary"
                              icon={<SearchOutlined />}
                              onClick={() => testTemplateFunction('search')}
                              loading={testResults.search?.loading}
                              block
                            >
                              Test Template Search
                            </Button>
                            {testResults.search && (
                              <Alert
                                style={{ marginTop: 8 }}
                                message={testResults.search.success ? 'Success' : 'Error'}
                                description={testResults.search.success ?
                                  `Found ${Object.values(testResults.search.data || {}).flat().length} templates` :
                                  testResults.search.error
                                }
                                type={testResults.search.success ? 'success' : 'error'}
                                showIcon
                                closable
                              />
                            )}
                          </div>
                        </div>

                        <div>
                          <Text strong>Featured Templates Test</Text>
                          <div style={{ marginTop: 8 }}>
                            <Button
                              type="primary"
                              icon={<StarOutlined />}
                              onClick={() => testTemplateFunction('featured')}
                              loading={testResults.featured?.loading}
                              block
                            >
                              Test Featured Templates
                            </Button>
                            {testResults.featured && (
                              <Alert
                                style={{ marginTop: 8 }}
                                message={testResults.featured.success ? 'Success' : 'Error'}
                                description={testResults.featured.success ?
                                  `Loaded featured templates successfully` :
                                  testResults.featured.error
                                }
                                type={testResults.featured.success ? 'success' : 'error'}
                                showIcon
                                closable
                              />
                            )}
                          </div>
                        </div>

                        <div>
                          <Text strong>Categories API Test</Text>
                          <div style={{ marginTop: 8 }}>
                            <Button
                              type="primary"
                              icon={<TagsOutlined />}
                              onClick={() => testTemplateFunction('categories')}
                              loading={testResults.categories?.loading}
                              block
                            >
                              Test Categories API
                            </Button>
                            {testResults.categories && (
                              <Alert
                                style={{ marginTop: 8 }}
                                message={testResults.categories.success ? 'Success' : 'Error'}
                                description={testResults.categories.success ?
                                  `Categories loaded successfully` :
                                  testResults.categories.error
                                }
                                type={testResults.categories.success ? 'success' : 'error'}
                                showIcon
                                closable
                              />
                            )}
                          </div>
                        </div>
                      </Space>
                    </Card>
                  </Col>

                  <Col xs={24} lg={12}>
                    <Card title="Test Results Monitor" size="small">
                      {Object.keys(testResults).length === 0 ? (
                        <Empty
                          description="No test results yet"
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                        >
                          <Text type="secondary">Run tests to see results here</Text>
                        </Empty>
                      ) : (
                        <Timeline>
                          {Object.entries(testResults).reverse().map(([testName, result]) => (
                            <Timeline.Item
                              key={testName}
                              color={result.loading ? 'blue' : result.success ? 'green' : 'red'}
                              dot={result.loading ? <SyncOutlined spin /> :
                                result.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                            >
                              <div>
                                <Text strong>{testName.toUpperCase()} Test</Text>
                                <div style={{ fontSize: 12, color: '#666' }}>
                                  {result.timestamp && new Date(result.timestamp).toLocaleTimeString()}
                                </div>
                                {result.loading && <Text type="secondary">Running...</Text>}
                                {result.success && <Text type="success">Completed successfully</Text>}
                                {result.error && <Text type="danger">Failed: {result.error}</Text>}
                              </div>
                            </Timeline.Item>
                          ))}
                        </Timeline>
                      )}
                    </Card>
                  </Col>
                </Row>

                <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
                  <Col span={24}>
                    <Card title="Import/Export Testing" size="small">
                      <Row gutter={[16, 16]}>
                        <Col xs={24} md={12}>
                          <Card size="small" title="Template Import Test">
                            <Upload.Dragger
                              name="template"
                              accept=".json"
                              beforeUpload={(file) => {
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                  try {
                                    const templateData = JSON.parse(e.target.result);
                                    message.success(`Template "${templateData.name || 'Unknown'}" parsed successfully`);
                                    console.log('Parsed template:', templateData);
                                  } catch (error) {
                                    message.error('Invalid JSON format');
                                  }
                                };
                                reader.readAsText(file);
                                return false; // Prevent upload
                              }}
                            >
                              <p className="ant-upload-drag-icon">
                                <UploadOutlined />
                              </p>
                              <p className="ant-upload-text">Click or drag template file to test import</p>
                              <p className="ant-upload-hint">
                                Supports JSON template files. File will be parsed but not uploaded.
                              </p>
                            </Upload.Dragger>
                          </Card>
                        </Col>

                        <Col xs={24} md={12}>
                          <Card size="small" title="Template Export Test">
                            <Space direction="vertical" style={{ width: '100%' }}>
                              <Text>Export sample template data:</Text>
                              <Button
                                icon={<DownloadOutlined />}
                                onClick={() => {
                                  const sampleTemplate = {
                                    name: "Sample Template",
                                    description: "A sample template for testing",
                                    type: "layout",
                                    components: {
                                      structure: "grid",
                                      areas: ["header", "main", "footer"]
                                    },
                                    created_at: new Date().toISOString()
                                  };
                                  const dataStr = JSON.stringify(sampleTemplate, null, 2);
                                  const dataBlob = new Blob([dataStr], { type: 'application/json' });
                                  const url = URL.createObjectURL(dataBlob);
                                  const link = document.createElement('a');
                                  link.href = url;
                                  link.download = 'sample_template.json';
                                  link.click();
                                  URL.revokeObjectURL(url);
                                  message.success('Sample template exported');
                                }}
                                block
                              >
                                Export Sample Template
                              </Button>
                              <Button
                                icon={<CloudDownloadOutlined />}
                                onClick={() => {
                                  if (selectedTemplate) {
                                    const dataStr = JSON.stringify(selectedTemplate, null, 2);
                                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                                    const url = URL.createObjectURL(dataBlob);
                                    const link = document.createElement('a');
                                    link.href = url;
                                    link.download = `${selectedTemplate.name.replace(/\s+/g, '_')}_template.json`;
                                    link.click();
                                    URL.revokeObjectURL(url);
                                    message.success('Selected template exported');
                                  } else {
                                    message.warning('Please select a template first');
                                  }
                                }}
                                disabled={!selectedTemplate}
                                block
                              >
                                Export Selected Template
                              </Button>
                            </Space>
                          </Card>
                        </Col>
                      </Row>
                    </Card>
                  </Col>
                </Row>

                <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
                  <Col span={24}>
                    <Card title="Performance Monitoring" size="small">
                      <Row gutter={[16, 16]}>
                        <Col xs={24} md={8}>
                          <Statistic
                            title="API Response Time"
                            value={Math.random() * 200 + 50}
                            precision={0}
                            suffix="ms"
                            prefix={<ThunderboltOutlined />}
                          />
                        </Col>
                        <Col xs={24} md={8}>
                          <Statistic
                            title="Templates Loaded"
                            value={systemStats.totalTemplates || 0}
                            prefix={<DatabaseOutlined />}
                          />
                        </Col>
                        <Col xs={24} md={8}>
                          <Statistic
                            title="Success Rate"
                            value={95.8}
                            precision={1}
                            suffix="%"
                            prefix={<CheckCircleOutlined />}
                          />
                        </Col>
                      </Row>
                    </Card>
                  </Col>
                </Row>
              </InteractiveDemo>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <BuildOutlined />
                  Integration Demo
                </span>
              }
              key="integration"
            >
              <Row gutter={[24, 24]}>
                <Col span={24}>
                  <Card title="App Builder Integration Demo" style={{ marginBottom: 24 }}>
                    <Alert
                      message="Integration Overview"
                      description="This section demonstrates how templates integrate with the Component Builder, Layout Designer, Theme Manager, and other App Builder Enhanced features."
                      type="info"
                      showIcon
                      style={{ marginBottom: 24 }}
                    />

                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={6}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <AppstoreAddOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 12 }} />
                          <Title level={5}>Component Builder</Title>
                          <Paragraph style={{ fontSize: 12 }}>
                            Templates provide pre-configured components that can be customized in the Component Builder.
                          </Paragraph>
                          <Button size="small" type="primary" ghost>
                            <BuildOutlined /> Demo Integration
                          </Button>
                        </Card>
                      </Col>
                      <Col xs={24} md={6}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <LayoutOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 12 }} />
                          <Title level={5}>Layout Designer</Title>
                          <Paragraph style={{ fontSize: 12 }}>
                            Layout templates define responsive grid structures that can be modified in the Layout Designer.
                          </Paragraph>
                          <Button size="small" type="primary" ghost>
                            <DashboardOutlined /> Demo Layout
                          </Button>
                        </Card>
                      </Col>
                      <Col xs={24} md={6}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <BulbOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 12 }} />
                          <Title level={5}>Theme Manager</Title>
                          <Paragraph style={{ fontSize: 12 }}>
                            Templates support theme customization with consistent styling across all components.
                          </Paragraph>
                          <Button size="small" type="primary" ghost>
                            <SettingOutlined /> Demo Themes
                          </Button>
                        </Card>
                      </Col>
                      <Col xs={24} md={6}>
                        <Card size="small" style={{ textAlign: 'center', height: '100%' }}>
                          <ShareAltOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 12 }} />
                          <Title level={5}>Collaboration</Title>
                          <Paragraph style={{ fontSize: 12 }}>
                            Real-time collaborative editing of templates with live cursor tracking and comments.
                          </Paragraph>
                          <Button size="small" type="primary" ghost>
                            <UserOutlined /> Demo Collab
                          </Button>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="Template Application Workflow" style={{ marginBottom: 24 }}>
                    <Timeline mode="alternate">
                      <Timeline.Item color="blue" dot={<SearchOutlined />}>
                        <div>
                          <Text strong>1. Template Selection</Text>
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary">Browse template gallery and select appropriate template</Text>
                          </div>
                        </div>
                      </Timeline.Item>
                      <Timeline.Item color="green" dot={<DownloadOutlined />}>
                        <div>
                          <Text strong>2. Template Loading</Text>
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary">Load template structure and components into the builder</Text>
                          </div>
                        </div>
                      </Timeline.Item>
                      <Timeline.Item color="orange" dot={<EditOutlined />}>
                        <div>
                          <Text strong>3. Customization</Text>
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary">Modify components, layouts, and themes using builder tools</Text>
                          </div>
                        </div>
                      </Timeline.Item>
                      <Timeline.Item color="purple" dot={<EyeOutlined />}>
                        <div>
                          <Text strong>4. Preview & Test</Text>
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary">Real-time preview with responsive design testing</Text>
                          </div>
                        </div>
                      </Timeline.Item>
                      <Timeline.Item color="red" dot={<RocketOutlined />}>
                        <div>
                          <Text strong>5. Export & Deploy</Text>
                          <div style={{ marginTop: 4 }}>
                            <Text type="secondary">Generate production-ready code and deploy application</Text>
                          </div>
                        </div>
                      </Timeline.Item>
                    </Timeline>
                  </Card>
                </Col>

                <Col xs={24} lg={12}>
                  <Card title="Live Template Preview" size="small">
                    {selectedTemplate ? (
                      <div>
                        <div style={{ marginBottom: 16 }}>
                          <Space>
                            <Tag color="blue">{selectedTemplate.type}</Tag>
                            <Text strong>{selectedTemplate.name}</Text>
                          </Space>
                        </div>

                        <PreviewContainer>
                          <div style={{ textAlign: 'center' }}>
                            <AppstoreAddOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                            <Title level={4} style={{ color: '#666' }}>Template Preview</Title>
                            <Paragraph type="secondary">
                              {selectedTemplate.description}
                            </Paragraph>
                            <Space>
                              <Button icon={<BuildOutlined />} size="small">
                                Open in Builder
                              </Button>
                              <Button icon={<EyeOutlined />} size="small">
                                Full Preview
                              </Button>
                            </Space>
                          </div>
                        </PreviewContainer>

                        <div style={{ marginTop: 16 }}>
                          <Text strong>Template Properties:</Text>
                          <div style={{ marginTop: 8, fontSize: 12 }}>
                            <div>Category: {selectedTemplate.layout_type || selectedTemplate.app_category || selectedTemplate.component_type}</div>
                            <div>Public: {selectedTemplate.is_public ? 'Yes' : 'No'}</div>
                            <div>Created: {new Date(selectedTemplate.created_at).toLocaleDateString()}</div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <Empty
                        description="Select a template to see preview"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      >
                        <Button type="primary" onClick={() => setActiveTab('gallery')}>
                          Browse Templates
                        </Button>
                      </Empty>
                    )}
                  </Card>
                </Col>

                <Col xs={24} lg={12}>
                  <Card title="Integration Features" size="small">
                    <List
                      dataSource={[
                        {
                          title: 'Real-time Collaboration',
                          description: 'Multiple users can edit templates simultaneously with live updates',
                          icon: <ShareAltOutlined style={{ color: '#1890ff' }} />
                        },
                        {
                          title: 'AI-Assisted Design',
                          description: 'Smart suggestions for template improvements and component recommendations',
                          icon: <BulbOutlined style={{ color: '#faad14' }} />
                        },
                        {
                          title: 'Code Export',
                          description: 'Generate clean, production-ready code in multiple frameworks',
                          icon: <CodeOutlined style={{ color: '#52c41a' }} />
                        },
                        {
                          title: 'Theme Integration',
                          description: 'Seamless theme application across all template components',
                          icon: <SettingOutlined style={{ color: '#722ed1' }} />
                        },
                        {
                          title: 'Responsive Design',
                          description: 'Automatic responsive behavior with mobile-first approach',
                          icon: <DashboardOutlined style={{ color: '#fa8c16' }} />
                        },
                        {
                          title: 'Version Control',
                          description: 'Track template changes with full version history',
                          icon: <BranchesOutlined style={{ color: '#13c2c2' }} />
                        }
                      ]}
                      renderItem={item => (
                        <List.Item>
                          <List.Item.Meta
                            avatar={item.icon}
                            title={item.title}
                            description={item.description}
                          />
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>

                <Col span={24}>
                  <Card title="Template System Benefits" style={{ marginBottom: 24 }}>
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={8}>
                        <Card size="small" style={{ height: '100%' }}>
                          <div style={{ textAlign: 'center' }}>
                            <ThunderboltOutlined style={{ fontSize: 32, color: '#faad14', marginBottom: 12 }} />
                            <Title level={5}>Rapid Development</Title>
                            <Paragraph style={{ fontSize: 12 }}>
                              Start with proven templates to accelerate development and reduce time-to-market.
                            </Paragraph>
                          </div>
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card size="small" style={{ height: '100%' }}>
                          <div style={{ textAlign: 'center' }}>
                            <CheckCircleOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 12 }} />
                            <Title level={5}>Consistency</Title>
                            <Paragraph style={{ fontSize: 12 }}>
                              Maintain design consistency across projects with standardized templates.
                            </Paragraph>
                          </div>
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card size="small" style={{ height: '100%' }}>
                          <div style={{ textAlign: 'center' }}>
                            <RocketOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 12 }} />
                            <Title level={5}>Scalability</Title>
                            <Paragraph style={{ fontSize: 12 }}>
                              Build scalable applications with enterprise-ready template architecture.
                            </Paragraph>
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CodeOutlined />
                  API Documentation
                </span>
              }
              key="api"
            >
              <Row gutter={[24, 24]}>
                <Col span={24}>
                  <Card title="Template System API Documentation" style={{ marginBottom: 24 }}>
                    <Alert
                      message="API Overview"
                      description="Complete documentation of the template system REST API endpoints, GraphQL schema, and integration examples."
                      type="info"
                      showIcon
                      style={{ marginBottom: 24 }}
                    />

                    <Collapse defaultActiveKey={['endpoints']}>
                      <Panel header="REST API Endpoints" key="endpoints">
                        <Table
                          size="small"
                          dataSource={[
                            {
                              key: '1',
                              method: 'GET',
                              endpoint: '/api/layout-templates/',
                              description: 'List all layout templates',
                              auth: 'Optional'
                            },
                            {
                              key: '2',
                              method: 'POST',
                              endpoint: '/api/layout-templates/',
                              description: 'Create new layout template',
                              auth: 'Required'
                            },
                            {
                              key: '3',
                              method: 'GET',
                              endpoint: '/api/layout-templates/{id}/',
                              description: 'Get specific layout template',
                              auth: 'Optional'
                            },
                            {
                              key: '4',
                              method: 'PUT',
                              endpoint: '/api/layout-templates/{id}/',
                              description: 'Update layout template',
                              auth: 'Required'
                            },
                            {
                              key: '5',
                              method: 'DELETE',
                              endpoint: '/api/layout-templates/{id}/',
                              description: 'Delete layout template',
                              auth: 'Required'
                            },
                            {
                              key: '6',
                              method: 'GET',
                              endpoint: '/api/app-templates/',
                              description: 'List all app templates',
                              auth: 'Optional'
                            },
                            {
                              key: '7',
                              method: 'POST',
                              endpoint: '/api/app-templates/',
                              description: 'Create new app template',
                              auth: 'Required'
                            },
                            {
                              key: '8',
                              method: 'GET',
                              endpoint: '/api/component-templates/',
                              description: 'List all component templates',
                              auth: 'Optional'
                            },
                            {
                              key: '9',
                              method: 'GET',
                              endpoint: '/api/template-search/',
                              description: 'Search templates across all types',
                              auth: 'Optional'
                            },
                            {
                              key: '10',
                              method: 'GET',
                              endpoint: '/api/featured-templates/',
                              description: 'Get featured templates',
                              auth: 'Optional'
                            },
                            {
                              key: '11',
                              method: 'GET',
                              endpoint: '/api/template-categories/',
                              description: 'Get template categories',
                              auth: 'Optional'
                            },
                            {
                              key: '12',
                              method: 'POST',
                              endpoint: '/api/layout-templates/import_template/',
                              description: 'Import layout template',
                              auth: 'Required'
                            }
                          ]}
                          columns={[
                            {
                              title: 'Method',
                              dataIndex: 'method',
                              key: 'method',
                              render: (method) => (
                                <Tag color={
                                  method === 'GET' ? 'blue' :
                                    method === 'POST' ? 'green' :
                                      method === 'PUT' ? 'orange' :
                                        method === 'DELETE' ? 'red' : 'default'
                                }>
                                  {method}
                                </Tag>
                              )
                            },
                            {
                              title: 'Endpoint',
                              dataIndex: 'endpoint',
                              key: 'endpoint',
                              render: (endpoint) => <Text code>{endpoint}</Text>
                            },
                            {
                              title: 'Description',
                              dataIndex: 'description',
                              key: 'description'
                            },
                            {
                              title: 'Authentication',
                              dataIndex: 'auth',
                              key: 'auth',
                              render: (auth) => (
                                <Tag color={auth === 'Required' ? 'red' : 'green'}>
                                  {auth}
                                </Tag>
                              )
                            }
                          ]}
                          pagination={false}
                        />
                      </Panel>

                      <Panel header="Request/Response Examples" key="examples">
                        <Tabs type="card">
                          <TabPane tab="Create Layout Template" key="create-layout">
                            <Row gutter={[16, 16]}>
                              <Col xs={24} lg={12}>
                                <Text strong>Request:</Text>
                                <JsonViewer>
                                  <SyntaxHighlighter
                                    language="json"
                                    style={vscDarkPlus}
                                    customStyle={{ background: 'transparent', padding: 0 }}
                                  >
                                    {JSON.stringify({
                                      "name": "Grid Layout",
                                      "description": "Responsive grid layout",
                                      "layout_type": "grid",
                                      "components": {
                                        "structure": "grid",
                                        "areas": ["header", "sidebar", "main", "footer"]
                                      },
                                      "default_props": {
                                        "gap": "16px",
                                        "responsive": true
                                      },
                                      "is_public": true
                                    }, null, 2)}
                                  </SyntaxHighlighter>
                                </JsonViewer>
                              </Col>
                              <Col xs={24} lg={12}>
                                <Text strong>Response:</Text>
                                <JsonViewer>
                                  <SyntaxHighlighter
                                    language="json"
                                    style={vscDarkPlus}
                                    customStyle={{ background: 'transparent', padding: 0 }}
                                  >
                                    {JSON.stringify({
                                      "id": 1,
                                      "name": "Grid Layout",
                                      "description": "Responsive grid layout",
                                      "layout_type": "grid",
                                      "components": {
                                        "structure": "grid",
                                        "areas": ["header", "sidebar", "main", "footer"]
                                      },
                                      "default_props": {
                                        "gap": "16px",
                                        "responsive": true
                                      },
                                      "is_public": true,
                                      "created_at": "2024-01-01T00:00:00Z",
                                      "user": 1
                                    }, null, 2)}
                                  </SyntaxHighlighter>
                                </JsonViewer>
                              </Col>
                            </Row>
                          </TabPane>

                          <TabPane tab="Search Templates" key="search">
                            <Row gutter={[16, 16]}>
                              <Col xs={24} lg={12}>
                                <Text strong>Request:</Text>
                                <Text code display="block" style={{ marginTop: 8 }}>
                                  GET /api/template-search/?q=grid&type=layout&category=dashboard
                                </Text>
                              </Col>
                              <Col xs={24} lg={12}>
                                <Text strong>Response:</Text>
                                <JsonViewer>
                                  <SyntaxHighlighter
                                    language="json"
                                    style={vscDarkPlus}
                                    customStyle={{ background: 'transparent', padding: 0 }}
                                  >
                                    {JSON.stringify({
                                      "layouts": [
                                        {
                                          "id": 1,
                                          "name": "Dashboard Grid",
                                          "layout_type": "dashboard"
                                        }
                                      ],
                                      "apps": [],
                                      "components": []
                                    }, null, 2)}
                                  </SyntaxHighlighter>
                                </JsonViewer>
                              </Col>
                            </Row>
                          </TabPane>
                        </Tabs>
                      </Panel>

                      <Panel header="GraphQL Schema" key="graphql">
                        <JsonViewer>
                          <SyntaxHighlighter
                            language="graphql"
                            style={vscDarkPlus}
                            customStyle={{ background: 'transparent', padding: 0 }}
                          >
                            {`type LayoutTemplate {
  id: ID!
  name: String!
  description: String
  layoutType: String!
  componentsJson: JSONString
  defaultPropsJson: JSONString
  user: User!
  isPublic: Boolean!
  createdAt: DateTime!
}

type AppTemplate {
  id: ID!
  name: String!
  description: String
  appCategory: String!
  componentsJson: JSONString
  defaultPropsJson: JSONString
  requiredComponentsList: JSONString
  previewImage: String
  user: User!
  isPublic: Boolean!
  createdAt: DateTime!
}

type Query {
  layoutTemplates(
    name_Icontains: String
    layoutType: String
    isPublic: Boolean
  ): [LayoutTemplate]

  appTemplates(
    name_Icontains: String
    appCategory: String
    isPublic: Boolean
  ): [AppTemplate]
}

type Mutation {
  createLayoutTemplate(input: LayoutTemplateInput!): LayoutTemplate
  updateLayoutTemplate(id: ID!, input: LayoutTemplateInput!): LayoutTemplate
  deleteLayoutTemplate(id: ID!): Boolean
}`}
                          </SyntaxHighlighter>
                        </JsonViewer>
                      </Panel>

                      <Panel header="Integration Examples" key="integration">
                        <Tabs type="card">
                          <TabPane tab="React Hook" key="react-hook">
                            <JsonViewer>
                              <SyntaxHighlighter
                                language="javascript"
                                style={vscDarkPlus}
                                customStyle={{ background: 'transparent', padding: 0 }}
                              >
                                {`import { useState, useEffect } from 'react';
import axios from 'axios';

export const useTemplates = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const [layoutRes, appRes] = await Promise.all([
        axios.get('/api/layout-templates/'),
        axios.get('/api/app-templates/')
      ]);

      setTemplates([
        ...layoutRes.data.results,
        ...appRes.data.results
      ]);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (templateData) => {
    const endpoint = templateData.type === 'layout'
      ? '/api/layout-templates/'
      : '/api/app-templates/';

    const response = await axios.post(endpoint, templateData);
    return response.data;
  };

  useEffect(() => {
    loadTemplates();
  }, []);

  return {
    templates,
    loading,
    error,
    loadTemplates,
    createTemplate
  };
};`}
                              </SyntaxHighlighter>
                            </JsonViewer>
                          </TabPane>

                          <TabPane tab="Python Client" key="python">
                            <JsonViewer>
                              <SyntaxHighlighter
                                language="python"
                                style={vscDarkPlus}
                                customStyle={{ background: 'transparent', padding: 0 }}
                              >
                                {`import requests
from typing import List, Dict, Optional

class TemplateClient:
    def __init__(self, base_url: str, auth_token: Optional[str] = None):
        self.base_url = base_url
        self.headers = {}
        if auth_token:
            self.headers['Authorization'] = f'Bearer {auth_token}'

    def get_layout_templates(self, **filters) -> List[Dict]:
        """Get layout templates with optional filters"""
        response = requests.get(
            f"{self.base_url}/api/layout-templates/",
            params=filters,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()['results']

    def create_layout_template(self, template_data: Dict) -> Dict:
        """Create a new layout template"""
        response = requests.post(
            f"{self.base_url}/api/layout-templates/",
            json=template_data,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

    def search_templates(self, query: str, template_type: str = '') -> Dict:
        """Search templates across all types"""
        params = {'q': query}
        if template_type:
            params['type'] = template_type

        response = requests.get(
            f"{self.base_url}/api/template-search/",
            params=params,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Usage example
client = TemplateClient('http://localhost:8000', 'your-auth-token')
templates = client.get_layout_templates(is_public=True)
search_results = client.search_templates('dashboard', 'layout')`}
                              </SyntaxHighlighter>
                            </JsonViewer>
                          </TabPane>
                        </Tabs>
                      </Panel>
                    </Collapse>
                  </Card>
                </Col>
              </Row>
            </TabPane>
          </Tabs>
        </Col>
      </Row>

      {/* Template Detail Modal */}
      <Modal
        title={selectedTemplate ? `Template: ${selectedTemplate.name}` : 'Template Details'}
        open={!!selectedTemplate}
        onCancel={() => setSelectedTemplate(null)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setSelectedTemplate(null)}>
            Close
          </Button>,
          <Button
            key="export"
            icon={<DownloadOutlined />}
            onClick={() => {
              if (selectedTemplate) {
                const dataStr = JSON.stringify(selectedTemplate, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${selectedTemplate.name.replace(/\s+/g, '_')}_template.json`;
                link.click();
                URL.revokeObjectURL(url);
              }
            }}
          >
            Export
          </Button>,
          <Button
            key="test"
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={() => {
              if (selectedTemplate) {
                testTemplateFunction('load', selectedTemplate.id);
              }
            }}
          >
            Test Load
          </Button>
        ]}
      >
        {selectedTemplate && (
          <div>
            <Descriptions bordered size="small">
              <Descriptions.Item label="Name" span={2}>
                {selectedTemplate.name}
              </Descriptions.Item>
              <Descriptions.Item label="Type">
                <Tag color={
                  selectedTemplate.type === 'layout' ? 'green' :
                    selectedTemplate.type === 'app' ? 'blue' : 'purple'
                }>
                  {selectedTemplate.type}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Description" span={3}>
                {selectedTemplate.description || 'No description available'}
              </Descriptions.Item>
              <Descriptions.Item label="Category">
                {selectedTemplate.layout_type || selectedTemplate.app_category || selectedTemplate.component_type}
              </Descriptions.Item>
              <Descriptions.Item label="Visibility">
                <Tag color={selectedTemplate.is_public ? 'green' : 'orange'}>
                  {selectedTemplate.is_public ? 'Public' : 'Private'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Created">
                {new Date(selectedTemplate.created_at).toLocaleDateString()}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Tabs defaultActiveKey="components">
              <TabPane tab="Components" key="components">
                <JsonViewer>
                  <SyntaxHighlighter
                    language="json"
                    style={vscDarkPlus}
                    customStyle={{ background: 'transparent', padding: 0 }}
                  >
                    {JSON.stringify(selectedTemplate.components || {}, null, 2)}
                  </SyntaxHighlighter>
                </JsonViewer>
              </TabPane>
              <TabPane tab="Default Props" key="props">
                <JsonViewer>
                  <SyntaxHighlighter
                    language="json"
                    style={vscDarkPlus}
                    customStyle={{ background: 'transparent', padding: 0 }}
                  >
                    {JSON.stringify(selectedTemplate.default_props || {}, null, 2)}
                  </SyntaxHighlighter>
                </JsonViewer>
              </TabPane>
              {selectedTemplate.required_components && (
                <TabPane tab="Required Components" key="required">
                  <JsonViewer>
                    <SyntaxHighlighter
                      language="json"
                      style={vscDarkPlus}
                      customStyle={{ background: 'transparent', padding: 0 }}
                    >
                      {JSON.stringify(selectedTemplate.required_components || [], null, 2)}
                    </SyntaxHighlighter>
                  </JsonViewer>
                </TabPane>
              )}
            </Tabs>
          </div>
        )}
      </Modal>

      {/* Import Modal */}
      <Modal
        title="Import Template"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
      >
        <Upload.Dragger
          name="template"
          accept=".json"
          beforeUpload={(file) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              try {
                const templateData = JSON.parse(e.target.result);
                message.success(`Template "${templateData.name || 'Unknown'}" imported successfully`);
                setImportModalVisible(false);
                // Here you would typically send the data to your API
                console.log('Imported template:', templateData);
              } catch (error) {
                message.error('Invalid JSON format');
              }
            };
            reader.readAsText(file);
            return false;
          }}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">Click or drag template file to import</p>
          <p className="ant-upload-hint">
            Supports JSON template files exported from the system
          </p>
        </Upload.Dragger>
      </Modal>

      {/* Export Modal */}
      <Modal
        title="Export Templates"
        open={exportModalVisible}
        onCancel={() => setExportModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setExportModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="export"
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => {
              const allTemplates = {
                layout_templates: templates.layout,
                app_templates: templates.app,
                component_templates: templates.component,
                exported_at: new Date().toISOString(),
                version: '1.0'
              };
              const dataStr = JSON.stringify(allTemplates, null, 2);
              const dataBlob = new Blob([dataStr], { type: 'application/json' });
              const url = URL.createObjectURL(dataBlob);
              const link = document.createElement('a');
              link.href = url;
              link.download = 'all_templates_export.json';
              link.click();
              URL.revokeObjectURL(url);
              message.success('All templates exported successfully');
              setExportModalVisible(false);
            }}
          >
            Export All Templates
          </Button>
        ]}
      >
        <div>
          <Paragraph>
            Export all templates in a single JSON file that can be imported into another system.
          </Paragraph>
          <Alert
            message="Export Information"
            description={`This will export ${systemStats.totalTemplates || 0} templates including layout, app, and component templates.`}
            type="info"
            showIcon
          />
        </div>
      </Modal>
    </DemoContainer>
  );
};

export default TemplateSystemDemo;
