import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Typography, Space, Alert, Progress, Tabs, Divider } from 'antd';
import { 
  PlayCircleOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * IntegrationTest - Comprehensive test for App Builder integration
 */
const IntegrationTest = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState('');

  const runIntegrationTests = async () => {
    setIsRunning(true);
    const results = {};

    const tests = [
      {
        name: 'reactEnvironment',
        description: 'React Environment Setup',
        test: () => typeof window.React !== 'undefined' && window.__REACT_LOADED__
      },
      {
        name: 'appInitialization',
        description: 'App Initialization',
        test: () => window.__APP_LOADED__ || document.body.textContent.includes('App Builder')
      },
      {
        name: 'integratedBuilder',
        description: 'Integrated App Builder',
        test: () => document.body.textContent.includes('Enhanced') || 
                   document.body.textContent.includes('Try Enhanced Builder')
      },
      {
        name: 'componentPalette',
        description: 'Component Palette',
        test: () => document.body.textContent.includes('Components') ||
                   document.body.textContent.includes('Add Button')
      },
      {
        name: 'canvasArea',
        description: 'Canvas/Preview Area',
        test: () => document.body.textContent.includes('Canvas') ||
                   document.body.textContent.includes('Preview') ||
                   document.body.textContent.includes('Drag')
      },
      {
        name: 'propertyEditor',
        description: 'Property Editor',
        test: () => document.body.textContent.includes('Properties') ||
                   document.body.textContent.includes('Select a component')
      },
      {
        name: 'layoutDesigner',
        description: 'Layout Designer',
        test: () => document.body.textContent.includes('Layout') ||
                   document.body.textContent.includes('Grid')
      },
      {
        name: 'interactiveElements',
        description: 'Interactive Elements',
        test: () => document.querySelectorAll('button').length >= 3
      },
      {
        name: 'errorHandling',
        description: 'Error Handling',
        test: () => document.querySelectorAll('[class*="error"], [class*="Error"]').length === 0
      },
      {
        name: 'responsiveDesign',
        description: 'Responsive Design',
        test: () => window.innerWidth > 0 && document.body.offsetWidth > 0
      }
    ];

    for (const test of tests) {
      setCurrentTest(test.description);
      await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for visual feedback
      
      try {
        results[test.name] = test.test();
      } catch (error) {
        console.error(`Test ${test.name} failed:`, error);
        results[test.name] = false;
      }
    }

    setTestResults(results);
    setCurrentTest('');
    setIsRunning(false);
  };

  const getTestStatus = (result) => {
    if (result === true) return { icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />, text: 'PASS', color: '#52c41a' };
    if (result === false) return { icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />, text: 'FAIL', color: '#ff4d4f' };
    return { icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />, text: 'UNKNOWN', color: '#faad14' };
  };

  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  const testCategories = {
    core: ['reactEnvironment', 'appInitialization', 'integratedBuilder'],
    builder: ['componentPalette', 'canvasArea', 'propertyEditor'],
    layout: ['layoutDesigner', 'responsiveDesign'],
    system: ['interactiveElements', 'errorHandling']
  };

  const renderTestCategory = (categoryName, testKeys) => {
    const categoryTests = testKeys.filter(key => testResults.hasOwnProperty(key));
    const categoryPassed = categoryTests.filter(key => testResults[key]).length;
    
    return (
      <div key={categoryName} style={{ marginBottom: '16px' }}>
        <Text strong style={{ textTransform: 'capitalize', fontSize: '14px' }}>
          {categoryName} Tests ({categoryPassed}/{categoryTests.length})
        </Text>
        <div style={{ marginTop: '8px' }}>
          {categoryTests.map(testKey => {
            const result = testResults[testKey];
            const status = getTestStatus(result);
            
            return (
              <div key={testKey} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '6px 12px',
                marginBottom: '4px',
                borderRadius: '4px',
                backgroundColor: result ? '#f6ffed' : '#fff2f0',
                border: `1px solid ${result ? '#d9f7be' : '#ffccc7'}`
              }}>
                <Text style={{ fontSize: '13px' }}>
                  {testKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </Text>
                <Space size="small">
                  {status.icon}
                  <Text style={{ color: status.color, fontSize: '12px', fontWeight: 'bold' }}>
                    {status.text}
                  </Text>
                </Space>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Card 
      title={
        <Space>
          <SettingOutlined />
          <span>App Builder Integration Test</span>
        </Space>
      }
      style={{ margin: '20px', maxWidth: '800px' }}
      extra={
        <Button 
          type="primary" 
          icon={<PlayCircleOutlined />} 
          onClick={runIntegrationTests}
          loading={isRunning}
          size="large"
        >
          Run Full Test Suite
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {isRunning && (
          <Alert
            message={`Running Tests... ${currentTest}`}
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        {totalTests > 0 && (
          <>
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text strong>Overall Progress</Text>
                <Text strong>{passedTests}/{totalTests} tests passed</Text>
              </div>
              <Progress 
                percent={successRate} 
                status={successRate >= 80 ? 'success' : successRate >= 60 ? 'active' : 'exception'}
                strokeColor={successRate >= 80 ? '#52c41a' : successRate >= 60 ? '#1890ff' : '#ff4d4f'}
              />
            </div>

            <Alert
              message={`Integration Status: ${successRate >= 80 ? 'EXCELLENT' : successRate >= 60 ? 'GOOD' : 'NEEDS IMPROVEMENT'}`}
              description={`${passedTests} out of ${totalTests} integration tests passed (${Math.round(successRate)}%)`}
              type={successRate >= 80 ? 'success' : successRate >= 60 ? 'info' : 'warning'}
              showIcon
              style={{ marginBottom: '20px' }}
            />

            <Divider />

            <div>
              {Object.entries(testCategories).map(([categoryName, testKeys]) => 
                renderTestCategory(categoryName, testKeys)
              )}
            </div>
          </>
        )}

        {totalTests === 0 && (
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <SettingOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '20px' }} />
            <br />
            <Title level={4} type="secondary">Ready to Test</Title>
            <Text type="secondary">
              Click "Run Full Test Suite" to verify the complete App Builder integration
            </Text>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default IntegrationTest;
