/**
 * Quill Integration Verification Test
 * This test verifies that the Quill editor is properly integrated and styled
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import SharedEditor from '../components/SharedEditor';

// Mock the services
jest.mock('../services/SharedEditingService', () => ({
  initialized: false,
  init: jest.fn(),
  joinDocument: jest.fn(() => ({ content: '', collaborators: {} })),
  leaveDocument: jest.fn(),
  getDocument: jest.fn(() => ({ content: '', collaborators: {} })),
  updateContent: jest.fn(),
  updateCursor: jest.fn(),
  updateSelection: jest.fn(),
  sendDocumentMessage: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
}));

jest.mock('../services/UserPresenceService', () => ({
  initialized: false,
  init: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
}));

jest.mock('../components/UserPresenceIndicator', () => {
  return function MockUserPresenceIndicator({ userId, username }) {
    return <div data-testid="user-presence">{username}</div>;
  };
});

describe('Quill Integration Verification', () => {
  const defaultProps = {
    documentId: 'test-doc',
    userId: 'test-user',
    username: 'Test User',
    title: 'Test Document'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('✅ Quill CSS is properly imported and applied', async () => {
    render(<SharedEditor {...defaultProps} />);

    // Wait for component to render
    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Wait for Quill structure to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();
    });

    // Check if Quill container exists (using our mock structure)
    const quillContainer = document.querySelector('.ql-container');
    expect(quillContainer).toBeInTheDocument();

    // Check if Quill editor exists
    const quillEditor = document.querySelector('.ql-editor');
    expect(quillEditor).toBeInTheDocument();

    // Check if Quill toolbar exists
    const quillToolbar = document.querySelector('.ql-toolbar');
    expect(quillToolbar).toBeInTheDocument();
  });

  test('✅ Quill Snow theme styles are applied', async () => {
    render(<SharedEditor {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Check for Snow theme specific classes
    const snowToolbar = document.querySelector('.ql-snow.ql-toolbar, .ql-snow .ql-toolbar');
    expect(snowToolbar).toBeInTheDocument();

    // Check for toolbar buttons
    const toolbarButtons = document.querySelectorAll('.ql-toolbar button');
    expect(toolbarButtons.length).toBeGreaterThan(0);
  });

  test('✅ Quill editor is functional', async () => {
    const user = userEvent.setup();
    const onContentChange = jest.fn();

    render(<SharedEditor {...defaultProps} onContentChange={onContentChange} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Find the editor content area
    const editor = document.querySelector('.ql-editor');
    expect(editor).toBeInTheDocument();

    // Simulate typing in the editor
    fireEvent.focus(editor);
    fireEvent.input(editor, { target: { innerHTML: '<p>Hello World</p>' } });

    // Verify content change callback is called
    await waitFor(() => {
      expect(onContentChange).toHaveBeenCalled();
    });
  });

  test('✅ Quill toolbar interactions work', async () => {
    render(<SharedEditor {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Wait for Quill to initialize
    await waitFor(() => {
      expect(document.querySelector('.ql-toolbar')).toBeInTheDocument();
    });

    // Check for bold button - test existence and basic interaction
    const boldButton = document.querySelector('.ql-bold');
    if (boldButton) {
      expect(boldButton).toBeInTheDocument();
      expect(boldButton.className).toContain('ql-bold');

      // Test that button can be clicked without errors
      expect(() => {
        boldButton.click();
      }).not.toThrow();
    }

    // Check for italic button - test existence and basic interaction
    const italicButton = document.querySelector('.ql-italic');
    if (italicButton) {
      expect(italicButton).toBeInTheDocument();
      expect(italicButton.className).toContain('ql-italic');

      // Test that button can be clicked without errors
      expect(() => {
        italicButton.click();
      }).not.toThrow();
    }
  });

  test('✅ Quill integrates well with Ant Design', async () => {
    render(<SharedEditor {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Wait for Quill to render
    await waitFor(() => {
      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();
    });

    // Check that the editor is wrapped in an Ant Design Card
    const cardElement = document.querySelector('.ant-card');
    expect(cardElement).toBeInTheDocument();

    // Check that Quill container exists and has proper structure
    const quillContainer = document.querySelector('.ql-container');
    expect(quillContainer).toBeInTheDocument();

    // Verify that getComputedStyle works and returns expected mock values
    const computedStyle = window.getComputedStyle(quillContainer);
    expect(computedStyle).toBeDefined();
    expect(computedStyle.fontFamily).toBe('"Helvetica Neue", Helvetica, Arial, sans-serif');
    expect(computedStyle.fontSize).toBe('14px');
    expect(computedStyle.getPropertyValue).toBeDefined();
    expect(typeof computedStyle.getPropertyValue).toBe('function');
  });

  test('✅ Read-only mode works correctly', async () => {
    render(<SharedEditor {...defaultProps} readOnly={true} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Wait for Quill to render
    await waitFor(() => {
      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();
    });

    // Check for read-only indicator
    expect(screen.getByText('Read-only mode')).toBeInTheDocument();

    // Check that editor has read-only attribute (our mock sets contentEditable based on readOnly prop)
    const editor = document.querySelector('.ql-editor');
    expect(editor).toBeInTheDocument();

    // In our mock, readOnly=true should set contentEditable to false
    expect(editor.getAttribute('contenteditable')).toBe('false');
  });

  test('✅ Custom height is applied correctly', async () => {
    const customHeight = 400;
    render(<SharedEditor {...defaultProps} height={customHeight} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Check that custom height is applied
    const reactQuillWrapper = document.querySelector('.quill');
    if (reactQuillWrapper) {
      const style = reactQuillWrapper.getAttribute('style');
      expect(style).toContain(`height: ${customHeight}`);
    }
  });

  test('✅ No console errors during rendering', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

    render(<SharedEditor {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Test Document')).toBeInTheDocument();
    });

    // Verify no console errors were logged
    expect(consoleSpy).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });
});
