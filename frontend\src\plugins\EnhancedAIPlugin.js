import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Card, Tabs, Button, Spin, Alert, Badge, Tooltip } from 'antd';
import {
  BulbOutlined,
  LayoutOutlined,
  AppstoreOutlined,
  Bar<PERSON><PERSON>Outlined as AnalysisOutlined, // Using BarChartOutlined as replacement
  ReloadOutlined,
  CheckOutlined
} from '@ant-design/icons';
import aiDesignService from '../services/aiDesignService';

const { TabPane } = Tabs;

/**
 * Enhanced AI Plugin component for App Builder
 * Provides layout suggestions, component combinations, and app analysis
 */
const EnhancedAIPlugin = ({
  components = [],
  layouts = [],
  selectedComponent = null,
  onApplyLayoutSuggestion,
  onApplyComponentCombination,
  onComponentAdd,
  context = {}
}) => {
  // State management
  const [activeTab, setActiveTab] = useState('layout');
  const [layoutSuggestions, setLayoutSuggestions] = useState([]);
  const [combinationSuggestions, setCombinationSuggestions] = useState([]);
  const [appAnalysis, setAppAnalysis] = useState(null);
  const [loading, setLoading] = useState({
    layout: false,
    combinations: false,
    analysis: false
  });
  const [error, setError] = useState(null);
  const [appliedSuggestions, setAppliedSuggestions] = useState(new Set());

  // Load suggestions when components change
  useEffect(() => {
    if (components.length > 0) {
      loadLayoutSuggestions();
      loadCombinationSuggestions();
      loadAppAnalysis();
    }
  }, [components, selectedComponent]);

  // Load layout suggestions
  const loadLayoutSuggestions = useCallback(async () => {
    setLoading(prev => ({ ...prev, layout: true }));
    setError(null);

    try {
      const response = await aiDesignService.generateLayoutSuggestions(
        components,
        layouts,
        context
      );
      setLayoutSuggestions(response.suggestions || []);
    } catch (err) {
      setError(`Failed to load layout suggestions: ${err.message}`);
    } finally {
      setLoading(prev => ({ ...prev, layout: false }));
    }
  }, [components, layouts, context]);

  // Load component combination suggestions
  const loadCombinationSuggestions = useCallback(async () => {
    setLoading(prev => ({ ...prev, combinations: true }));
    setError(null);

    try {
      const response = await aiDesignService.generateComponentCombinations(
        components,
        selectedComponent,
        context
      );
      setCombinationSuggestions(response.suggestions || []);
    } catch (err) {
      setError(`Failed to load combination suggestions: ${err.message}`);
    } finally {
      setLoading(prev => ({ ...prev, combinations: false }));
    }
  }, [components, selectedComponent, context]);

  // Load app analysis
  const loadAppAnalysis = useCallback(async () => {
    setLoading(prev => ({ ...prev, analysis: true }));

    try {
      const response = await aiDesignService.analyzeAppStructure(components, layouts);
      setAppAnalysis(response.analysis || null);
    } catch (err) {
      console.warn('Failed to load app analysis:', err);
    } finally {
      setLoading(prev => ({ ...prev, analysis: false }));
    }
  }, [components, layouts]);

  // Handle applying layout suggestion
  const handleApplyLayoutSuggestion = (suggestion) => {
    if (onApplyLayoutSuggestion) {
      onApplyLayoutSuggestion(suggestion);
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
    }
  };

  // Handle applying component combination
  const handleApplyComponentCombination = (suggestion) => {
    if (onApplyComponentCombination) {
      onApplyComponentCombination(suggestion);
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
    } else if (onComponentAdd && suggestion.missing_components) {
      // Fallback: add missing components one by one
      suggestion.missing_components.forEach(componentType => {
        onComponentAdd(componentType);
      });
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
    }
  };

  // Refresh all suggestions
  const handleRefresh = () => {
    aiDesignService.clearCache();
    loadLayoutSuggestions();
    loadCombinationSuggestions();
    loadAppAnalysis();
  };

  // Render layout suggestions tab
  const renderLayoutSuggestions = () => (
    <div style={{ padding: '8px 0' }}>
      {loading.layout ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip="Analyzing your app structure..." />
        </div>
      ) : (
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {layoutSuggestions.map((suggestion) => (
            <Card
              key={suggestion.id}
              size="small"
              style={{ marginBottom: '12px' }}
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>{suggestion.name}</span>
                  <Badge count={suggestion.score} style={{ backgroundColor: '#52c41a' }} />
                </div>
              }
              extra={
                <Button
                  type={appliedSuggestions.has(suggestion.id) ? 'default' : 'primary'}
                  size="small"
                  icon={appliedSuggestions.has(suggestion.id) ? <CheckOutlined /> : <LayoutOutlined />}
                  onClick={() => handleApplyLayoutSuggestion(suggestion)}
                  disabled={appliedSuggestions.has(suggestion.id)}
                >
                  {appliedSuggestions.has(suggestion.id) ? 'Applied' : 'Apply'}
                </Button>
              }
            >
              <p style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#666' }}>
                {suggestion.description}
              </p>
              <p style={{ margin: 0, fontSize: '11px', fontStyle: 'italic' }}>
                {suggestion.explanation}
              </p>
            </Card>
          ))}
          {layoutSuggestions.length === 0 && (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              No layout suggestions available. Add more components to get suggestions.
            </div>
          )}
        </div>
      )}
    </div>
  );

  // Render component combinations tab
  const renderComponentCombinations = () => (
    <div style={{ padding: '8px 0' }}>
      {loading.combinations ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip="Finding component combinations..." />
        </div>
      ) : (
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {combinationSuggestions.map((suggestion) => (
            <Card
              key={suggestion.id}
              size="small"
              style={{ marginBottom: '12px' }}
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <span>{suggestion.name}</span>
                  <Badge count={suggestion.score} style={{ backgroundColor: '#1890ff' }} />
                </div>
              }
              extra={
                <Button
                  type={appliedSuggestions.has(suggestion.id) ? 'default' : 'primary'}
                  size="small"
                  icon={appliedSuggestions.has(suggestion.id) ? <CheckOutlined /> : <AppstoreOutlined />}
                  onClick={() => handleApplyComponentCombination(suggestion)}
                  disabled={appliedSuggestions.has(suggestion.id)}
                >
                  {appliedSuggestions.has(suggestion.id) ? 'Applied' : 'Add'}
                </Button>
              }
            >
              <p style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#666' }}>
                {suggestion.description}
              </p>
              {suggestion.missing_components && suggestion.missing_components.length > 0 && (
                <p style={{ margin: '0 0 8px 0', fontSize: '11px' }}>
                  <strong>Missing:</strong> {suggestion.missing_components.join(', ')}
                </p>
              )}
              <p style={{ margin: 0, fontSize: '11px', fontStyle: 'italic' }}>
                {suggestion.explanation}
              </p>
            </Card>
          ))}
          {combinationSuggestions.length === 0 && (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              No combination suggestions available. Select a component to get targeted suggestions.
            </div>
          )}
        </div>
      )}
    </div>
  );

  // Render app analysis tab
  const renderAppAnalysis = () => (
    <div style={{ padding: '8px 0' }}>
      {loading.analysis ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin tip="Analyzing app structure..." />
        </div>
      ) : appAnalysis ? (
        <div>
          <Card size="small" style={{ marginBottom: '12px' }}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
              <div>
                <strong>Components:</strong> {appAnalysis.component_count}
              </div>
              <div>
                <strong>App Type:</strong> {appAnalysis.app_type}
              </div>
              <div>
                <strong>Complexity:</strong> {appAnalysis.complexity_score}
              </div>
              <div>
                <strong>Has Navigation:</strong> {appAnalysis.has_navigation ? 'Yes' : 'No'}
              </div>
            </div>
          </Card>

          {Object.keys(appAnalysis.component_types).length > 0 && (
            <Card size="small" title="Component Types">
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {Object.entries(appAnalysis.component_types).map(([type, count]) => (
                  <Badge key={type} count={count} style={{ backgroundColor: '#722ed1' }}>
                    <span style={{ padding: '4px 8px', background: '#f0f0f0', borderRadius: '4px' }}>
                      {type}
                    </span>
                  </Badge>
                ))}
              </div>
            </Card>
          )}
        </div>
      ) : (
        <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
          No analysis data available.
        </div>
      )}
    </div>
  );

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>
            <BulbOutlined style={{ marginRight: '8px' }} />
            AI Design Assistant
          </span>
          <Tooltip title="Refresh suggestions">
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading.layout || loading.combinations || loading.analysis}
            />
          </Tooltip>
        </div>
      }
      size="small"
      style={{ width: '100%' }}
    >
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          closable
          style={{ marginBottom: '16px' }}
          onClose={() => setError(null)}
        />
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
        <TabPane
          tab={
            <span>
              <LayoutOutlined />
              Layouts
            </span>
          }
          key="layout"
        >
          {renderLayoutSuggestions()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <AppstoreOutlined />
              Components
            </span>
          }
          key="combinations"
        >
          {renderComponentCombinations()}
        </TabPane>

        <TabPane
          tab={
            <span>
              <AnalysisOutlined />
              Analysis
            </span>
          }
          key="analysis"
        >
          {renderAppAnalysis()}
        </TabPane>
      </Tabs>
    </Card>
  );
};

EnhancedAIPlugin.propTypes = {
  components: PropTypes.array,
  layouts: PropTypes.array,
  selectedComponent: PropTypes.object,
  onApplyLayoutSuggestion: PropTypes.func,
  onApplyComponentCombination: PropTypes.func,
  onComponentAdd: PropTypes.func,
  context: PropTypes.object,
};

export default EnhancedAIPlugin;
