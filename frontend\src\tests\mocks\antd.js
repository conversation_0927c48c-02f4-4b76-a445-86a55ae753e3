/**
 * Comprehensive Ant Design mock for testing
 * Provides all necessary components and utilities to prevent CSS-in-JS errors
 */

import React from 'react';

// Mock theme utilities
const mockTheme = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    colorText: '#000000d9',
    colorTextSecondary: '#00000073',
    colorBgBase: '#ffffff',
    borderRadius: 6,
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial',
  },
  components: {},
  cssVar: {},
};

// Mock CSS-in-JS utilities
const mockCSSInJS = {
  createTheme: jest.fn(() => mockTheme),
  StyleProvider: ({ children }) => children,
  createCache: jest.fn(() => ({})),
  extractStyle: jest.fn(() => ''),
  theme: {
    defaultConfig: mockTheme,
    useToken: jest.fn(() => [mockTheme.token]),
  },
};

// Mock all Ant Design components
const createMockComponent = (name, defaultProps = {}) => {
  const MockComponent = React.forwardRef((props, ref) => {
    const { children, className = '', style = {}, ...otherProps } = props;
    return React.createElement(
      'div',
      {
        ref,
        className: `ant-${name.toLowerCase()} ${className}`.trim(),
        style,
        'data-testid': `ant-${name.toLowerCase()}`,
        ...otherProps,
      },
      children
    );
  });
  MockComponent.displayName = `Mock${name}`;
  return MockComponent;
};

// Helper functions for creating special components
const createConfigProvider = () => ({ children, theme, ...props }) => {
  return React.createElement('div', { 'data-testid': 'config-provider', ...props }, children);
};

const createApp = () => ({ children, ...props }) => {
  return React.createElement('div', { 'data-testid': 'ant-app', ...props }, children);
};

// Custom Card component that properly handles title prop
const createCard = () => ({ children, title, extra, ...props }) => {
  return React.createElement('div', {
    'data-testid': 'ant-card',
    className: 'ant-card',
    ...props
  }, [
    title && React.createElement('div', {
      key: 'title',
      className: 'ant-card-head',
      style: { marginBottom: '8px' }
    }, title),
    extra && React.createElement('div', {
      key: 'extra',
      className: 'ant-card-extra',
      style: { marginBottom: '8px' }
    }, extra),
    React.createElement('div', {
      key: 'body',
      className: 'ant-card-body'
    }, children)
  ].filter(Boolean));
};

// Create all components and store them in the mockComponents object
const mockComponents = {
  // Layout
  Layout: createMockComponent('Layout'),

  // Navigation
  Affix: createMockComponent('Affix'),
  Breadcrumb: createMockComponent('Breadcrumb'),
  Dropdown: createMockComponent('Dropdown'),
  Menu: createMockComponent('Menu'),
  Pagination: createMockComponent('Pagination'),
  Steps: createMockComponent('Steps'),

  // Data Entry
  AutoComplete: createMockComponent('AutoComplete'),
  Cascader: createMockComponent('Cascader'),
  Checkbox: createMockComponent('Checkbox'),
  DatePicker: createMockComponent('DatePicker'),
  Form: createMockComponent('Form'),
  Input: createMockComponent('Input'),
  InputNumber: createMockComponent('InputNumber'),
  Mentions: createMockComponent('Mentions'),
  Radio: createMockComponent('Radio'),
  Rate: createMockComponent('Rate'),
  Select: createMockComponent('Select'),
  Slider: createMockComponent('Slider'),
  Switch: createMockComponent('Switch'),
  TimePicker: createMockComponent('TimePicker'),
  Transfer: createMockComponent('Transfer'),
  TreeSelect: createMockComponent('TreeSelect'),
  Upload: createMockComponent('Upload'),

  // Data Display
  Avatar: createMockComponent('Avatar'),
  Badge: createMockComponent('Badge'),
  Calendar: createMockComponent('Calendar'),
  Card: createCard(),
  Carousel: createMockComponent('Carousel'),
  Collapse: createMockComponent('Collapse'),
  Comment: createMockComponent('Comment'),
  Descriptions: createMockComponent('Descriptions'),
  Empty: createMockComponent('Empty'),
  Image: createMockComponent('Image'),
  List: createMockComponent('List'),
  Popover: createMockComponent('Popover'),
  QRCode: createMockComponent('QRCode'),
  Segmented: createMockComponent('Segmented'),
  Statistic: createMockComponent('Statistic'),
  Table: createMockComponent('Table'),
  Tabs: createMockComponent('Tabs'),
  Tag: createMockComponent('Tag'),
  Timeline: createMockComponent('Timeline'),
  Tooltip: createMockComponent('Tooltip'),
  Tour: createMockComponent('Tour'),
  Tree: createMockComponent('Tree'),

  // Feedback
  Alert: createMockComponent('Alert'),
  Drawer: createMockComponent('Drawer'),
  Modal: createMockComponent('Modal'),
  Popconfirm: createMockComponent('Popconfirm'),
  Progress: createMockComponent('Progress'),
  Result: createMockComponent('Result'),
  Skeleton: createMockComponent('Skeleton'),
  Spin: createMockComponent('Spin'),
  Watermark: createMockComponent('Watermark'),

  // General
  Button: createMockComponent('Button'),
  FloatButton: createMockComponent('FloatButton'),
  Icon: createMockComponent('Icon'),
  Typography: createMockComponent('Typography'),

  // Other
  Anchor: createMockComponent('Anchor'),
  BackTop: createMockComponent('BackTop'),
  ConfigProvider: createConfigProvider(),
  Divider: createMockComponent('Divider'),
  Flex: createMockComponent('Flex'),
  Grid: createMockComponent('Grid'),
  Space: createMockComponent('Space'),
  App: createApp(),

  // Theme
  theme: mockTheme,
};

// Add nested components
// Layout components
mockComponents.Layout.Header = createMockComponent('Layout-Header');
mockComponents.Layout.Content = createMockComponent('Layout-Content');
mockComponents.Layout.Footer = createMockComponent('Layout-Footer');
mockComponents.Layout.Sider = createMockComponent('Layout-Sider');

// Form components
mockComponents.Form.Item = createMockComponent('Form-Item');
mockComponents.Form.List = createMockComponent('Form-List');
mockComponents.Form.Provider = createMockComponent('Form-Provider');

// Menu components
mockComponents.Menu.Item = createMockComponent('Menu-Item');
mockComponents.Menu.SubMenu = createMockComponent('Menu-SubMenu');
mockComponents.Menu.ItemGroup = createMockComponent('Menu-ItemGroup');
mockComponents.Menu.Divider = createMockComponent('Menu-Divider');

// Table components
mockComponents.Table.Column = createMockComponent('Table-Column');
mockComponents.Table.ColumnGroup = createMockComponent('Table-ColumnGroup');

// Input components
mockComponents.Input.TextArea = createMockComponent('Input-TextArea');
mockComponents.Input.Search = createMockComponent('Input-Search');
mockComponents.Input.Group = createMockComponent('Input-Group');
mockComponents.Input.Password = createMockComponent('Input-Password');

// Select components
mockComponents.Select.Option = createMockComponent('Select-Option');
mockComponents.Select.OptGroup = createMockComponent('Select-OptGroup');

// Date picker components
mockComponents.DatePicker.RangePicker = createMockComponent('DatePicker-RangePicker');
mockComponents.DatePicker.MonthPicker = createMockComponent('DatePicker-MonthPicker');
mockComponents.DatePicker.WeekPicker = createMockComponent('DatePicker-WeekPicker');
mockComponents.DatePicker.YearPicker = createMockComponent('DatePicker-YearPicker');

// Upload component
mockComponents.Upload.Dragger = createMockComponent('Upload-Dragger');

// Tree components
mockComponents.Tree.TreeNode = createMockComponent('Tree-TreeNode');
mockComponents.Tree.DirectoryTree = createMockComponent('Tree-DirectoryTree');

// Collapse components
mockComponents.Collapse.Panel = createMockComponent('Collapse-Panel');

// Tabs components
mockComponents.Tabs.TabPane = createMockComponent('Tabs-TabPane');

// Steps components
mockComponents.Steps.Step = createMockComponent('Steps-Step');

// List components
mockComponents.List.Item = createMockComponent('List-Item');
mockComponents.List.Item.Meta = createMockComponent('List-Item-Meta');

// Card components
mockComponents.Card.Grid = createMockComponent('Card-Grid');
mockComponents.Card.Meta = createMockComponent('Card-Meta');

// Avatar components
mockComponents.Avatar.Group = createMockComponent('Avatar-Group');

// Descriptions components
mockComponents.Descriptions.Item = createMockComponent('Descriptions-Item');

// Statistic components
mockComponents.Statistic.Countdown = createMockComponent('Statistic-Countdown');

// Skeleton components
mockComponents.Skeleton.Avatar = createMockComponent('Skeleton-Avatar');
mockComponents.Skeleton.Button = createMockComponent('Skeleton-Button');
mockComponents.Skeleton.Image = createMockComponent('Skeleton-Image');
mockComponents.Skeleton.Input = createMockComponent('Skeleton-Input');
mockComponents.Skeleton.Node = createMockComponent('Skeleton-Node');

// Typography components
mockComponents.Typography.Text = createMockComponent('Typography-Text');
mockComponents.Typography.Title = createMockComponent('Typography-Title');
mockComponents.Typography.Paragraph = createMockComponent('Typography-Paragraph');
mockComponents.Typography.Link = createMockComponent('Typography-Link');

// Button components
mockComponents.Button.Group = createMockComponent('Button-Group');

// Radio components
mockComponents.Radio.Group = createMockComponent('Radio-Group');
mockComponents.Radio.Button = createMockComponent('Radio-Button');

// Checkbox components
mockComponents.Checkbox.Group = createMockComponent('Checkbox-Group');

// Grid components
mockComponents.Grid.Row = createMockComponent('Grid-Row');
mockComponents.Grid.Col = createMockComponent('Grid-Col');

// Breadcrumb components
mockComponents.Breadcrumb.Item = createMockComponent('Breadcrumb-Item');
mockComponents.Breadcrumb.Separator = createMockComponent('Breadcrumb-Separator');

// Dropdown components
mockComponents.Dropdown.Button = createMockComponent('Dropdown-Button');

// Timeline components
mockComponents.Timeline.Item = createMockComponent('Timeline-Item');

// Anchor components
mockComponents.Anchor.Link = createMockComponent('Anchor-Link');

// Mock message and notification APIs
mockComponents.message = {
  success: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  warning: jest.fn(),
  warn: jest.fn(),
  loading: jest.fn(),
  open: jest.fn(),
  config: jest.fn(),
  destroy: jest.fn(),
};

mockComponents.notification = {
  success: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  warning: jest.fn(),
  warn: jest.fn(),
  open: jest.fn(),
  close: jest.fn(),
  config: jest.fn(),
  destroy: jest.fn(),
};

mockComponents.Modal.confirm = jest.fn();
mockComponents.Modal.info = jest.fn();
mockComponents.Modal.success = jest.fn();
mockComponents.Modal.error = jest.fn();
mockComponents.Modal.warning = jest.fn();
mockComponents.Modal.warn = jest.fn();

// Export everything
export default mockComponents;

// Individual exports
export const Layout = mockComponents.Layout;
export const Form = mockComponents.Form;
export const Menu = mockComponents.Menu;
export const Table = mockComponents.Table;
export const Input = mockComponents.Input;
export const Select = mockComponents.Select;
export const DatePicker = mockComponents.DatePicker;
export const Upload = mockComponents.Upload;
export const Tree = mockComponents.Tree;
export const Collapse = mockComponents.Collapse;
export const Tabs = mockComponents.Tabs;
export const Steps = mockComponents.Steps;
export const Carousel = mockComponents.Carousel;
export const List = mockComponents.List;
export const Card = mockComponents.Card;
export const Avatar = mockComponents.Avatar;
export const Comment = mockComponents.Comment;
export const Descriptions = mockComponents.Descriptions;
export const Empty = mockComponents.Empty;
export const Statistic = mockComponents.Statistic;
export const Result = mockComponents.Result;
export const Skeleton = mockComponents.Skeleton;
export const Spin = mockComponents.Spin;
export const ConfigProvider = mockComponents.ConfigProvider;
export const App = mockComponents.App;
export const Affix = mockComponents.Affix;
export const Breadcrumb = mockComponents.Breadcrumb;
export const Dropdown = mockComponents.Dropdown;
export const Pagination = mockComponents.Pagination;
export const AutoComplete = mockComponents.AutoComplete;
export const Cascader = mockComponents.Cascader;
export const Checkbox = mockComponents.Checkbox;
export const InputNumber = mockComponents.InputNumber;
export const Mentions = mockComponents.Mentions;
export const Radio = mockComponents.Radio;
export const Rate = mockComponents.Rate;
export const Slider = mockComponents.Slider;
export const Switch = mockComponents.Switch;
export const TimePicker = mockComponents.TimePicker;
export const Transfer = mockComponents.Transfer;
export const TreeSelect = mockComponents.TreeSelect;
export const Badge = mockComponents.Badge;
export const Calendar = mockComponents.Calendar;
export const Image = mockComponents.Image;
export const Popover = mockComponents.Popover;
export const QRCode = mockComponents.QRCode;
export const Segmented = mockComponents.Segmented;
export const Tag = mockComponents.Tag;
export const Timeline = mockComponents.Timeline;
export const Tooltip = mockComponents.Tooltip;
export const Tour = mockComponents.Tour;
export const Alert = mockComponents.Alert;
export const Drawer = mockComponents.Drawer;
export const Modal = mockComponents.Modal;
export const Popconfirm = mockComponents.Popconfirm;
export const Progress = mockComponents.Progress;
export const Watermark = mockComponents.Watermark;
export const Button = mockComponents.Button;
export const FloatButton = mockComponents.FloatButton;
export const Icon = mockComponents.Icon;
export const Typography = mockComponents.Typography;
export const Anchor = mockComponents.Anchor;
export const BackTop = mockComponents.BackTop;
export const Divider = mockComponents.Divider;
export const Flex = mockComponents.Flex;
export const Grid = mockComponents.Grid;
export const Space = mockComponents.Space;
export const theme = mockComponents.theme;
export const message = mockComponents.message;
export const notification = mockComponents.notification;
