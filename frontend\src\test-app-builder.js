/**
 * Test script to verify App Builder functionality
 * Run this in the browser console to test component builder features
 */

// Test function to verify <PERSON>pp Builder is working
function testAppBuilder() {
  console.log('🧪 Testing App Builder functionality...');
  
  const results = {
    reactLoaded: false,
    appLoaded: false,
    integratedBuilderAvailable: false,
    componentBuilderWorking: false,
    layoutDesignerWorking: false,
    errors: []
  };

  try {
    // Test 1: Check if React is loaded
    if (typeof window.React !== 'undefined') {
      results.reactLoaded = true;
      console.log('✅ React is loaded:', window.React.version);
    } else {
      results.errors.push('React not loaded globally');
      console.error('❌ React not loaded globally');
    }

    // Test 2: Check if app is loaded
    if (window.__APP_LOADED__) {
      results.appLoaded = true;
      console.log('✅ App is loaded');
    } else {
      results.errors.push('App not fully loaded');
      console.error('❌ App not fully loaded');
    }

    // Test 3: Check if IntegratedAppBuilder is available
    const rootElement = document.getElementById('root');
    if (rootElement && rootElement.innerHTML.includes('Enhanced App Builder')) {
      results.integratedBuilderAvailable = true;
      console.log('✅ IntegratedAppBuilder is available');
    } else {
      results.errors.push('IntegratedAppBuilder not found in DOM');
      console.log('⚠️ IntegratedAppBuilder not found, checking for fallback...');
    }

    // Test 4: Check for component builder elements
    const componentPalette = document.querySelector('[data-testid="component-palette"]') || 
                            document.querySelector('.component-palette') ||
                            document.querySelector('h3:contains("Components")');
    
    if (componentPalette || document.body.textContent.includes('Components')) {
      results.componentBuilderWorking = true;
      console.log('✅ Component builder elements found');
    } else {
      results.errors.push('Component builder elements not found');
      console.error('❌ Component builder elements not found');
    }

    // Test 5: Check for layout designer elements
    const canvasArea = document.querySelector('[data-testid="canvas-area"]') || 
                      document.querySelector('.canvas-area') ||
                      document.querySelector('h3:contains("Canvas")');
    
    if (canvasArea || document.body.textContent.includes('Canvas')) {
      results.layoutDesignerWorking = true;
      console.log('✅ Layout designer elements found');
    } else {
      results.errors.push('Layout designer elements not found');
      console.error('❌ Layout designer elements not found');
    }

    // Test 6: Check for any JavaScript errors
    const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
    if (errorElements.length > 0) {
      results.errors.push(`Found ${errorElements.length} error elements in DOM`);
      console.warn('⚠️ Found error elements:', errorElements);
    }

  } catch (error) {
    results.errors.push(`Test execution error: ${error.message}`);
    console.error('❌ Test execution error:', error);
  }

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('React Loaded:', results.reactLoaded ? '✅' : '❌');
  console.log('App Loaded:', results.appLoaded ? '✅' : '❌');
  console.log('IntegratedAppBuilder Available:', results.integratedBuilderAvailable ? '✅' : '❌');
  console.log('Component Builder Working:', results.componentBuilderWorking ? '✅' : '❌');
  console.log('Layout Designer Working:', results.layoutDesignerWorking ? '✅' : '❌');
  
  if (results.errors.length > 0) {
    console.log('\n🚨 Errors found:');
    results.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }

  const overallSuccess = results.reactLoaded && 
                        results.appLoaded && 
                        (results.componentBuilderWorking || results.layoutDesignerWorking) &&
                        results.errors.length === 0;

  console.log('\n🎯 Overall Status:', overallSuccess ? '✅ PASS' : '❌ FAIL');
  
  return results;
}

// Test function to simulate component addition
function testComponentAddition() {
  console.log('🧪 Testing component addition...');
  
  try {
    // Look for "Add Button" or similar buttons
    const addButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
      btn.textContent.includes('Add') || btn.textContent.includes('Button')
    );
    
    if (addButtons.length > 0) {
      console.log(`✅ Found ${addButtons.length} add component buttons`);
      
      // Try to click the first one
      const firstButton = addButtons[0];
      console.log('🖱️ Simulating click on:', firstButton.textContent);
      firstButton.click();
      
      // Wait a bit and check if anything changed
      setTimeout(() => {
        const canvasContent = document.querySelector('[data-testid="canvas-area"]') || 
                            document.querySelector('.canvas-area') ||
                            document.body;
        
        console.log('📊 Canvas content after click:', canvasContent.textContent.length, 'characters');
      }, 1000);
      
      return true;
    } else {
      console.error('❌ No add component buttons found');
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing component addition:', error);
    return false;
  }
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(testAppBuilder, 2000);
    });
  } else {
    setTimeout(testAppBuilder, 2000);
  }
}

// Export functions for manual testing
window.testAppBuilder = testAppBuilder;
window.testComponentAddition = testComponentAddition;

console.log('🔧 App Builder test functions loaded. Run testAppBuilder() or testComponentAddition() in console.');
