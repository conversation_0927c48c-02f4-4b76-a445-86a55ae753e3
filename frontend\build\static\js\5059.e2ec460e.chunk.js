"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5059],{

/***/ 77641:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ services_CollaborationService)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./src/services/EnhancedWebSocketClient.js
var EnhancedWebSocketClient = __webpack_require__(17177);
;// ./src/utils/websocket.js
/**
 * WebSocket utility functions for the App Builder application.
 */

/**
 * Get the WebSocket URL based on the current environment and endpoint.
 *
 * @param {string} endpoint - The WebSocket endpoint (e.g., 'app_builder', 'test')
 * @param {Object} options - Additional options
 * @param {boolean} options.forceSecure - Force secure WebSocket connection (wss://)
 * @returns {string} The WebSocket URL
 */
function getWebSocketUrl() {
  var endpoint = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$forceSecure = options.forceSecure,
    forceSecure = _options$forceSecure === void 0 ? false : _options$forceSecure;

  // Get the base URL from environment variables or use the current host
  var websocketUrl = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.WEBSOCKET_URL || '';
  if (websocketUrl) {
    // Use the configured WebSocket URL if available
    return "".concat(websocketUrl, "/").concat(endpoint).replace(/\/+$/, '');
  }

  // Determine protocol (ws:// or wss://)
  var isSecure = forceSecure || window.location.protocol === 'https:';
  var protocol = isSecure ? 'wss://' : 'ws://';

  // Use the current host and backend port
  var host = window.location.hostname;

  // Determine the port based on the environment
  var port;
  if ({"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_BACKEND_PORT) {
    port = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_BACKEND_PORT;
  } else if (window.location.port) {
    // If we're on a non-standard port, use 8000 for backend
    port = '8000';
  } else {
    // For standard ports (80/443), use the same port
    port = window.location.port || (isSecure ? '443' : '80');
  }

  // Construct the WebSocket URL
  var url;

  // For localhost development, connect directly to backend (bypass proxy issues)
  if (host === 'localhost' || host === '127.0.0.1') {
    // Connect directly to backend port 8000 to bypass proxy issues
    url = "".concat(protocol).concat(host, ":8000/ws");

    // Fallback options if the main URL doesn't work
    window.WEBSOCKET_FALLBACK_URLS = ["".concat(protocol).concat(host, ":8000/ws"), "/ws", "".concat(protocol).concat(host, ":").concat(port, "/ws"), "".concat(protocol, "127.0.0.1:8000/ws")];
  } else if (host === 'backend') {
    // Special case for Docker container-to-container communication
    url = "".concat(protocol).concat(host, ":8000/ws");
  } else {
    // For production, use the configured port
    url = "".concat(protocol).concat(host, ":").concat(port, "/ws");

    // Fallback options if the main URL doesn't work
    window.WEBSOCKET_FALLBACK_URLS = ["".concat(protocol).concat(host, ":").concat(port, "/ws"), "".concat(protocol).concat(host, ":8000/ws")];
  }

  // Log the WebSocket URL for debugging
  console.log("\uD83D\uDD0C Constructing WebSocket URL: ".concat(url));
  console.log("\uD83D\uDD0C Environment: NODE_ENV=".concat("production", ", host=").concat(host, ", port=").concat(window.location.port));

  // Add the endpoint if provided
  if (endpoint) {
    url += "/".concat(endpoint);
  }

  // Ensure the URL ends with a trailing slash for Django Channels
  if (!url.endsWith('/')) {
    url += '/';
  }
  console.log("\uD83D\uDD0C Final WebSocket URL: ".concat(url));
  return url;
}

/**
 * Create a WebSocket connection with automatic reconnection.
 *
 * @param {Object} options - WebSocket options
 * @param {string} options.url - The WebSocket URL
 * @param {boolean} options.autoReconnect - Whether to automatically reconnect
 * @param {number} options.reconnectInterval - Reconnection interval in milliseconds
 * @param {number} options.maxReconnectAttempts - Maximum number of reconnection attempts
 * @param {Function} options.onOpen - Callback when the connection is opened
 * @param {Function} options.onMessage - Callback when a message is received
 * @param {Function} options.onClose - Callback when the connection is closed
 * @param {Function} options.onError - Callback when an error occurs
 * @returns {WebSocket} The WebSocket instance
 */
function createWebSocket(options) {
  var url = options.url,
    _options$autoReconnec = options.autoReconnect,
    autoReconnect = _options$autoReconnec === void 0 ? true : _options$autoReconnec,
    _options$reconnectInt = options.reconnectInterval,
    reconnectInterval = _options$reconnectInt === void 0 ? 1000 : _options$reconnectInt,
    _options$maxReconnect = options.maxReconnectAttempts,
    maxReconnectAttempts = _options$maxReconnect === void 0 ? 5 : _options$maxReconnect,
    onOpen = options.onOpen,
    onMessage = options.onMessage,
    onClose = options.onClose,
    onError = options.onError;
  var reconnectAttempts = 0;
  var socket = null;

  // Create the WebSocket connection
  function connect() {
    try {
      var _window$WebSocket, _window$WebSocket2;
      // Close existing connection if any
      if (socket) {
        socket.close();
      }

      // Log WebSocket class information for debugging
      console.log('🔌 Creating WebSocket connection...');
      console.log('🔌 WebSocket class:', ((_window$WebSocket = window.WebSocket) === null || _window$WebSocket === void 0 ? void 0 : _window$WebSocket.name) || 'WebSocket');
      console.log('🔌 Is mock WebSocket?', ((_window$WebSocket2 = window.WebSocket) === null || _window$WebSocket2 === void 0 ? void 0 : _window$WebSocket2.name) === 'MockWebSocket');
      console.log('🔌 URL:', url);

      // Create new WebSocket connection
      socket = new WebSocket(url);

      // Log the created socket for debugging
      console.log('🔌 WebSocket instance created:', socket.constructor.name);

      // Connection opened
      socket.onopen = function (event) {
        console.log("WebSocket connected to ".concat(url));
        reconnectAttempts = 0;

        // Call the onOpen callback if provided
        if (typeof onOpen === 'function') {
          onOpen(event);
        }
      };

      // Listen for messages
      socket.onmessage = function (event) {
        // Call the onMessage callback if provided
        if (typeof onMessage === 'function') {
          onMessage(event);
        }
      };

      // Connection closed
      socket.onclose = function (event) {
        console.log("WebSocket connection closed: ".concat(event.code, " ").concat(event.reason));

        // Call the onClose callback if provided
        if (typeof onClose === 'function') {
          onClose(event);
        }

        // Reconnect if enabled and not a normal closure
        if (autoReconnect && event.code !== 1000 && event.code !== 1001) {
          reconnect();
        }
      };

      // Connection error
      socket.onerror = function (event) {
        console.error('WebSocket error:', event);

        // Call the onError callback if provided
        if (typeof onError === 'function') {
          onError(event);
        }
      };
      return socket;
    } catch (error) {
      console.error('Error creating WebSocket:', error);

      // Call the onError callback if provided
      if (typeof onError === 'function') {
        onError(error);
      }

      // Reconnect if enabled
      if (autoReconnect) {
        reconnect();
      }
      return null;
    }
  }

  // Reconnect to the WebSocket server
  function reconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.log("Maximum reconnection attempts (".concat(maxReconnectAttempts, ") reached"));
      return;
    }
    reconnectAttempts++;
    var delay = reconnectInterval * Math.pow(1.5, reconnectAttempts - 1);
    console.log("Reconnecting to WebSocket in ".concat(delay, "ms (attempt ").concat(reconnectAttempts, "/").concat(maxReconnectAttempts, ")"));
    setTimeout(connect, delay);
  }

  // Initial connection
  connect();

  // Return the WebSocket instance and control functions
  return {
    socket: socket,
    reconnect: reconnect,
    close: function close() {
      if (socket) {
        socket.close(1000, 'User initiated disconnect');
      }
    },
    send: function send(data) {
      if (socket && socket.readyState === WebSocket.OPEN) {
        var message = typeof data === 'string' ? data : JSON.stringify(data);
        socket.send(message);
      } else {
        console.error('Cannot send message: WebSocket is not connected');
      }
    }
  };
}

/**
 * Check if WebSockets are supported in the current browser.
 *
 * @returns {boolean} Whether WebSockets are supported
 */
function isWebSocketSupported() {
  return 'WebSocket' in window;
}

/**
 * Handle WebSocket errors and provide helpful messages.
 *
 * @param {Error} error - The WebSocket error
 * @returns {string} A user-friendly error message
 */
function getWebSocketErrorMessage(error) {
  if (!isWebSocketSupported()) {
    return 'WebSockets are not supported in your browser. Please upgrade to a modern browser.';
  }
  if (error instanceof Event) {
    return 'A WebSocket error occurred. Please check your connection and try again.';
  }
  if (error instanceof DOMException && error.name === 'SecurityError') {
    return 'WebSocket connection blocked due to security restrictions. Please check your browser settings.';
  }
  return error.message || 'An unknown WebSocket error occurred.';
}
;// ./src/services/CollaborationService.js




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * Real-time Collaboration Service
 * 
 * Handles real-time collaborative features for the App Builder
 */



var CollaborationService = /*#__PURE__*/function () {
  function CollaborationService() {
    (0,classCallCheck/* default */.A)(this, CollaborationService);
    this.wsClient = null;
    this.isConnected = false;
    this.currentDocument = null;
    this.collaborators = new Map();
    this.eventListeners = new Map();
    this.operationQueue = [];
    this.isProcessingOperations = false;
    this.debug = "production" === 'development';
  }

  /**
   * Initialize collaboration for a session
   * @param {string} sessionId - The collaboration session ID
   * @param {Object} user - Current user information
   */
  return (0,createClass/* default */.A)(CollaborationService, [{
    key: "initializeCollaboration",
    value: (function () {
      var _initializeCollaboration = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(sessionId, user) {
        var protocol, wsUrl, _t;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              this.currentDocument = sessionId;

              // Create WebSocket connection for collaboration
              protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
              wsUrl = "".concat(protocol, "//").concat(window.location.host, "/ws/collaboration/").concat(sessionId, "/");
              this.wsClient = new EnhancedWebSocketClient["default"]({
                url: wsUrl,
                autoConnect: true,
                autoReconnect: true,
                debug: this.debug,
                heartbeatInterval: 30000
              });

              // Set up event listeners
              this._setupEventListeners();

              // Join the collaboration session
              _context.next = 1;
              return this._joinSession(sessionId, user);
            case 1:
              this.isConnected = true;
              this._log('Collaboration initialized for session:', sessionId);
              _context.next = 3;
              break;
            case 2:
              _context.prev = 2;
              _t = _context["catch"](0);
              this._error('Failed to initialize collaboration:', _t);
              throw _t;
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[0, 2]]);
      }));
      function initializeCollaboration(_x, _x2) {
        return _initializeCollaboration.apply(this, arguments);
      }
      return initializeCollaboration;
    }()
    /**
     * Join a collaboration session
     * @param {string} sessionId - Session ID
     * @param {Object} user - User information
     */
    )
  }, {
    key: "_joinSession",
    value: (function () {
      var _joinSession2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(sessionId, user) {
        var _this = this;
        return regenerator_default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              return _context2.abrupt("return", new Promise(function (resolve, reject) {
                var timeout = setTimeout(function () {
                  reject(new Error('Join session timeout'));
                }, 5000);

                // Listen for join confirmation
                var _handleJoinResponse = function handleJoinResponse(data) {
                  if (data.type === 'session_joined' && data.session_id === sessionId) {
                    clearTimeout(timeout);
                    _this.removeEventListener('message', _handleJoinResponse);
                    _this.collaborators = new Map((data.data.participants || []).map(function (p) {
                      return [p.user_id, p];
                    }));
                    resolve(data);
                  }
                };
                _this.addEventListener('message', _handleJoinResponse);

                // Send join request
                _this.wsClient.send({
                  type: 'join_session',
                  session_id: sessionId
                });
              }));
            case 1:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
      function _joinSession(_x3, _x4) {
        return _joinSession2.apply(this, arguments);
      }
      return _joinSession;
    }()
    /**
     * Leave the current collaboration session
     */
    )
  }, {
    key: "leaveSession",
    value: (function () {
      var _leaveSession = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
        return regenerator_default().wrap(function (_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (this.currentDocument && this.wsClient) {
                this.wsClient.send({
                  type: 'leave_session'
                });
              }
              this.currentDocument = null;
              this.collaborators.clear();
              this.operationQueue = [];
            case 1:
            case "end":
              return _context3.stop();
          }
        }, _callee3, this);
      }));
      function leaveSession() {
        return _leaveSession.apply(this, arguments);
      }
      return leaveSession;
    }()
    /**
     * Send a real-time operation (like text changes, component updates)
     * @param {Object} operation - The operation to send
     */
    )
  }, {
    key: "sendOperation",
    value: function sendOperation(operation) {
      if (!this.isConnected || !this.currentDocument) {
        this._log('Cannot send operation: not connected or no document');
        return;
      }
      var operationData = {
        type: 'operation',
        documentId: this.currentDocument,
        operation: _objectSpread(_objectSpread({}, operation), {}, {
          timestamp: Date.now(),
          id: this._generateOperationId()
        })
      };
      this.wsClient.send(operationData);
      this._log('Operation sent:', operationData);
    }

    /**
     * Send cursor position update
     * @param {Object} cursor - Cursor position data
     */
  }, {
    key: "sendCursorUpdate",
    value: function sendCursorUpdate(cursor) {
      if (!this.isConnected || !this.currentDocument) return;
      this.wsClient.send({
        type: 'cursor_update',
        documentId: this.currentDocument,
        cursor: _objectSpread(_objectSpread({}, cursor), {}, {
          timestamp: Date.now()
        })
      });
    }

    /**
     * Send user presence update
     * @param {Object} presence - Presence data
     */
  }, {
    key: "sendPresenceUpdate",
    value: function sendPresenceUpdate(presence) {
      if (!this.isConnected || !this.currentDocument) return;
      this.wsClient.send({
        type: 'presence_update',
        presence: _objectSpread(_objectSpread({}, presence), {}, {
          timestamp: Date.now()
        })
      });
    }

    /**
     * Create a comment
     * @param {string} content - Comment content
     * @param {string} componentId - Component ID (optional)
     * @param {Object} canvasPosition - Canvas position (optional)
     * @param {string} parentId - Parent comment ID for replies (optional)
     */
  }, {
    key: "createComment",
    value: function createComment(content) {
      var componentId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var canvasPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var parentId = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
      if (!this.isConnected || !this.currentDocument) {
        throw new Error('Not connected to collaboration session');
      }
      this.wsClient.send({
        type: 'create_comment',
        content: content,
        component_id: componentId,
        canvas_position: canvasPosition,
        parent_id: parentId
      });
    }

    /**
     * Update a comment
     * @param {string} commentId - Comment ID
     * @param {string} content - New content
     */
  }, {
    key: "updateComment",
    value: function updateComment(commentId, content) {
      if (!this.isConnected || !this.currentDocument) {
        throw new Error('Not connected to collaboration session');
      }
      this.wsClient.send({
        type: 'update_comment',
        comment_id: commentId,
        content: content
      });
    }

    /**
     * Resolve a comment
     * @param {string} commentId - Comment ID
     */
  }, {
    key: "resolveComment",
    value: function resolveComment(commentId) {
      if (!this.isConnected || !this.currentDocument) {
        throw new Error('Not connected to collaboration session');
      }
      this.wsClient.send({
        type: 'resolve_comment',
        comment_id: commentId
      });
    }

    /**
     * Send a component operation
     * @param {string} operationType - Type of operation
     * @param {string} targetId - Target component ID
     * @param {Object} operationData - Operation data
     */
  }, {
    key: "sendComponentOperation",
    value: function sendComponentOperation(operationType, targetId, operationData) {
      if (!this.isConnected || !this.currentDocument) {
        throw new Error('Not connected to collaboration session');
      }
      this.wsClient.send({
        type: 'component_operation',
        operation_type: operationType,
        target_id: targetId,
        operation_data: operationData,
        timestamp: Date.now()
      });
    }

    /**
     * Get list of current collaborators
     * @returns {Array} List of collaborators
     */
  }, {
    key: "getCollaborators",
    value: function getCollaborators() {
      return Array.from(this.collaborators.values());
    }

    /**
     * Add event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
  }, {
    key: "addEventListener",
    value: function addEventListener(event, callback) {
      if (!this.eventListeners.has(event)) {
        this.eventListeners.set(event, new Set());
      }
      this.eventListeners.get(event).add(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} callback - Callback function
     */
  }, {
    key: "removeEventListener",
    value: function removeEventListener(event, callback) {
      if (this.eventListeners.has(event)) {
        this.eventListeners.get(event)["delete"](callback);
      }
    }

    /**
     * Emit event to listeners
     * @param {string} event - Event name
     * @param {*} data - Event data
     */
  }, {
    key: "_emit",
    value: function _emit(event, data) {
      var _this2 = this;
      if (this.eventListeners.has(event)) {
        this.eventListeners.get(event).forEach(function (callback) {
          try {
            callback(data);
          } catch (error) {
            _this2._error('Error in event listener:', error);
          }
        });
      }
    }

    /**
     * Set up WebSocket event listeners
     */
  }, {
    key: "_setupEventListeners",
    value: function _setupEventListeners() {
      var _this3 = this;
      this.wsClient.addEventListener('message', function (data) {
        _this3._handleMessage(data);
      });
      this.wsClient.addEventListener('open', function () {
        _this3._log('Collaboration WebSocket connected');
        _this3._emit('connected');
      });
      this.wsClient.addEventListener('close', function () {
        _this3._log('Collaboration WebSocket disconnected');
        _this3.isConnected = false;
        _this3._emit('disconnected');
      });
      this.wsClient.addEventListener('error', function (error) {
        _this3._error('Collaboration WebSocket error:', error);
        _this3._emit('error', error);
      });
    }

    /**
     * Handle incoming WebSocket messages
     * @param {Object} data - Message data
     */
  }, {
    key: "_handleMessage",
    value: function _handleMessage(data) {
      this._log('Received message:', data);
      switch (data.type) {
        case 'operation':
        case 'component_operation':
          this._handleOperation(data.operation || data);
          break;
        case 'cursor_position':
          this._handleCursorUpdate(data.position, data.user_id, data.username);
          break;
        case 'selection_change':
          this._handleSelectionChange(data.selection, data.user_id, data.username);
          break;
        case 'presence_update':
          this._handlePresenceUpdate(data.presence, data.user_id);
          break;
        case 'user_joined':
          this._handleCollaboratorJoined({
            id: data.user_id,
            username: data.username,
            timestamp: data.timestamp
          });
          break;
        case 'user_left':
          this._handleCollaboratorLeft(data.user_id);
          break;
        case 'session_joined':
          this._handleSessionJoined(data);
          break;
        case 'comment_created':
          this._handleCommentCreated(data.comment);
          break;
        case 'comment_updated':
          this._handleCommentUpdated(data.comment);
          break;
        case 'comment_resolved':
          this._handleCommentResolved(data.comment);
          break;
        case 'edit_confirmed':
          this._handleEditConfirmed(data);
          break;
        default:
          this._log('Unknown message type:', data.type);
      }

      // Emit generic message event
      this._emit('message', data);
    }

    /**
     * Handle incoming operations
     * @param {Object} operation - Operation data
     */
  }, {
    key: "_handleOperation",
    value: function _handleOperation(operation) {
      this.operationQueue.push(operation);
      this._processOperationQueue();
      this._emit('operation', operation);
    }

    /**
     * Handle cursor updates
     * @param {Object} cursor - Cursor data
     * @param {string} userId - User ID
     */
  }, {
    key: "_handleCursorUpdate",
    value: function _handleCursorUpdate(cursor, userId) {
      this._emit('cursor_update', {
        cursor: cursor,
        userId: userId
      });
    }

    /**
     * Handle presence updates
     * @param {Object} presence - Presence data
     * @param {string} userId - User ID
     */
  }, {
    key: "_handlePresenceUpdate",
    value: function _handlePresenceUpdate(presence, userId) {
      if (this.collaborators.has(userId)) {
        var collaborator = this.collaborators.get(userId);
        collaborator.presence = presence;
        this.collaborators.set(userId, collaborator);
      }
      this._emit('presence_update', {
        presence: presence,
        userId: userId
      });
    }

    /**
     * Handle collaborator joined
     * @param {Object} collaborator - Collaborator data
     */
  }, {
    key: "_handleCollaboratorJoined",
    value: function _handleCollaboratorJoined(collaborator) {
      this.collaborators.set(collaborator.id, collaborator);
      this._emit('collaborator_joined', collaborator);
    }

    /**
     * Handle collaborator left
     * @param {string} userId - User ID
     */
  }, {
    key: "_handleCollaboratorLeft",
    value: function _handleCollaboratorLeft(userId) {
      var collaborator = this.collaborators.get(userId);
      this.collaborators["delete"](userId);
      this._emit('collaborator_left', {
        userId: userId,
        collaborator: collaborator
      });
    }

    /**
     * Handle session joined confirmation
     * @param {Object} data - Join data
     */
  }, {
    key: "_handleSessionJoined",
    value: function _handleSessionJoined(data) {
      this._log('Successfully joined session:', data.session_id);
      this._emit('session_joined', data);
    }

    /**
     * Handle selection change
     * @param {Object} selection - Selection data
     * @param {string} userId - User ID
     * @param {string} username - Username
     */
  }, {
    key: "_handleSelectionChange",
    value: function _handleSelectionChange(selection, userId, username) {
      this._emit('selection_change', {
        selection: selection,
        userId: userId,
        username: username
      });
    }

    /**
     * Handle comment created
     * @param {Object} comment - Comment data
     */
  }, {
    key: "_handleCommentCreated",
    value: function _handleCommentCreated(comment) {
      this._emit('comment_created', comment);
    }

    /**
     * Handle comment updated
     * @param {Object} comment - Comment data
     */
  }, {
    key: "_handleCommentUpdated",
    value: function _handleCommentUpdated(comment) {
      this._emit('comment_updated', comment);
    }

    /**
     * Handle comment resolved
     * @param {Object} comment - Comment data
     */
  }, {
    key: "_handleCommentResolved",
    value: function _handleCommentResolved(comment) {
      this._emit('comment_resolved', comment);
    }

    /**
     * Handle edit confirmed
     * @param {Object} data - Confirmation data
     */
  }, {
    key: "_handleEditConfirmed",
    value: function _handleEditConfirmed(data) {
      this._emit('edit_confirmed', data);
    }

    /**
     * Process operation queue
     */
  }, {
    key: "_processOperationQueue",
    value: (function () {
      var _processOperationQueue2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4() {
        var operation, _t2;
        return regenerator_default().wrap(function (_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              if (!(this.isProcessingOperations || this.operationQueue.length === 0)) {
                _context4.next = 1;
                break;
              }
              return _context4.abrupt("return");
            case 1:
              this.isProcessingOperations = true;
              _context4.prev = 2;
            case 3:
              if (!(this.operationQueue.length > 0)) {
                _context4.next = 5;
                break;
              }
              operation = this.operationQueue.shift(); // Process operation (transform, apply, etc.)
              _context4.next = 4;
              return this._processOperation(operation);
            case 4:
              _context4.next = 3;
              break;
            case 5:
              _context4.next = 7;
              break;
            case 6:
              _context4.prev = 6;
              _t2 = _context4["catch"](2);
              this._error('Error processing operations:', _t2);
            case 7:
              _context4.prev = 7;
              this.isProcessingOperations = false;
              return _context4.finish(7);
            case 8:
            case "end":
              return _context4.stop();
          }
        }, _callee4, this, [[2, 6, 7, 8]]);
      }));
      function _processOperationQueue() {
        return _processOperationQueue2.apply(this, arguments);
      }
      return _processOperationQueue;
    }()
    /**
     * Process a single operation
     * @param {Object} operation - Operation to process
     */
    )
  }, {
    key: "_processOperation",
    value: (function () {
      var _processOperation2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5(operation) {
        return regenerator_default().wrap(function (_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              // This would contain the operational transformation logic
              // For now, just emit the operation
              this._emit('operation_processed', operation);
            case 1:
            case "end":
              return _context5.stop();
          }
        }, _callee5, this);
      }));
      function _processOperation(_x5) {
        return _processOperation2.apply(this, arguments);
      }
      return _processOperation;
    }()
    /**
     * Generate unique operation ID
     * @returns {string} Operation ID
     */
    )
  }, {
    key: "_generateOperationId",
    value: function _generateOperationId() {
      return "op_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
    }

    /**
     * Disconnect from collaboration
     */
  }, {
    key: "disconnect",
    value: function disconnect() {
      if (this.wsClient) {
        this.wsClient.disconnect();
        this.wsClient = null;
      }
      this.isConnected = false;
      this.currentDocument = null;
      this.collaborators.clear();
      this.operationQueue = [];
      this.eventListeners.clear();
    }

    /**
     * Log debug message
     * @param {...any} args - Arguments to log
     */
  }, {
    key: "_log",
    value: function _log() {
      if (this.debug) {
        var _console;
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        (_console = console).log.apply(_console, ['[CollaborationService]'].concat(args));
      }
    }

    /**
     * Log error message
     * @param {...any} args - Arguments to log
     */
  }, {
    key: "_error",
    value: function _error() {
      var _console2;
      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
        args[_key2] = arguments[_key2];
      }
      (_console2 = console).error.apply(_console2, ['[CollaborationService]'].concat(args));
    }
  }]);
}(); // Export singleton instance
/* harmony default export */ const services_CollaborationService = (new CollaborationService());

/***/ }),

/***/ 92939:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProgressiveWrapper: () => (/* binding */ ProgressiveWrapper),
/* harmony export */   progressiveLoader: () => (/* binding */ progressiveLoader),
/* harmony export */   useInteractionLoading: () => (/* binding */ useInteractionLoading),
/* harmony export */   useProgressiveLoading: () => (/* binding */ useProgressiveLoading),
/* harmony export */   useViewportLoading: () => (/* binding */ useViewportLoading)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(92901);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(96540);
/* harmony import */ var _config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(38787);







var _excluded = ["children", "componentName", "strategy", "fallback"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Progressive Component Loading System
 * 
 * Implements intelligent loading of components based on priority, user interaction,
 * and viewport visibility to optimize initial bundle size and user experience.
 */

// Global state for tracking loaded components
var loadedComponents = new Set();
var loadingComponents = new Set();
var preloadQueue = [];

/**
 * Progressive loading manager
 */
var ProgressiveLoadingManager = /*#__PURE__*/function () {
  function ProgressiveLoadingManager() {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(this, ProgressiveLoadingManager);
    this.intersectionObserver = null;
    this.idleCallback = null;
    this.loadingStrategy = 'priority'; // 'priority', 'interaction', 'viewport'
    this.maxConcurrentLoads = 3;
    this.currentLoads = 0;
    this.initializeObservers();
  }
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A)(ProgressiveLoadingManager, [{
    key: "initializeObservers",
    value: function initializeObservers() {
      var _this = this;
      // Intersection Observer for viewport-based loading
      if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
        this.intersectionObserver = new IntersectionObserver(function (entries) {
          entries.forEach(function (entry) {
            if (entry.isIntersecting) {
              var componentName = entry.target.dataset.componentName;
              if (componentName) {
                _this.loadComponent(componentName, 'viewport');
              }
            }
          });
        }, {
          rootMargin: '50px',
          threshold: 0.1
        });
      }

      // Idle callback for background loading
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        this.scheduleIdleLoading();
      }
    }
  }, {
    key: "scheduleIdleLoading",
    value: function scheduleIdleLoading() {
      var _this2 = this;
      this.idleCallback = requestIdleCallback(function () {
        _this2.loadNextInQueue();
        _this2.scheduleIdleLoading(); // Schedule next idle period
      }, {
        timeout: 5000
      });
    }
  }, {
    key: "loadComponent",
    value: function () {
      var _loadComponent = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7___default().mark(function _callee(componentName) {
        var trigger,
          component,
          _i,
          _Object$entries,
          _Object$entries$_i,
          featureName,
          components,
          _args = arguments,
          _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_7___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              trigger = _args.length > 1 && _args[1] !== undefined ? _args[1] : 'manual';
              if (!(loadedComponents.has(componentName) || loadingComponents.has(componentName))) {
                _context.next = 1;
                break;
              }
              return _context.abrupt("return");
            case 1:
              if (!(this.currentLoads >= this.maxConcurrentLoads)) {
                _context.next = 2;
                break;
              }
              preloadQueue.push({
                componentName: componentName,
                trigger: trigger
              });
              return _context.abrupt("return");
            case 2:
              loadingComponents.add(componentName);
              this.currentLoads++;
              _context.prev = 3;
              // Find component in feature groups
              component = null;
              _i = 0, _Object$entries = Object.entries(_config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__.FeatureComponents);
            case 4:
              if (!(_i < _Object$entries.length)) {
                _context.next = 6;
                break;
              }
              _Object$entries$_i = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_Object$entries[_i], 2), featureName = _Object$entries$_i[0], components = _Object$entries$_i[1];
              component = components.find(function (comp) {
                return comp.displayName === componentName;
              });
              if (!component) {
                _context.next = 5;
                break;
              }
              return _context.abrupt("continue", 6);
            case 5:
              _i++;
              _context.next = 4;
              break;
            case 6:
              if (!(component && component.preload)) {
                _context.next = 8;
                break;
              }
              console.log("\uD83D\uDD04 Loading component: ".concat(componentName, " (trigger: ").concat(trigger, ")"));
              _context.next = 7;
              return component.preload();
            case 7:
              loadedComponents.add(componentName);
              console.log("\u2705 Loaded component: ".concat(componentName));
            case 8:
              _context.next = 10;
              break;
            case 9:
              _context.prev = 9;
              _t = _context["catch"](3);
              console.warn("\u274C Failed to load component: ".concat(componentName), _t);
            case 10:
              _context.prev = 10;
              loadingComponents["delete"](componentName);
              this.currentLoads--;
              this.loadNextInQueue();
              return _context.finish(10);
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[3, 9, 10, 11]]);
      }));
      function loadComponent(_x) {
        return _loadComponent.apply(this, arguments);
      }
      return loadComponent;
    }()
  }, {
    key: "loadNextInQueue",
    value: function loadNextInQueue() {
      if (preloadQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
        var _preloadQueue$shift = preloadQueue.shift(),
          componentName = _preloadQueue$shift.componentName,
          trigger = _preloadQueue$shift.trigger;
        this.loadComponent(componentName, trigger);
      }
    }
  }, {
    key: "observeElement",
    value: function observeElement(element, componentName) {
      if (this.intersectionObserver && element) {
        element.dataset.componentName = componentName;
        this.intersectionObserver.observe(element);
      }
    }
  }, {
    key: "unobserveElement",
    value: function unobserveElement(element) {
      if (this.intersectionObserver && element) {
        this.intersectionObserver.unobserve(element);
      }
    }
  }, {
    key: "preloadByPriority",
    value: function preloadByPriority() {
      var _this3 = this;
      // Load high priority components first
      _config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__.LoadingPriority.high.forEach(function (component) {
        _this3.loadComponent(component.displayName, 'priority-high');
      });

      // Schedule medium priority for idle time
      setTimeout(function () {
        _config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__.LoadingPriority.medium.forEach(function (component) {
          _this3.loadComponent(component.displayName, 'priority-medium');
        });
      }, 1000);

      // Schedule low priority for later
      setTimeout(function () {
        _config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__.LoadingPriority.low.forEach(function (component) {
          _this3.loadComponent(component.displayName, 'priority-low');
        });
      }, 3000);
    }
  }, {
    key: "preloadByFeature",
    value: function preloadByFeature(featureName) {
      var _this4 = this;
      var components = _config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__.FeatureComponents[featureName];
      if (components) {
        components.forEach(function (component) {
          _this4.loadComponent(component.displayName, "feature-".concat(featureName));
        });
      }
    }
  }, {
    key: "destroy",
    value: function destroy() {
      if (this.intersectionObserver) {
        this.intersectionObserver.disconnect();
      }
      if (this.idleCallback) {
        cancelIdleCallback(this.idleCallback);
      }
    }
  }]);
}(); // Global instance
var progressiveLoader = new ProgressiveLoadingManager();

/**
 * Hook for progressive component loading
 */
var useProgressiveLoading = function useProgressiveLoading() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$strategy = options.strategy,
    strategy = _options$strategy === void 0 ? 'priority' : _options$strategy,
    _options$features = options.features,
    features = _options$features === void 0 ? [] : _options$features,
    _options$components = options.components,
    components = _options$components === void 0 ? [] : _options$components,
    _options$autoStart = options.autoStart,
    autoStart = _options$autoStart === void 0 ? true : _options$autoStart,
    _options$delay = options.delay,
    delay = _options$delay === void 0 ? 0 : _options$delay;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)({
      loaded: Array.from(loadedComponents),
      loading: Array.from(loadingComponents),
      total: 0
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    loadingState = _useState2[0],
    setLoadingState = _useState2[1];
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    var updateLoadingState = function updateLoadingState() {
      setLoadingState({
        loaded: Array.from(loadedComponents),
        loading: Array.from(loadingComponents),
        total: Object.values(_config_lazyComponents__WEBPACK_IMPORTED_MODULE_9__.FeatureComponents).flat().length
      });
    };

    // Update state periodically
    var interval = setInterval(updateLoadingState, 1000);
    if (autoStart) {
      var timer = setTimeout(function () {
        switch (strategy) {
          case 'priority':
            progressiveLoader.preloadByPriority();
            break;
          case 'features':
            features.forEach(function (feature) {
              progressiveLoader.preloadByFeature(feature);
            });
            break;
          case 'components':
            components.forEach(function (componentName) {
              progressiveLoader.loadComponent(componentName, 'manual');
            });
            break;
        }
      }, delay);
      return function () {
        clearTimeout(timer);
        clearInterval(interval);
      };
    }
    return function () {
      return clearInterval(interval);
    };
  }, [strategy, features, components, autoStart, delay]);
  return _objectSpread(_objectSpread({}, loadingState), {}, {
    loadComponent: function loadComponent(componentName) {
      return progressiveLoader.loadComponent(componentName, 'manual');
    },
    loadFeature: function loadFeature(featureName) {
      return progressiveLoader.preloadByFeature(featureName);
    },
    isLoaded: function isLoaded(componentName) {
      return loadedComponents.has(componentName);
    },
    isLoading: function isLoading(componentName) {
      return loadingComponents.has(componentName);
    }
  });
};

/**
 * Hook for viewport-based component loading
 */
var useViewportLoading = function useViewportLoading(componentName) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var elementRef = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(null);
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    isVisible = _useState4[0],
    setIsVisible = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(loadedComponents.has(componentName)),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    isLoaded = _useState6[0],
    setIsLoaded = _useState6[1];
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    var element = elementRef.current;
    if (element && componentName) {
      progressiveLoader.observeElement(element, componentName);

      // Check if component gets loaded
      var checkLoaded = function checkLoaded() {
        if (loadedComponents.has(componentName)) {
          setIsLoaded(true);
        }
      };
      var interval = setInterval(checkLoaded, 500);
      return function () {
        progressiveLoader.unobserveElement(element);
        clearInterval(interval);
      };
    }
  }, [componentName]);
  return {
    ref: elementRef,
    isVisible: isVisible,
    isLoaded: isLoaded,
    loadComponent: function loadComponent() {
      return progressiveLoader.loadComponent(componentName, 'manual');
    }
  };
};

/**
 * Hook for interaction-based component loading
 */
var useInteractionLoading = function useInteractionLoading(componentName) {
  var events = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ['mouseenter', 'focus'];
  var elementRef = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(null);
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(loadedComponents.has(componentName)),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    isLoaded = _useState8[0],
    setIsLoaded = _useState8[1];
  var hasTriggered = (0,react__WEBPACK_IMPORTED_MODULE_8__.useRef)(false);
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    var element = elementRef.current;
    if (element && componentName && !hasTriggered.current) {
      var handleInteraction = function handleInteraction() {
        if (!hasTriggered.current) {
          hasTriggered.current = true;
          progressiveLoader.loadComponent(componentName, 'interaction');
        }
      };
      events.forEach(function (event) {
        element.addEventListener(event, handleInteraction, {
          passive: true
        });
      });
      return function () {
        events.forEach(function (event) {
          element.removeEventListener(event, handleInteraction);
        });
      };
    }
  }, [componentName, events]);
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    var checkLoaded = function checkLoaded() {
      if (loadedComponents.has(componentName)) {
        setIsLoaded(true);
      }
    };
    var interval = setInterval(checkLoaded, 500);
    return function () {
      return clearInterval(interval);
    };
  }, [componentName]);
  return {
    ref: elementRef,
    isLoaded: isLoaded,
    loadComponent: function loadComponent() {
      return progressiveLoader.loadComponent(componentName, 'manual');
    }
  };
};

/**
 * Component wrapper for progressive loading
 */
var ProgressiveWrapper = function ProgressiveWrapper(_ref) {
  var children = _ref.children,
    componentName = _ref.componentName,
    _ref$strategy = _ref.strategy,
    strategy = _ref$strategy === void 0 ? 'viewport' : _ref$strategy,
    _ref$fallback = _ref.fallback,
    fallback = _ref$fallback === void 0 ? null : _ref$fallback,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref, _excluded);
  var viewportHook = useViewportLoading(componentName);
  var interactionHook = useInteractionLoading(componentName);
  var hook = strategy === 'viewport' ? viewportHook : interactionHook;
  return /*#__PURE__*/React.createElement("div", (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
    ref: hook.ref
  }, props), hook.isLoaded ? children : fallback);
};

// Export the manager instance for direct use


// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', function () {
    progressiveLoader.destroy();
  });
}

/***/ })

}]);