/**
 * Mock for @ant-design/icons
 * Provides simple div elements with appropriate test IDs for all icons
 */

import React from 'react';

// Create a generic icon mock
const createIconMock = (iconName) => {
  const IconComponent = React.forwardRef((props, ref) => {
    const { className = '', style = {}, onClick, ...otherProps } = props;
    return React.createElement('span', {
      ref,
      className: `anticon anticon-${iconName.toLowerCase()} ${className}`.trim(),
      style,
      onClick,
      'data-testid': `icon-${iconName.toLowerCase()}`,
      'aria-label': iconName,
      role: 'img',
      ...otherProps,
    }, iconName.charAt(0)); // Show first letter as content
  });
  IconComponent.displayName = `${iconName}Icon`;
  return IconComponent;
};

// Export all commonly used icons
export const AccountBookOutlined = createIconMock('AccountBook');
export const AimOutlined = createIconMock('Aim');
export const AlertOutlined = createIconMock('Alert');
export const ApartmentOutlined = createIconMock('Apartment');
export const ApiOutlined = createIconMock('Api');
export const AppstoreAddOutlined = createIconMock('AppstoreAdd');
export const AppstoreOutlined = createIconMock('Appstore');
export const AudioOutlined = createIconMock('Audio');
export const AudioMutedOutlined = createIconMock('AudioMuted');
export const AuditOutlined = createIconMock('Audit');
export const BackwardOutlined = createIconMock('Backward');
export const BankOutlined = createIconMock('Bank');
export const BarChartOutlined = createIconMock('BarChart');
export const BarcodeOutlined = createIconMock('Barcode');
export const BarsOutlined = createIconMock('Bars');
export const BellOutlined = createIconMock('Bell');
export const BlockOutlined = createIconMock('Block');
export const BookOutlined = createIconMock('Book');
export const BorderOutlined = createIconMock('Border');
export const BranchesOutlined = createIconMock('Branches');
export const BugOutlined = createIconMock('Bug');
export const BuildOutlined = createIconMock('Build');
export const BulbOutlined = createIconMock('Bulb');
export const CalculatorOutlined = createIconMock('Calculator');
export const CalendarOutlined = createIconMock('Calendar');
export const CameraOutlined = createIconMock('Camera');
export const CarOutlined = createIconMock('Car');
export const CarryOutOutlined = createIconMock('CarryOut');
export const CheckCircleOutlined = createIconMock('CheckCircle');
export const CheckOutlined = createIconMock('Check');
export const CheckSquareOutlined = createIconMock('CheckSquare');
export const ClockCircleOutlined = createIconMock('ClockCircle');
export const CloseCircleOutlined = createIconMock('CloseCircle');
export const CloseOutlined = createIconMock('Close');
export const CloudDownloadOutlined = createIconMock('CloudDownload');
export const CloudOutlined = createIconMock('Cloud');
export const CloudUploadOutlined = createIconMock('CloudUpload');
export const CodeOutlined = createIconMock('Code');
export const CoffeeOutlined = createIconMock('Coffee');
export const CommentOutlined = createIconMock('Comment');
export const CompassOutlined = createIconMock('Compass');
export const CompressOutlined = createIconMock('Compress');
export const ContactsOutlined = createIconMock('Contacts');
export const ContainerOutlined = createIconMock('Container');
export const ControlOutlined = createIconMock('Control');
export const CopyOutlined = createIconMock('Copy');
export const CopyrightOutlined = createIconMock('Copyright');
export const CreditCardOutlined = createIconMock('CreditCard');
export const CrownOutlined = createIconMock('Crown');
export const CustomerServiceOutlined = createIconMock('CustomerService');
export const DashboardOutlined = createIconMock('Dashboard');
export const DatabaseOutlined = createIconMock('Database');
export const DeleteOutlined = createIconMock('Delete');
export const DeliveredProcedureOutlined = createIconMock('DeliveredProcedure');
export const DeploymentUnitOutlined = createIconMock('DeploymentUnit');
export const DesktopOutlined = createIconMock('Desktop');
export const DiffOutlined = createIconMock('Diff');
export const DingdingOutlined = createIconMock('Dingding');
export const DisconnectOutlined = createIconMock('Disconnect');
export const DislikeOutlined = createIconMock('Dislike');
export const DollarCircleOutlined = createIconMock('DollarCircle');
export const DollarOutlined = createIconMock('Dollar');
export const DownCircleOutlined = createIconMock('DownCircle');
export const DownloadOutlined = createIconMock('Download');
export const DownOutlined = createIconMock('Down');
export const DownSquareOutlined = createIconMock('DownSquare');
export const DragOutlined = createIconMock('Drag');
export const EditOutlined = createIconMock('Edit');
export const EllipsisOutlined = createIconMock('Ellipsis');
export const EnterOutlined = createIconMock('Enter');
export const EnvironmentOutlined = createIconMock('Environment');
export const EuroCircleOutlined = createIconMock('EuroCircle');
export const EuroOutlined = createIconMock('Euro');
export const ExceptionOutlined = createIconMock('Exception');
export const ExclamationCircleOutlined = createIconMock('ExclamationCircle');
export const ExclamationOutlined = createIconMock('Exclamation');
export const ExpandAltOutlined = createIconMock('ExpandAlt');
export const ExpandOutlined = createIconMock('Expand');
export const ExperimentOutlined = createIconMock('Experiment');
export const ExportOutlined = createIconMock('Export');
export const EyeInvisibleOutlined = createIconMock('EyeInvisible');
export const EyeOutlined = createIconMock('Eye');
export const FieldBinaryOutlined = createIconMock('FieldBinary');
export const FieldNumberOutlined = createIconMock('FieldNumber');
export const FieldStringOutlined = createIconMock('FieldString');
export const FieldTimeOutlined = createIconMock('FieldTime');
export const FileAddOutlined = createIconMock('FileAdd');
export const FileDoneOutlined = createIconMock('FileDone');
export const FileExcelOutlined = createIconMock('FileExcel');
export const FileExclamationOutlined = createIconMock('FileExclamation');
export const FileImageOutlined = createIconMock('FileImage');
export const FileJpgOutlined = createIconMock('FileJpg');
export const FileMarkdownOutlined = createIconMock('FileMarkdown');
export const FileOutlined = createIconMock('File');
export const FilePdfOutlined = createIconMock('FilePdf');
export const FilePptOutlined = createIconMock('FilePpt');
export const FileProtectOutlined = createIconMock('FileProtect');
export const FileSearchOutlined = createIconMock('FileSearch');
export const FileSyncOutlined = createIconMock('FileSync');
export const FileTextOutlined = createIconMock('FileText');
export const FileUnknownOutlined = createIconMock('FileUnknown');
export const FileWordOutlined = createIconMock('FileWord');
export const FileZipOutlined = createIconMock('FileZip');
export const FilterOutlined = createIconMock('Filter');
export const FireOutlined = createIconMock('Fire');
export const FlagOutlined = createIconMock('Flag');
export const FolderAddOutlined = createIconMock('FolderAdd');
export const FolderOpenOutlined = createIconMock('FolderOpen');
export const FolderOutlined = createIconMock('Folder');
export const FolderViewOutlined = createIconMock('FolderView');
export const FormatPainterOutlined = createIconMock('FormatPainter');
export const ForwardOutlined = createIconMock('Forward');
export const FrownOutlined = createIconMock('Frown');
export const FullscreenExitOutlined = createIconMock('FullscreenExit');
export const FullscreenOutlined = createIconMock('Fullscreen');
export const FunctionOutlined = createIconMock('Function');
export const FundProjectionScreenOutlined = createIconMock('FundProjectionScreen');
export const FundViewOutlined = createIconMock('FundView');
export const FunnelPlotOutlined = createIconMock('FunnelPlot');
export const GatewayOutlined = createIconMock('Gateway');
export const GifOutlined = createIconMock('Gif');
export const GiftOutlined = createIconMock('Gift');
export const GlobalOutlined = createIconMock('Global');
export const GoldOutlined = createIconMock('Gold');
export const GroupOutlined = createIconMock('Group');
export const HddOutlined = createIconMock('Hdd');
export const HeartOutlined = createIconMock('Heart');
export const HistoryOutlined = createIconMock('History');
export const HomeOutlined = createIconMock('Home');
export const HourglassOutlined = createIconMock('Hourglass');
export const IdcardOutlined = createIconMock('Idcard');
export const ImportOutlined = createIconMock('Import');
export const InboxOutlined = createIconMock('Inbox');
export const InfoCircleOutlined = createIconMock('InfoCircle');
export const InfoOutlined = createIconMock('Info');
export const InsertRowAboveOutlined = createIconMock('InsertRowAbove');
export const InsertRowBelowOutlined = createIconMock('InsertRowBelow');
export const InsertRowLeftOutlined = createIconMock('InsertRowLeft');
export const InsertRowRightOutlined = createIconMock('InsertRowRight');
export const InstagramOutlined = createIconMock('Instagram');
export const InteractionOutlined = createIconMock('Interaction');
export const IssuesCloseOutlined = createIconMock('IssuesClose');
export const KeyOutlined = createIconMock('Key');
export const LaptopOutlined = createIconMock('Laptop');
export const LayoutOutlined = createIconMock('Layout');
export const LeftCircleOutlined = createIconMock('LeftCircle');
export const LeftOutlined = createIconMock('Left');
export const LeftSquareOutlined = createIconMock('LeftSquare');
export const LikeOutlined = createIconMock('Like');
export const LineChartOutlined = createIconMock('LineChart');
export const LinkOutlined = createIconMock('Link');
export const LinkedinOutlined = createIconMock('Linkedin');
export const LoadingOutlined = createIconMock('Loading');
export const LockOutlined = createIconMock('Lock');
export const LoginOutlined = createIconMock('Login');
export const LogoutOutlined = createIconMock('Logout');
export const MacCommandOutlined = createIconMock('MacCommand');
export const MailOutlined = createIconMock('Mail');
export const ManOutlined = createIconMock('Man');
export const MedicineBoxOutlined = createIconMock('MedicineBox');
export const MehOutlined = createIconMock('Meh');
export const MenuFoldOutlined = createIconMock('MenuFold');
export const MenuOutlined = createIconMock('Menu');
export const MenuUnfoldOutlined = createIconMock('MenuUnfold');
export const MessageOutlined = createIconMock('Message');
export const MobileOutlined = createIconMock('Mobile');
export const MoneyCollectOutlined = createIconMock('MoneyCollect');
export const MonitorOutlined = createIconMock('Monitor');
export const MoreOutlined = createIconMock('More');
export const NodeCollapseOutlined = createIconMock('NodeCollapse');
export const NodeExpandOutlined = createIconMock('NodeExpand');
export const NodeIndexOutlined = createIconMock('NodeIndex');
export const NotificationOutlined = createIconMock('Notification');
export const NumberOutlined = createIconMock('Number');
export const PaperClipOutlined = createIconMock('PaperClip');
export const PartitionOutlined = createIconMock('Partition');
export const PauseCircleOutlined = createIconMock('PauseCircle');
export const PauseOutlined = createIconMock('Pause');
export const PayCircleOutlined = createIconMock('PayCircle');
export const PercentageOutlined = createIconMock('Percentage');
export const PhoneOutlined = createIconMock('Phone');
export const PictureOutlined = createIconMock('Picture');
export const PieChartOutlined = createIconMock('PieChart');
export const PlayCircleOutlined = createIconMock('PlayCircle');
export const PlaySquareOutlined = createIconMock('PlaySquare');
export const PlusCircleOutlined = createIconMock('PlusCircle');
export const PlusOutlined = createIconMock('Plus');
export const PlusSquareOutlined = createIconMock('PlusSquare');
export const PoweroffOutlined = createIconMock('Poweroff');
export const PrinterOutlined = createIconMock('Printer');
export const ProfileOutlined = createIconMock('Profile');
export const ProjectOutlined = createIconMock('Project');
export const PropertySafetyOutlined = createIconMock('PropertySafety');
export const PullRequestOutlined = createIconMock('PullRequest');
export const PushpinOutlined = createIconMock('Pushpin');
export const QrcodeOutlined = createIconMock('Qrcode');
export const QuestionCircleOutlined = createIconMock('QuestionCircle');
export const QuestionOutlined = createIconMock('Question');
export const RadiusBottomleftOutlined = createIconMock('RadiusBottomleft');
export const RadiusBottomrightOutlined = createIconMock('RadiusBottomright');
export const RadiusSettingOutlined = createIconMock('RadiusSetting');
export const RadiusUpleftOutlined = createIconMock('RadiusUpleft');
export const RadiusUprightOutlined = createIconMock('RadiusUpright');
export const ReadOutlined = createIconMock('Read');
export const ReconciliationOutlined = createIconMock('Reconciliation');
export const RedoOutlined = createIconMock('Redo');
export const ReloadOutlined = createIconMock('Reload');
export const RestOutlined = createIconMock('Rest');
export const RightCircleOutlined = createIconMock('RightCircle');
export const RightOutlined = createIconMock('Right');
export const RightSquareOutlined = createIconMock('RightSquare');
export const RiseOutlined = createIconMock('Rise');
export const RobotOutlined = createIconMock('Robot');
export const RocketOutlined = createIconMock('Rocket');
export const RollbackOutlined = createIconMock('Rollback');
export const SafetyCertificateOutlined = createIconMock('SafetyCertificate');
export const SafetyOutlined = createIconMock('Safety');
export const SaveOutlined = createIconMock('Save');
export const ScanOutlined = createIconMock('Scan');
export const ScheduleOutlined = createIconMock('Schedule');
export const ScissorOutlined = createIconMock('Scissor');
export const SearchOutlined = createIconMock('Search');
export const SecurityScanOutlined = createIconMock('SecurityScan');
export const SelectOutlined = createIconMock('Select');
export const SendOutlined = createIconMock('Send');
export const SettingOutlined = createIconMock('Setting');
export const ShakeOutlined = createIconMock('Shake');
export const ShareAltOutlined = createIconMock('ShareAlt');
export const ShopOutlined = createIconMock('Shop');
export const ShoppingCartOutlined = createIconMock('ShoppingCart');
export const ShoppingOutlined = createIconMock('Shopping');
export const ShrinkOutlined = createIconMock('Shrink');
export const SisternodeOutlined = createIconMock('Sisternode');
export const SketchOutlined = createIconMock('Sketch');
export const SkinOutlined = createIconMock('Skin');
export const SmileOutlined = createIconMock('Smile');
export const SolutionOutlined = createIconMock('Solution');
export const SortAscendingOutlined = createIconMock('SortAscending');
export const SortDescendingOutlined = createIconMock('SortDescending');
export const SoundOutlined = createIconMock('Sound');
export const StarOutlined = createIconMock('Star');
export const StepBackwardOutlined = createIconMock('StepBackward');
export const StepForwardOutlined = createIconMock('StepForward');
export const StockOutlined = createIconMock('Stock');
export const StopOutlined = createIconMock('Stop');
export const SubnodeOutlined = createIconMock('Subnode');
export const SwapLeftOutlined = createIconMock('SwapLeft');
export const SwapOutlined = createIconMock('Swap');
export const SwapRightOutlined = createIconMock('SwapRight');
export const SwitcherOutlined = createIconMock('Switcher');
export const SyncOutlined = createIconMock('Sync');
export const TableOutlined = createIconMock('Table');
export const TabletOutlined = createIconMock('Tablet');
export const TagOutlined = createIconMock('Tag');
export const TagsOutlined = createIconMock('Tags');
export const TeamOutlined = createIconMock('Team');
export const ThunderboltOutlined = createIconMock('Thunderbolt');
export const ToTopOutlined = createIconMock('ToTop');
export const ToolOutlined = createIconMock('Tool');
export const TrademarkCircleOutlined = createIconMock('TrademarkCircle');
export const TrademarkOutlined = createIconMock('Trademark');
export const TransactionOutlined = createIconMock('Transaction');
export const TranslationOutlined = createIconMock('Translation');
export const TrophyOutlined = createIconMock('Trophy');
export const UndoOutlined = createIconMock('Undo');
export const UnlockOutlined = createIconMock('Unlock');
export const UpCircleOutlined = createIconMock('UpCircle');
export const UploadOutlined = createIconMock('Upload');
export const UpOutlined = createIconMock('Up');
export const UpSquareOutlined = createIconMock('UpSquare');
export const UsbOutlined = createIconMock('Usb');
export const UserAddOutlined = createIconMock('UserAdd');
export const UserDeleteOutlined = createIconMock('UserDelete');
export const UserOutlined = createIconMock('User');
export const UserSwitchOutlined = createIconMock('UserSwitch');
export const UsergroupAddOutlined = createIconMock('UsergroupAdd');
export const UsergroupDeleteOutlined = createIconMock('UsergroupDelete');
export const VideoCameraOutlined = createIconMock('VideoCamera');
export const WalletOutlined = createIconMock('Wallet');
export const WarningOutlined = createIconMock('Warning');
export const WifiOutlined = createIconMock('Wifi');
export const WindowsOutlined = createIconMock('Windows');
export const WomanOutlined = createIconMock('Woman');
export const ZoomInOutlined = createIconMock('ZoomIn');
export const ZoomOutOutlined = createIconMock('ZoomOut');

// Default export with all icons
const icons = {
  AccountBookOutlined, AimOutlined, AlertOutlined, ApartmentOutlined, ApiOutlined,
  AppstoreAddOutlined, AppstoreOutlined, AudioOutlined, AudioMutedOutlined, AuditOutlined,
  BackwardOutlined, BankOutlined, BarChartOutlined, BarcodeOutlined, BarsOutlined,
  BellOutlined, BlockOutlined, BookOutlined, BorderOutlined, BranchesOutlined,
  BugOutlined, BuildOutlined, BulbOutlined, CalculatorOutlined, CalendarOutlined,
  CameraOutlined, CarOutlined, CarryOutOutlined, CheckCircleOutlined, CheckOutlined,
  CheckSquareOutlined, ClockCircleOutlined, CloseCircleOutlined, CloseOutlined,
  CloudDownloadOutlined, CloudOutlined, CloudUploadOutlined, CodeOutlined, CoffeeOutlined,
  CommentOutlined, CompassOutlined, CompressOutlined, ContactsOutlined, ContainerOutlined,
  ControlOutlined, CopyOutlined, CopyrightOutlined, CreditCardOutlined, CrownOutlined,
  CustomerServiceOutlined, DashboardOutlined, DatabaseOutlined, DeleteOutlined,
  DeliveredProcedureOutlined, DeploymentUnitOutlined, DesktopOutlined, DiffOutlined,
  DingdingOutlined, DisconnectOutlined, DislikeOutlined, DollarCircleOutlined,
  DollarOutlined, DownCircleOutlined, DownloadOutlined, DownOutlined, DownSquareOutlined,
  DragOutlined, EditOutlined, EllipsisOutlined, EnterOutlined, EnvironmentOutlined,
  EuroCircleOutlined, EuroOutlined, ExceptionOutlined, ExclamationCircleOutlined,
  ExclamationOutlined, ExpandAltOutlined, ExpandOutlined, ExperimentOutlined,
  ExportOutlined, EyeInvisibleOutlined, EyeOutlined, FieldBinaryOutlined,
  FieldNumberOutlined, FieldStringOutlined, FieldTimeOutlined, FileAddOutlined,
  FileDoneOutlined, FileExcelOutlined, FileExclamationOutlined, FileImageOutlined,
  FileJpgOutlined, FileMarkdownOutlined, FileOutlined, FilePdfOutlined, FilePptOutlined,
  FileProtectOutlined, FileSearchOutlined, FileSyncOutlined, FileTextOutlined,
  FileUnknownOutlined, FileWordOutlined, FileZipOutlined, FilterOutlined, FireOutlined,
  FlagOutlined, FolderAddOutlined, FolderOpenOutlined, FolderOutlined, FolderViewOutlined,
  FormatPainterOutlined, ForwardOutlined, FrownOutlined, FullscreenExitOutlined,
  FullscreenOutlined, FunctionOutlined, FundProjectionScreenOutlined, FundViewOutlined,
  FunnelPlotOutlined, GatewayOutlined, GifOutlined, GiftOutlined, GlobalOutlined,
  GoldOutlined, GroupOutlined, HddOutlined, HeartOutlined, HistoryOutlined, HomeOutlined,
  HourglassOutlined, IdcardOutlined, ImportOutlined, InboxOutlined, InfoCircleOutlined,
  InfoOutlined, InsertRowAboveOutlined, InsertRowBelowOutlined, InsertRowLeftOutlined,
  InsertRowRightOutlined, InstagramOutlined, InteractionOutlined, IssuesCloseOutlined,
  KeyOutlined, LaptopOutlined, LayoutOutlined, LeftCircleOutlined, LeftOutlined,
  LeftSquareOutlined, LikeOutlined, LineChartOutlined, LinkOutlined, LinkedinOutlined,
  LoadingOutlined, LockOutlined, LoginOutlined, LogoutOutlined, MacCommandOutlined,
  MailOutlined, ManOutlined, MedicineBoxOutlined, MehOutlined, MenuFoldOutlined,
  MenuOutlined, MenuUnfoldOutlined, MessageOutlined, MobileOutlined, MoneyCollectOutlined,
  MonitorOutlined, MoreOutlined, NodeCollapseOutlined, NodeExpandOutlined,
  NodeIndexOutlined, NotificationOutlined, NumberOutlined, PaperClipOutlined,
  PartitionOutlined, PauseCircleOutlined, PauseOutlined, PayCircleOutlined,
  PercentageOutlined, PhoneOutlined, PictureOutlined, PieChartOutlined,
  PlayCircleOutlined, PlaySquareOutlined, PlusCircleOutlined, PlusOutlined,
  PlusSquareOutlined, PoweroffOutlined, PrinterOutlined, ProfileOutlined,
  ProjectOutlined, PropertySafetyOutlined, PullRequestOutlined, PushpinOutlined,
  QrcodeOutlined, QuestionCircleOutlined, QuestionOutlined, RadiusBottomleftOutlined,
  RadiusBottomrightOutlined, RadiusSettingOutlined, RadiusUpleftOutlined,
  RadiusUprightOutlined, ReadOutlined, ReconciliationOutlined, RedoOutlined,
  ReloadOutlined, RestOutlined, RightCircleOutlined, RightOutlined, RightSquareOutlined,
  RiseOutlined, RobotOutlined, RocketOutlined, RollbackOutlined, SafetyCertificateOutlined,
  SafetyOutlined, SaveOutlined, ScanOutlined, ScheduleOutlined, ScissorOutlined,
  SearchOutlined, SecurityScanOutlined, SelectOutlined, SendOutlined, SettingOutlined,
  ShakeOutlined, ShareAltOutlined, ShopOutlined, ShoppingCartOutlined, ShoppingOutlined,
  ShrinkOutlined, SisternodeOutlined, SketchOutlined, SkinOutlined, SmileOutlined,
  SolutionOutlined, SortAscendingOutlined, SortDescendingOutlined, SoundOutlined,
  StarOutlined, StepBackwardOutlined, StepForwardOutlined, StockOutlined, StopOutlined,
  SubnodeOutlined, SwapLeftOutlined, SwapOutlined, SwapRightOutlined, SwitcherOutlined,
  SyncOutlined, TableOutlined, TabletOutlined, TagOutlined, TagsOutlined, TeamOutlined,
  ThunderboltOutlined, ToTopOutlined, ToolOutlined, TrademarkCircleOutlined,
  TrademarkOutlined, TransactionOutlined, TranslationOutlined, TrophyOutlined,
  UndoOutlined, UnlockOutlined, UpCircleOutlined, UploadOutlined, UpOutlined,
  UpSquareOutlined, UsbOutlined, UserAddOutlined, UserDeleteOutlined, UserOutlined,
  UserSwitchOutlined, UsergroupAddOutlined, UsergroupDeleteOutlined, VideoCameraOutlined,
  WalletOutlined, WarningOutlined, WifiOutlined, WindowsOutlined, WomanOutlined,
  ZoomInOutlined, ZoomOutOutlined,
};

export default icons;
