<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Builder WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .log-warning { color: #ffc107; }
        select, input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔌 App Builder WebSocket Test</h1>
    
    <div class="container">
        <h2>Connection Settings</h2>
        <div>
            <label for="endpoint">Endpoint:</label>
            <select id="endpoint">
                <option value="app_builder">App Builder (app_builder)</option>
                <option value="test">Test (test)</option>
                <option value="echo">Echo (echo)</option>
                <option value="simple">Simple (simple)</option>
                <option value="health">Health (health)</option>
            </select>
        </div>
        <div>
            <button id="connect-btn">Connect</button>
            <button id="disconnect-btn" disabled>Disconnect</button>
            <button id="clear-log-btn">Clear Log</button>
        </div>
        <div id="status" class="status disconnected">Disconnected</div>
    </div>

    <div class="container">
        <h2>Send Message</h2>
        <div>
            <input type="text" id="message-input" placeholder="Enter message" disabled>
            <button id="send-btn" disabled>Send</button>
            <button id="ping-btn" disabled>Send Ping</button>
        </div>
    </div>

    <div class="container">
        <h2>Connection Log</h2>
        <div id="log"></div>
    </div>

    <script>
        let socket = null;
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const sendBtn = document.getElementById('send-btn');
        const pingBtn = document.getElementById('ping-btn');
        const messageInput = document.getElementById('message-input');
        const endpointSelect = document.getElementById('endpoint');
        const clearLogBtn = document.getElementById('clear-log-btn');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status, className) {
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }

        function updateButtons(connected) {
            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            sendBtn.disabled = !connected;
            pingBtn.disabled = !connected;
            messageInput.disabled = !connected;
        }

        function connect() {
            const endpoint = endpointSelect.value;
            const url = `ws://localhost:8000/ws/${endpoint}/`;
            
            log(`Connecting to ${url}...`, 'info');
            updateStatus('Connecting...', 'connecting');
            
            try {
                socket = new WebSocket(url);
                
                socket.onopen = function(event) {
                    log('✅ WebSocket connection established', 'success');
                    updateStatus('Connected', 'connected');
                    updateButtons(true);
                };
                
                socket.onmessage = function(event) {
                    log(`📨 Received: ${event.data}`, 'info');
                };
                
                socket.onclose = function(event) {
                    const reason = event.wasClean ? 'Clean close' : 'Unexpected close';
                    log(`🔌 Connection closed: ${reason} (Code: ${event.code})`, 'warning');
                    updateStatus('Disconnected', 'disconnected');
                    updateButtons(false);
                    socket = null;
                };
                
                socket.onerror = function(error) {
                    log(`❌ WebSocket error: ${error.message || 'Unknown error'}`, 'error');
                    updateStatus('Error', 'disconnected');
                    updateButtons(false);
                };
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`, 'error');
                updateStatus('Error', 'disconnected');
                updateButtons(false);
            }
        }

        function disconnect() {
            if (socket) {
                log('Disconnecting...', 'info');
                socket.close(1000, 'User requested disconnect');
            }
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (socket && message) {
                log(`📤 Sending: ${message}`, 'info');
                socket.send(message);
                messageInput.value = '';
            }
        }

        function sendPing() {
            if (socket) {
                const pingMessage = JSON.stringify({
                    type: 'ping',
                    timestamp: Date.now()
                });
                log(`📤 Sending ping: ${pingMessage}`, 'info');
                socket.send(pingMessage);
            }
        }

        function clearLog() {
            logDiv.innerHTML = '';
        }

        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        sendBtn.addEventListener('click', sendMessage);
        pingBtn.addEventListener('click', sendPing);
        clearLogBtn.addEventListener('click', clearLog);
        
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Initial log
        log('🚀 WebSocket test page loaded', 'success');
        log('Backend should be running on ws://localhost:8000', 'info');
        log('Make sure to start the backend with: daphne -b 0.0.0.0 -p 8000 app_builder_201.asgi:application', 'warning');
    </script>
</body>
</html>
