"""
Management command to create default templates for the App Builder.
This creates a comprehensive "Hello World" starter template and other essential templates.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from my_app.models import AppTemplate, LayoutTemplate, ComponentTemplate


class Command(BaseCommand):
    help = 'Create default templates for the App Builder'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing default templates before creating new ones',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Deleting existing default templates...')
            AppTemplate.objects.filter(name__startswith='[Default]').delete()
            LayoutTemplate.objects.filter(name__startswith='[Default]').delete()
            ComponentTemplate.objects.filter(name__startswith='[Default]').delete()

        # Get or create a system user for default templates
        system_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'User',
                'is_active': True
            }
        )

        if created:
            self.stdout.write(f'Created system user: {system_user.username}')

        # Create default templates
        self.create_hello_world_template(system_user)
        self.create_default_component_templates(system_user)
        self.create_default_layout_templates(system_user)
        self.create_additional_app_templates(system_user)

        self.stdout.write(
            self.style.SUCCESS('Successfully created default templates')
        )

    def create_hello_world_template(self, user):
        """Create the main Hello World starter template"""
        hello_world_template = {
            'name': '[Default] Hello World Starter',
            'description': 'A simple starter template with basic React components, responsive design, and sample content. Perfect for getting started with the App Builder.',
            'app_category': 'other',
            'components': {
                'pages': [
                    {
                        'name': 'home',
                        'title': 'Home',
                        'components': [
                            {
                                'id': 'header-1',
                                'type': 'header',
                                'props': {
                                    'title': 'Hello World!',
                                    'subtitle': 'Welcome to your new app built with App Builder',
                                    'backgroundColor': '#1890ff',
                                    'textColor': '#ffffff',
                                    'height': '200px',
                                    'textAlign': 'center'
                                },
                                'position': { 'x': 0, 'y': 0, 'width': 12, 'height': 3 }
                            },
                            {
                                'id': 'container-1',
                                'type': 'container',
                                'props': {
                                    'padding': '40px 20px',
                                    'maxWidth': '1200px',
                                    'margin': '0 auto',
                                    'backgroundColor': '#ffffff'
                                },
                                'position': { 'x': 0, 'y': 3, 'width': 12, 'height': 8 },
                                'children': [
                                    {
                                        'id': 'text-1',
                                        'type': 'text',
                                        'props': {
                                            'content': 'This is your first app! You can drag and drop components, edit their properties, and export your creation as working code.',
                                            'fontSize': '18px',
                                            'lineHeight': '1.6',
                                            'color': '#333333',
                                            'marginBottom': '30px'
                                        },
                                        'position': { 'x': 0, 'y': 0, 'width': 12, 'height': 2 }
                                    },
                                    {
                                        'id': 'button-1',
                                        'type': 'button',
                                        'props': {
                                            'text': 'Get Started',
                                            'backgroundColor': '#52c41a',
                                            'color': '#ffffff',
                                            'padding': '12px 24px',
                                            'borderRadius': '6px',
                                            'fontSize': '16px',
                                            'border': 'none',
                                            'cursor': 'pointer',
                                            'marginRight': '15px'
                                        },
                                        'position': { 'x': 0, 'y': 2, 'width': 3, 'height': 1 }
                                    },
                                    {
                                        'id': 'button-2',
                                        'type': 'button',
                                        'props': {
                                            'text': 'Learn More',
                                            'backgroundColor': 'transparent',
                                            'color': '#1890ff',
                                            'padding': '12px 24px',
                                            'borderRadius': '6px',
                                            'fontSize': '16px',
                                            'border': '2px solid #1890ff',
                                            'cursor': 'pointer'
                                        },
                                        'position': { 'x': 3, 'y': 2, 'width': 3, 'height': 1 }
                                    }
                                ]
                            },
                            {
                                'id': 'footer-1',
                                'type': 'footer',
                                'props': {
                                    'content': '© 2024 Your App. Built with App Builder.',
                                    'backgroundColor': '#f5f5f5',
                                    'textAlign': 'center',
                                    'padding': '20px',
                                    'color': '#666666',
                                    'fontSize': '14px'
                                },
                                'position': { 'x': 0, 'y': 11, 'width': 12, 'height': 1 }
                            }
                        ]
                    }
                ],
                'theme': {
                    'primaryColor': '#1890ff',
                    'secondaryColor': '#52c41a',
                    'backgroundColor': '#ffffff',
                    'textColor': '#333333',
                    'fontFamily': 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
                    'borderRadius': '6px',
                    'spacing': {
                        'small': '8px',
                        'medium': '16px',
                        'large': '24px',
                        'xlarge': '40px'
                    }
                },
                'layout': {
                    'type': 'grid',
                    'columns': 12,
                    'gap': '16px',
                    'responsive': True,
                    'breakpoints': {
                        'mobile': '768px',
                        'tablet': '1024px',
                        'desktop': '1200px'
                    }
                }
            },
            'default_props': {
                'theme': 'modern',
                'primaryColor': '#1890ff',
                'secondaryColor': '#52c41a',
                'fontFamily': 'Inter',
                'responsive': True,
                'animations': True
            },
            'required_components': ['header', 'container', 'text', 'button', 'footer'],
            'preview_image': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjVmNWY1Ii8+CjxyZWN0IHk9IjAiIHdpZHRoPSI0MDAiIGhlaWdodD0iODAiIGZpbGw9IiMxODkwZmYiLz4KPHR5cGUgeD0iMjAwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+SGVsbG8gV29ybGQhPC90ZXh0Pgo8dGV4dCB4PSIyMDAiIHk9IjEzMCIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjE2IiBmaWxsPSIjMzMzIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Zb3VyIGZpcnN0IGFwcCBidWlsdCB3aXRoIEFwcCBCdWlsZGVyPC90ZXh0Pgo8cmVjdCB4PSIxNDAiIHk9IjE1MCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSI0MCIgcng9IjYiIGZpbGw9IiM1MmM0MWEiLz4KPHR5cGUgeD0iMjAwIiB5PSIxNzUiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkdldCBTdGFydGVkPC90ZXh0Pgo8cmVjdCB5PSIyNjAiIHdpZHRoPSI0MDAiIGhlaWdodD0iNDAiIGZpbGw9IiNmNWY1ZjUiLz4KPHR5cGUgeD0iMjAwIiB5PSIyODUiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+wqkgMjAyNCBZb3VyIEFwcC4gQnVpbHQgd2l0aCBBcHAgQnVpbGRlci48L3RleHQ+Cjwvc3ZnPgo=',
            'is_public': True
        }

        template, created = AppTemplate.objects.get_or_create(
            name=hello_world_template['name'],
            defaults={
                'description': hello_world_template['description'],
                'app_category': hello_world_template['app_category'],
                'components': hello_world_template['components'],
                'default_props': hello_world_template['default_props'],
                'required_components': hello_world_template['required_components'],
                'preview_image': hello_world_template['preview_image'],
                'is_public': hello_world_template['is_public'],
                'user': user
            }
        )

        if created:
            self.stdout.write(f'Created Hello World template: {template.name}')
        else:
            self.stdout.write(f'Hello World template already exists: {template.name}')

    def create_default_component_templates(self, user):
        """Create default component templates"""
        component_templates = [
            {
                'name': '[Default] Modern Button',
                'description': 'A modern, accessible button component with hover effects',
                'component_type': 'button',
                'default_props': {
                    'text': 'Click me',
                    'backgroundColor': '#1890ff',
                    'color': '#ffffff',
                    'padding': '12px 24px',
                    'borderRadius': '6px',
                    'border': 'none',
                    'fontSize': '16px',
                    'cursor': 'pointer',
                    'transition': 'all 0.3s ease',
                    'hover': {
                        'backgroundColor': '#40a9ff'
                    }
                }
            },
            {
                'name': '[Default] Responsive Header',
                'description': 'A responsive header component with title and subtitle',
                'component_type': 'header',
                'default_props': {
                    'title': 'Page Title',
                    'subtitle': 'Page subtitle or description',
                    'backgroundColor': '#1890ff',
                    'textColor': '#ffffff',
                    'padding': '40px 20px',
                    'textAlign': 'center',
                    'responsive': True
                }
            },
            {
                'name': '[Default] Text Block',
                'description': 'A flexible text component with typography options',
                'component_type': 'text',
                'default_props': {
                    'content': 'Your text content goes here.',
                    'fontSize': '16px',
                    'lineHeight': '1.6',
                    'color': '#333333',
                    'fontFamily': 'Inter, sans-serif',
                    'margin': '0 0 16px 0'
                }
            },
            {
                'name': '[Default] Flex Container',
                'description': 'A flexible container for organizing components',
                'component_type': 'container',
                'default_props': {
                    'display': 'flex',
                    'flexDirection': 'column',
                    'gap': '16px',
                    'padding': '20px',
                    'backgroundColor': '#ffffff',
                    'borderRadius': '8px',
                    'boxShadow': '0 2px 8px rgba(0,0,0,0.1)'
                }
            }
        ]

        for template_data in component_templates:
            template, created = ComponentTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'description': template_data['description'],
                    'component_type': template_data['component_type'],
                    'default_props': str(template_data['default_props']),
                    'user': user,
                    'is_public': True
                }
            )

            if created:
                self.stdout.write(f'Created component template: {template.name}')

    def create_default_layout_templates(self, user):
        """Create default layout templates"""
        layout_templates = [
            {
                'name': '[Default] Hero Section Layout',
                'description': 'A hero section layout with header, content, and call-to-action',
                'layout_type': 'hero',
                'components': {
                    'structure': [
                        {
                            'type': 'header',
                            'position': 'top',
                            'props': {
                                'height': '60vh',
                                'backgroundImage': 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)'
                            }
                        },
                        {
                            'type': 'container',
                            'position': 'center',
                            'props': {
                                'textAlign': 'center',
                                'color': 'white'
                            }
                        }
                    ]
                }
            },
            {
                'name': '[Default] Two Column Layout',
                'description': 'A responsive two-column layout',
                'layout_type': 'grid',
                'components': {
                    'grid': {
                        'columns': 2,
                        'gap': '24px',
                        'responsive': True,
                        'breakpoint': '768px'
                    }
                }
            }
        ]

        for template_data in layout_templates:
            template, created = LayoutTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'description': template_data['description'],
                    'layout_type': template_data['layout_type'],
                    'components': template_data['components'],
                    'default_props': {},
                    'user': user,
                    'is_public': True
                }
            )

            if created:
                self.stdout.write(f'Created layout template: {template.name}')

    def create_additional_app_templates(self, user):
        """Create additional starter app templates"""
        additional_templates = [
            {
                'name': '[Default] Simple Landing Page',
                'description': 'A clean, modern landing page template with hero section, features, and contact form',
                'app_category': 'landing',
                'components': {
                    'pages': [
                        {
                            'name': 'home',
                            'title': 'Landing Page',
                            'components': [
                                {
                                    'id': 'hero-1',
                                    'type': 'hero',
                                    'props': {
                                        'title': 'Build Amazing Apps',
                                        'subtitle': 'Create beautiful, functional applications with our drag-and-drop builder',
                                        'backgroundImage': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                        'height': '70vh'
                                    }
                                },
                                {
                                    'id': 'features-1',
                                    'type': 'features',
                                    'props': {
                                        'title': 'Why Choose Our Platform',
                                        'features': [
                                            {
                                                'title': 'Easy to Use',
                                                'description': 'Drag and drop components to build your app',
                                                'icon': 'drag'
                                            },
                                            {
                                                'title': 'Export Code',
                                                'description': 'Generate clean, production-ready code',
                                                'icon': 'code'
                                            },
                                            {
                                                'title': 'Responsive',
                                                'description': 'Your apps work perfectly on all devices',
                                                'icon': 'mobile'
                                            }
                                        ]
                                    }
                                }
                            ]
                        }
                    ]
                },
                'default_props': {
                    'theme': 'modern',
                    'primaryColor': '#667eea',
                    'responsive': True
                },
                'required_components': ['hero', 'features', 'button'],
                'preview_image': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSJ1cmwoI2dyYWQpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNjY3ZWVhO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM3NjRiYTI7c3RvcC1vcGFjaXR5OjEiIC8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHR5cGUgeD0iMjAwIiB5PSIxMDAiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIyOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkJ1aWxkIEFtYXppbmcgQXBwczwvdGV4dD4KPHR5cGUgeD0iMjAwIiB5PSIxMzAiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkNyZWF0ZSBiZWF1dGlmdWwgYXBwbGljYXRpb25zPC90ZXh0Pgo8cmVjdCB4PSIxNTAiIHk9IjE2MCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSI0MCIgcng9IjgiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMiIvPgo8dGV4dCB4PSIyMDAiIHk9IjE4NSIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+R2V0IFN0YXJ0ZWQ8L3RleHQ+Cjwvc3ZnPgo=',
                'is_public': True
            },
            {
                'name': '[Default] Dashboard Template',
                'description': 'A basic dashboard template with sidebar navigation and content area',
                'app_category': 'dashboard',
                'components': {
                    'pages': [
                        {
                            'name': 'dashboard',
                            'title': 'Dashboard',
                            'layout': 'sidebar',
                            'components': [
                                {
                                    'id': 'sidebar-1',
                                    'type': 'sidebar',
                                    'props': {
                                        'width': '250px',
                                        'backgroundColor': '#001529',
                                        'items': [
                                            { 'label': 'Dashboard', 'icon': 'dashboard', 'active': True },
                                            { 'label': 'Analytics', 'icon': 'chart' },
                                            { 'label': 'Settings', 'icon': 'settings' }
                                        ]
                                    }
                                },
                                {
                                    'id': 'content-1',
                                    'type': 'content',
                                    'props': {
                                        'padding': '24px',
                                        'backgroundColor': '#f0f2f5'
                                    }
                                }
                            ]
                        }
                    ]
                },
                'default_props': {
                    'theme': 'dashboard',
                    'primaryColor': '#1890ff',
                    'layout': 'sidebar'
                },
                'required_components': ['sidebar', 'content', 'card'],
                'preview_image': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjBmMmY1Ii8+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMDAxNTI5Ii8+CjxyZWN0IHg9IjEyMCIgeT0iMjAiIHdpZHRoPSIyNjAiIGhlaWdodD0iODAiIHJ4PSI4IiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIxMjAiIHk9IjEyMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxNjAiIHJ4PSI4IiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIyNjAiIHk9IjEyMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxNjAiIHJ4PSI4IiBmaWxsPSJ3aGl0ZSIvPgo8dGV4dCB4PSI1MCIgeT0iNDAiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRhc2hib2FyZDwvdGV4dD4KPHR5cGUgeD0iNTAiIHk9IjgwIiBmb250LWZhbWlseT0iSW50ZXIiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiNhYWEiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkFuYWx5dGljczwvdGV4dD4KPHR5cGUgeD0iNTAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjEyIiBmaWxsPSIjYWFhIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5TZXR0aW5nczwvdGV4dD4KPC9zdmc+Cg==',
                'is_public': True
            }
        ]

        for template_data in additional_templates:
            template, created = AppTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'description': template_data['description'],
                    'app_category': template_data['app_category'],
                    'components': template_data['components'],
                    'default_props': template_data['default_props'],
                    'required_components': template_data['required_components'],
                    'preview_image': template_data['preview_image'],
                    'is_public': template_data['is_public'],
                    'user': user
                }
            )

            if created:
                self.stdout.write(f'Created app template: {template.name}')
