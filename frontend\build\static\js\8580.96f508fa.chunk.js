"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8580],{

/***/ 12576:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(5556);
/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);
/* harmony import */ var _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(18768);





var _excluded = ["children", "role", "ariaLabel", "ariaLabelledBy", "ariaDescribedBy", "ariaExpanded", "ariaSelected", "ariaChecked", "ariaDisabled", "ariaHidden", "ariaLive", "ariaAtomic", "tabIndex", "onKeyDown", "onKeyUp", "onKeyPress", "autoFocus", "onFocus", "onBlur", "focusTrap", "restoreFocus", "interactive", "disabled", "onClick", "onDoubleClick", "draggable", "onDragStart", "onDragEnd", "onDrop", "onDragOver", "onDragEnter", "onDragLeave", "skipLink", "skipTarget", "announcements", "invalid", "errorMessage", "loading", "loadingText", "componentId", "componentType", "className", "style", "testId", "enableA11yTesting"];
var _templateObject, _templateObject2, _templateObject3;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Accessible Component Wrapper
 * 
 * A comprehensive wrapper component that adds accessibility features
 * to any component, ensuring WCAG 2.1 AA compliance.
 */






var AccessibleWrapper = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  /* Ensure focus is visible */\n  &:focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n    border-radius: ", ";\n  }\n  \n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 1px solid;\n    \n    &:focus-visible {\n      outline: 3px solid;\n      outline-offset: 2px;\n    }\n  }\n  \n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none !important;\n    animation: none !important;\n  }\n  \n  /* Ensure minimum touch target size */\n  ", "\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.sm, function (props) {
  return props.interactive && "\n    min-width: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.accessibility.minTouchTarget.width, ";\n    min-height: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.accessibility.minTouchTarget.height, ";\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n  ");
});
var SkipLink = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.a(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  position: absolute;\n  top: -40px;\n  left: 6px;\n  background: ", ";\n  color: ", ";\n  padding: ", " ", ";\n  border-radius: ", ";\n  text-decoration: none;\n  font-weight: ", ";\n  z-index: ", ";\n  transition: ", ";\n  \n  &:focus {\n    top: 6px;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.zIndex.skipLink, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"]);
var LiveRegion = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.accessibility.srOnly);
var AccessibleComponent = function AccessibleComponent(_ref) {
  var children = _ref.children,
    role = _ref.role,
    ariaLabel = _ref.ariaLabel,
    ariaLabelledBy = _ref.ariaLabelledBy,
    ariaDescribedBy = _ref.ariaDescribedBy,
    ariaExpanded = _ref.ariaExpanded,
    ariaSelected = _ref.ariaSelected,
    ariaChecked = _ref.ariaChecked,
    ariaDisabled = _ref.ariaDisabled,
    ariaHidden = _ref.ariaHidden,
    ariaLive = _ref.ariaLive,
    ariaAtomic = _ref.ariaAtomic,
    tabIndex = _ref.tabIndex,
    onKeyDown = _ref.onKeyDown,
    onKeyUp = _ref.onKeyUp,
    onKeyPress = _ref.onKeyPress,
    autoFocus = _ref.autoFocus,
    onFocus = _ref.onFocus,
    onBlur = _ref.onBlur,
    focusTrap = _ref.focusTrap,
    restoreFocus = _ref.restoreFocus,
    _ref$interactive = _ref.interactive,
    interactive = _ref$interactive === void 0 ? false : _ref$interactive,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    onClick = _ref.onClick,
    onDoubleClick = _ref.onDoubleClick,
    _ref$draggable = _ref.draggable,
    draggable = _ref$draggable === void 0 ? false : _ref$draggable,
    onDragStart = _ref.onDragStart,
    onDragEnd = _ref.onDragEnd,
    onDrop = _ref.onDrop,
    onDragOver = _ref.onDragOver,
    onDragEnter = _ref.onDragEnter,
    onDragLeave = _ref.onDragLeave,
    skipLink = _ref.skipLink,
    skipTarget = _ref.skipTarget,
    _ref$announcements = _ref.announcements,
    announcements = _ref$announcements === void 0 ? [] : _ref$announcements,
    _ref$invalid = _ref.invalid,
    invalid = _ref$invalid === void 0 ? false : _ref$invalid,
    errorMessage = _ref.errorMessage,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading,
    _ref$loadingText = _ref.loadingText,
    loadingText = _ref$loadingText === void 0 ? 'Loading...' : _ref$loadingText,
    componentId = _ref.componentId,
    componentType = _ref.componentType,
    className = _ref.className,
    style = _ref.style,
    testId = _ref.testId,
    _ref$enableA11yTestin = _ref.enableA11yTesting,
    enableA11yTesting = _ref$enableA11yTestin === void 0 ? "production" === 'development' : _ref$enableA11yTestin,
    otherProps = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_ref, _excluded);
  var wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    focusTrapInstance = _useState2[0],
    setFocusTrapInstance = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    restoreFocusFunction = _useState4[0],
    setRestoreFocusFunction = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    a11yViolations = _useState6[0],
    setA11yViolations = _useState6[1];

  // Generate unique IDs for ARIA relationships
  var elementId = componentId || _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.aria.generateId('accessible-component');
  var errorId = "".concat(elementId, "-error");
  var descriptionId = "".concat(elementId, "-description");

  // Handle keyboard navigation
  var handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (e) {
    // Handle escape key for modals/dropdowns
    if (e.key === _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.keyboard.KEYS.ESCAPE && focusTrap) {
      e.preventDefault();
      if (focusTrapInstance) {
        focusTrapInstance.deactivate();
      }
    }

    // Handle activation keys for interactive elements
    if (interactive && (e.key === _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.keyboard.KEYS.ENTER || e.key === _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.keyboard.KEYS.SPACE)) {
      if (!disabled && onClick) {
        e.preventDefault();
        onClick(e);
      }
    }

    // Call custom keyDown handler
    if (onKeyDown) {
      onKeyDown(e);
    }
  }, [interactive, disabled, onClick, onKeyDown, focusTrap, focusTrapInstance]);

  // Handle focus events
  var handleFocus = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (e) {
    // Announce focus change for screen readers
    if (ariaLabel || ariaLabelledBy) {
      var _document$getElementB;
      var label = ariaLabel || ((_document$getElementB = document.getElementById(ariaLabelledBy)) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.textContent);
      if (label) {
        _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.aria.announce("Focused on ".concat(label), 'polite');
      }
    }
    if (onFocus) {
      onFocus(e);
    }
  }, [ariaLabel, ariaLabelledBy, onFocus]);

  // Handle blur events
  var handleBlur = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (e) {
    if (onBlur) {
      onBlur(e);
    }
  }, [onBlur]);

  // Handle drag and drop with accessibility
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (e) {
    if (draggable && !disabled) {
      // Set accessible drag data
      var dragData = {
        id: elementId,
        type: componentType,
        label: ariaLabel || 'Component'
      };
      e.dataTransfer.setData('application/json', JSON.stringify(dragData));
      e.dataTransfer.effectAllowed = 'move';

      // Announce drag start
      _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.aria.announce("Started dragging ".concat(dragData.label), 'assertive');
      if (onDragStart) {
        onDragStart(e);
      }
    }
  }, [draggable, disabled, elementId, componentType, ariaLabel, onDragStart]);
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (e) {
    if (draggable) {
      // Announce drag end
      _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.aria.announce('Drag operation completed', 'polite');
      if (onDragEnd) {
        onDragEnd(e);
      }
    }
  }, [draggable, onDragEnd]);
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (e) {
    e.preventDefault();
    try {
      var dragData = JSON.parse(e.dataTransfer.getData('application/json'));

      // Announce successful drop
      _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.aria.announce("Dropped ".concat(dragData.label), 'assertive');
      if (onDrop) {
        onDrop(e, dragData);
      }
    } catch (error) {
      console.warn('Invalid drag data:', error);
    }
  }, [onDrop]);

  // Set up focus trap
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (focusTrap && wrapperRef.current) {
      var trap = _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.focus.createFocusTrap(wrapperRef);
      setFocusTrapInstance(trap);
      trap.activate();
      return function () {
        trap.deactivate();
      };
    }
  }, [focusTrap]);

  // Set up focus restoration
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (restoreFocus) {
      var restoreFunction = _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.focus.createFocusRestore();
      setRestoreFocusFunction(function () {
        return restoreFunction;
      });
      return restoreFunction;
    }
  }, [restoreFocus]);

  // Auto focus on mount
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (autoFocus && wrapperRef.current) {
      wrapperRef.current.focus();
    }
  }, [autoFocus]);

  // Accessibility testing in development
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (enableA11yTesting && wrapperRef.current) {
      var audit = _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.testing.auditElement(wrapperRef.current);
      if (audit.issues.length > 0) {
        setA11yViolations(audit.issues);
        _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.monitoring.logViolations(audit.issues.map(function (issue) {
          return {
            description: issue,
            element: wrapperRef.current
          };
        }));
      }
    }
  }, [enableA11yTesting, children]);

  // Handle live region announcements
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    announcements.forEach(function (announcement) {
      if (announcement.message) {
        _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.aria.announce(announcement.message, announcement.priority || 'polite');
      }
    });
  }, [announcements]);

  // Build ARIA attributes
  var ariaAttributes = {
    role: role,
    'aria-label': ariaLabel,
    'aria-labelledby': ariaLabelledBy,
    'aria-describedby': [ariaDescribedBy, errorMessage ? errorId : null, invalid ? descriptionId : null].filter(Boolean).join(' ') || undefined,
    'aria-expanded': ariaExpanded,
    'aria-selected': ariaSelected,
    'aria-checked': ariaChecked,
    'aria-disabled': ariaDisabled || disabled,
    'aria-hidden': ariaHidden,
    'aria-live': ariaLive,
    'aria-atomic': ariaAtomic,
    'aria-invalid': invalid,
    'aria-busy': loading
  };

  // Build drag and drop attributes
  var dragDropAttributes = draggable ? _objectSpread({
    draggable: !disabled,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
    onDrop: handleDrop,
    onDragOver: onDragOver,
    onDragEnter: onDragEnter,
    onDragLeave: onDragLeave
  }, _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.dragDrop.createDragDropAttributes({
    id: elementId
  }, false, !!onDrop)) : {};

  // Build event handlers
  var eventHandlers = _objectSpread({
    onKeyDown: handleKeyDown,
    onKeyUp: onKeyUp,
    onKeyPress: onKeyPress,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onClick: interactive && !disabled ? onClick : undefined,
    onDoubleClick: interactive && !disabled ? onDoubleClick : undefined
  }, dragDropAttributes);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Fragment, null, skipLink && skipTarget && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(SkipLink, {
    href: "#".concat(skipTarget)
  }, skipLink), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(AccessibleWrapper, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
    ref: wrapperRef,
    id: elementId,
    className: className,
    style: style,
    interactive: interactive,
    tabIndex: interactive ? tabIndex !== undefined ? tabIndex : 0 : tabIndex,
    "data-testid": testId
  }, ariaAttributes, eventHandlers, otherProps), children, errorMessage && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    id: errorId,
    className: "sr-only",
    role: "alert"
  }, errorMessage), loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    className: "sr-only",
    "aria-live": "polite"
  }, loadingText), draggable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", _utils_accessibility__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.dragDrop.createDragInstructions(elementId))), announcements.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(LiveRegion, {
    "aria-live": "polite",
    "aria-atomic": "true"
  }, announcements.map(function (announcement, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      key: index
    }, announcement.message);
  })),  false && /*#__PURE__*/0);
};
AccessibleComponent.propTypes = {
  children: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().node).isRequired,
  // Accessibility props
  role: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  ariaLabel: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  ariaLabelledBy: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  ariaDescribedBy: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  ariaExpanded: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  ariaSelected: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  ariaChecked: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  ariaDisabled: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  ariaHidden: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  ariaLive: prop_types__WEBPACK_IMPORTED_MODULE_6___default().oneOf(['polite', 'assertive', 'off']),
  ariaAtomic: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  // Keyboard navigation props
  tabIndex: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().number),
  onKeyDown: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onKeyUp: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onKeyPress: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  // Focus management props
  autoFocus: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  onFocus: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onBlur: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  focusTrap: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  restoreFocus: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  // Interactive props
  interactive: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  onClick: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onDoubleClick: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  // Drag and drop props
  draggable: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  onDragStart: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onDragEnd: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onDrop: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onDragOver: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onDragEnter: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  onDragLeave: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().func),
  // Skip link props
  skipLink: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  skipTarget: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  // Live region props
  announcements: prop_types__WEBPACK_IMPORTED_MODULE_6___default().arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_6___default().shape({
    message: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string).isRequired,
    priority: prop_types__WEBPACK_IMPORTED_MODULE_6___default().oneOf(['polite', 'assertive'])
  })),
  // Validation props
  invalid: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  errorMessage: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  // Loading state props
  loading: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool),
  loadingText: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  // Component identification
  componentId: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  componentType: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  // Style props
  className: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  style: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().object),
  // Testing props
  testId: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().string),
  enableA11yTesting: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().bool)
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AccessibleComponent);

/***/ }),

/***/ 19361:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Performance Tools Component
 * 
 * Comprehensive performance analysis tools for the App Builder including
 * bundle size tracking, render performance metrics, and optimization suggestions.
 */





var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;

// Styled Components
var PerformanceContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 16px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"])));
var MetricsGrid = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 24px;\n"])));
var PerformanceChart = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: #fafafa;\n  border: 1px solid #d9d9d9;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 16px 0;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n"])));
var OptimizationCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 12px;\n  \n  .ant-card-head {\n    background: ", ";\n  }\n"])), function (props) {
  switch (props.severity) {
    case 'high':
      return '#fff2f0';
    case 'medium':
      return '#fffbe6';
    case 'low':
      return '#f6ffed';
    default:
      return '#fafafa';
  }
});

// Mock performance data
var generateMockPerformanceData = function generateMockPerformanceData() {
  return {
    coreWebVitals: {
      fcp: {
        value: 1.2,
        threshold: 1.8,
        status: 'good'
      },
      lcp: {
        value: 2.1,
        threshold: 2.5,
        status: 'good'
      },
      fid: {
        value: 45,
        threshold: 100,
        status: 'good'
      },
      cls: {
        value: 0.08,
        threshold: 0.1,
        status: 'good'
      },
      ttfb: {
        value: 0.6,
        threshold: 0.8,
        status: 'good'
      }
    },
    bundleSize: {
      total: 245.6,
      javascript: 180.2,
      css: 45.8,
      images: 19.6,
      threshold: 250
    },
    renderMetrics: {
      componentsRendered: 24,
      averageRenderTime: 12.5,
      slowestComponent: 'DataTable',
      slowestRenderTime: 45.2,
      reRenders: 8
    },
    memoryUsage: {
      used: 42.8,
      total: 100,
      peak: 58.3,
      threshold: 80
    },
    networkRequests: {
      total: 12,
      cached: 8,
      failed: 1,
      averageTime: 245
    }
  };
};
var generateOptimizationSuggestions = function generateOptimizationSuggestions() {
  return [{
    id: 'opt-1',
    title: 'Optimize Bundle Size',
    description: 'Consider code splitting for large components to reduce initial bundle size.',
    severity: 'medium',
    impact: 'high',
    effort: 'medium',
    category: 'bundle'
  }, {
    id: 'opt-2',
    title: 'Reduce Re-renders',
    description: 'Use React.memo() for components that render frequently with same props.',
    severity: 'low',
    impact: 'medium',
    effort: 'low',
    category: 'rendering'
  }, {
    id: 'opt-3',
    title: 'Implement Virtual Scrolling',
    description: 'Large lists should use virtual scrolling to improve performance.',
    severity: 'high',
    impact: 'high',
    effort: 'high',
    category: 'rendering'
  }, {
    id: 'opt-4',
    title: 'Optimize Images',
    description: 'Use WebP format and lazy loading for better image performance.',
    severity: 'medium',
    impact: 'medium',
    effort: 'low',
    category: 'assets'
  }];
};

/**
 * PerformanceTools Component
 */
var PerformanceTools = function PerformanceTools(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onOptimizationApply = _ref.onOptimizationApply,
    _ref$realTimeMonitori = _ref.realTimeMonitoring,
    realTimeMonitoring = _ref$realTimeMonitori === void 0 ? true : _ref$realTimeMonitori,
    _ref$showSuggestions = _ref.showSuggestions,
    showSuggestions = _ref$showSuggestions === void 0 ? true : _ref$showSuggestions,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(generateMockPerformanceData()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    performanceData = _useState4[0],
    setPerformanceData = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(generateOptimizationSuggestions()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    optimizationSuggestions = _useState6[0],
    setOptimizationSuggestions = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(realTimeMonitoring),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    monitoringEnabled = _useState8[0],
    setMonitoringEnabled = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('all'),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    selectedMetric = _useState0[0],
    setSelectedMetric = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    showDetails = _useState10[0],
    setShowDetails = _useState10[1];

  // Computed metrics
  var performanceScore = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var coreWebVitals = performanceData.coreWebVitals;
    var scores = Object.values(coreWebVitals).map(function (metric) {
      if (metric.status === 'good') return 100;
      if (metric.status === 'needs-improvement') return 75;
      return 50;
    });
    return Math.round(scores.reduce(function (sum, score) {
      return sum + score;
    }, 0) / scores.length);
  }, [performanceData]);
  var bundleHealthScore = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var bundleSize = performanceData.bundleSize;
    var ratio = bundleSize.total / bundleSize.threshold;
    if (ratio <= 0.7) return 100;
    if (ratio <= 0.9) return 75;
    if (ratio <= 1.0) return 50;
    return 25;
  }, [performanceData]);

  // Core Web Vitals table columns
  var webVitalsColumns = [{
    title: 'Metric',
    dataIndex: 'name',
    key: 'name',
    render: function render(text) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, text);
    }
  }, {
    title: 'Value',
    dataIndex: 'value',
    key: 'value',
    render: function render(value, record) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, value), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        type: "secondary"
      }, record.unit));
    }
  }, {
    title: 'Threshold',
    dataIndex: 'threshold',
    key: 'threshold',
    render: function render(threshold, record) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        type: "secondary"
      }, threshold, record.unit);
    }
  }, {
    title: 'Status',
    dataIndex: 'status',
    key: 'status',
    render: function render(status) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
        color: status === 'good' ? 'green' : status === 'needs-improvement' ? 'orange' : 'red'
      }, status.toUpperCase().replace('-', ' '));
    }
  }];

  // Transform Core Web Vitals data for table
  var webVitalsData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var coreWebVitals = performanceData.coreWebVitals;
    return [_objectSpread(_objectSpread({
      name: 'First Contentful Paint'
    }, coreWebVitals.fcp), {}, {
      unit: 's'
    }), _objectSpread(_objectSpread({
      name: 'Largest Contentful Paint'
    }, coreWebVitals.lcp), {}, {
      unit: 's'
    }), _objectSpread(_objectSpread({
      name: 'First Input Delay'
    }, coreWebVitals.fid), {}, {
      unit: 'ms'
    }), _objectSpread(_objectSpread({
      name: 'Cumulative Layout Shift'
    }, coreWebVitals.cls), {}, {
      unit: ''
    }), _objectSpread(_objectSpread({
      name: 'Time to First Byte'
    }, coreWebVitals.ttfb), {}, {
      unit: 's'
    })];
  }, [performanceData]);

  // Handle optimization suggestion actions
  var handleApplyOptimization = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (suggestion) {
    if (onOptimizationApply) {
      onOptimizationApply(suggestion);
    }
    setOptimizationSuggestions(function (prev) {
      return prev.filter(function (s) {
        return s.id !== suggestion.id;
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_4__/* .notification */ .Ew.success({
      message: 'Optimization Applied',
      description: "".concat(suggestion.title, " has been applied successfully."),
      duration: 3
    });
  }, [onOptimizationApply]);
  var handleDismissSuggestion = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (suggestionId) {
    setOptimizationSuggestions(function (prev) {
      return prev.filter(function (s) {
        return s.id !== suggestionId;
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_4__/* .notification */ .Ew.info({
      message: 'Suggestion Dismissed',
      duration: 2
    });
  }, []);

  // Refresh performance data
  var refreshData = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setPerformanceData(generateMockPerformanceData());
    antd__WEBPACK_IMPORTED_MODULE_4__/* .notification */ .Ew.success({
      message: 'Performance Data Refreshed',
      duration: 2
    });
  }, []);

  // Real-time monitoring effect
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!monitoringEnabled) return;
    var interval = setInterval(function () {
      setPerformanceData(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          renderMetrics: _objectSpread(_objectSpread({}, prev.renderMetrics), {}, {
            componentsRendered: prev.renderMetrics.componentsRendered + Math.floor(Math.random() * 3),
            averageRenderTime: Math.max(5, prev.renderMetrics.averageRenderTime + (Math.random() - 0.5) * 2),
            reRenders: prev.renderMetrics.reRenders + Math.floor(Math.random() * 2)
          }),
          memoryUsage: _objectSpread(_objectSpread({}, prev.memoryUsage), {}, {
            used: Math.min(prev.memoryUsage.total * 0.9, Math.max(20, prev.memoryUsage.used + (Math.random() - 0.5) * 5))
          })
        });
      });
    }, 2000);
    return function () {
      return clearInterval(interval);
    };
  }, [monitoringEnabled]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PerformanceContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    justify: "space-between",
    align: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 4,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DashboardOutlined */ .zpd, {
    style: {
      marginRight: 8,
      color: '#1890ff'
    }
  }), "Performance Tools")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    checked: monitoringEnabled,
    onChange: setMonitoringEnabled,
    checkedChildren: "Live",
    unCheckedChildren: "Static"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ReloadOutlined */ .KF4, null),
    onClick: refreshData
  }, "Refresh"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
    onClick: function onClick() {
      return setShowDetails(true);
    }
  }, "Details"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MetricsGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Performance Score",
    value: performanceScore,
    suffix: "/100",
    valueStyle: {
      color: performanceScore >= 80 ? '#3f8600' : performanceScore >= 60 ? '#faad14' : '#cf1322'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Bundle Size",
    value: performanceData.bundleSize.total,
    suffix: "KB",
    valueStyle: {
      color: bundleHealthScore >= 75 ? '#3f8600' : '#cf1322'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FileTextOutlined */ .y9H, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Avg Render Time",
    value: performanceData.renderMetrics.averageRenderTime,
    suffix: "ms",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ClockCircleOutlined */ .L8Y, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Memory Usage",
    value: Math.round(performanceData.memoryUsage.used / performanceData.memoryUsage.total * 100),
    suffix: "%",
    valueStyle: {
      color: performanceData.memoryUsage.used / performanceData.memoryUsage.total > 0.8 ? '#cf1322' : '#3f8600'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .HddOutlined */ .nRz, null)
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: "Overview",
    key: "overview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    title: "Core Web Vitals",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Table */ .XI, {
    dataSource: webVitalsData,
    columns: webVitalsColumns,
    pagination: false,
    size: "small"
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    title: "Bundle Analysis",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Progress */ .ke, {
    percent: Math.round(performanceData.bundleSize.total / performanceData.bundleSize.threshold * 100),
    status: bundleHealthScore >= 75 ? 'success' : 'exception',
    format: function format() {
      return "".concat(performanceData.bundleSize.total, "KB / ").concat(performanceData.bundleSize.threshold, "KB");
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    gutter: [8, 8]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "JavaScript:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, performanceData.bundleSize.javascript, "KB")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "CSS:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, performanceData.bundleSize.css, "KB")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Images:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, performanceData.bundleSize.images, "KB")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Total:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, performanceData.bundleSize.total, "KB")))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    gutter: [16, 16],
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    title: "Render Performance",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    gutter: [8, 8]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Components Rendered",
    value: performanceData.renderMetrics.componentsRendered,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MonitorOutlined */ .rwR, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Re-renders",
    value: performanceData.renderMetrics.reRenders,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ReloadOutlined */ .KF4, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, {
    style: {
      margin: '12px 0'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Slowest Component:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    strong: true
  }, performanceData.renderMetrics.slowestComponent), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, " (", performanceData.renderMetrics.slowestRenderTime, "ms)"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    title: "Memory & Network",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    gutter: [8, 8]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Memory Usage:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Progress */ .ke, {
    percent: Math.round(performanceData.memoryUsage.used / performanceData.memoryUsage.total * 100),
    format: function format() {
      return "".concat(performanceData.memoryUsage.used, "MB / ").concat(performanceData.memoryUsage.total, "MB");
    },
    status: performanceData.memoryUsage.used / performanceData.memoryUsage.total > 0.8 ? 'exception' : 'success'
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Network Requests",
    value: performanceData.networkRequests.total,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LineChartOutlined */ .BdS, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Statistic */ .jL, {
    title: "Cached",
    value: performanceData.networkRequests.cached,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CheckCircleOutlined */ .hWy, null)
  }))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: "Optimization",
    key: "optimization"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    justify: "space-between",
    align: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 5
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BulbOutlined */ .o3f, {
    style: {
      marginRight: 8
    }
  }), "Performance Suggestions")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6, {
    value: selectedMetric,
    onChange: setSelectedMetric,
    style: {
      width: 120
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
    value: "all"
  }, "All"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
    value: "bundle"
  }, "Bundle"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
    value: "rendering"
  }, "Rendering"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Option, {
    value: "assets"
  }, "Assets"))))), optimizationSuggestions.filter(function (suggestion) {
    return selectedMetric === 'all' || suggestion.category === selectedMetric;
  }).map(function (suggestion) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(OptimizationCard, {
      key: suggestion.id,
      size: "small",
      severity: suggestion.severity,
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, suggestion.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
        color: suggestion.severity === 'high' ? 'red' : suggestion.severity === 'medium' ? 'orange' : 'green'
      }, suggestion.severity.toUpperCase()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
        color: "blue"
      }, suggestion.impact.toUpperCase(), " IMPACT")),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        size: "small",
        type: "primary",
        onClick: function onClick() {
          return handleApplyOptimization(suggestion);
        }
      }, "Apply"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        size: "small",
        onClick: function onClick() {
          return handleDismissSuggestion(suggestion.id);
        }
      }, "Dismiss"))
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, {
      style: {
        margin: 0
      }
    }, suggestion.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        marginTop: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      type: "secondary"
    }, "Effort: "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
      color: suggestion.effort === 'high' ? 'red' : suggestion.effort === 'medium' ? 'orange' : 'green'
    }, suggestion.effort.toUpperCase())));
  }), optimizationSuggestions.filter(function (suggestion) {
    return selectedMetric === 'all' || suggestion.category === selectedMetric;
  }).length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
    message: "No optimization suggestions",
    description: "Your application is performing well! No immediate optimizations needed.",
    type: "success",
    showIcon: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: "Charts",
    key: "charts"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    title: "Performance Trends",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PerformanceChart, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LineChartOutlined */ .BdS, {
    style: {
      fontSize: 48,
      color: '#d9d9d9',
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Performance charts will be displayed here"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "Showing trends for Core Web Vitals and bundle size"))))))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Modal */ .aF, {
    title: "Performance Details",
    open: showDetails,
    onCancel: function onCancel() {
      return setShowDetails(false);
    },
    footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      key: "close",
      onClick: function onClick() {
        return setShowDetails(false);
      }
    }, "Close"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      key: "export",
      type: "primary",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DownloadOutlined */ .jsW, null)
    }, "Export Report")],
    width: 800
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU, {
    defaultActiveKey: "metrics"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: "Detailed Metrics",
    key: "metrics"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Table */ .XI, {
    dataSource: webVitalsData,
    columns: webVitalsColumns,
    pagination: false,
    size: "small"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: "Component Analysis",
    key: "components"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .List */ .B8, {
    dataSource: components,
    renderItem: function renderItem(component) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .List */ .B8.Item.Meta, {
        title: component.name || component.type,
        description: "Render time: ".concat(Math.random() * 20 + 5, "ms")
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
        color: "blue"
      }, "Active"));
    }
  })))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceTools);

/***/ }),

/***/ 27954:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(70572);






var _templateObject, _templateObject2, _templateObject3, _templateObject4;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Data Management Tools Component
 * 
 * Provides data binding, state management, and data flow visualization tools
 * for components with real-time updates and visual state tracking.
 */





var Title = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6.Option;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_8__/* .Input */ .pd.TextArea;

// Styled Components
var DataContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  padding: 16px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"])));
var DataFlowVisualization = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: #fafafa;\n  border: 1px dashed #d9d9d9;\n  border-radius: 8px;\n  padding: 24px;\n  margin: 16px 0;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n"])));
var StateNode = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  background: ", ";\n  border: 2px solid ", ";\n  border-radius: 8px;\n  padding: 12px;\n  margin: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    border-color: #1890ff;\n    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);\n  }\n"])), function (props) {
  return props.active ? '#e6f7ff' : '#fff';
}, function (props) {
  return props.active ? '#1890ff' : '#d9d9d9';
});
var DataMetrics = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 24px;\n"])));

// Mock data for demonstration
var generateMockStateData = function generateMockStateData() {
  return {
    global: {
      user: {
        id: 1,
        name: 'John Doe',
        role: 'admin'
      },
      theme: {
        mode: 'light',
        primaryColor: '#1890ff'
      },
      settings: {
        language: 'en',
        notifications: true
      }
    },
    components: {
      'component-1': {
        visible: true,
        loading: false,
        data: {
          title: 'Header',
          items: 5
        }
      },
      'component-2': {
        visible: true,
        loading: true,
        data: {
          content: 'Loading...'
        }
      },
      'component-3': {
        visible: false,
        loading: false,
        data: {
          list: ['item1', 'item2']
        }
      }
    },
    api: {
      '/api/users': {
        status: 'success',
        lastFetch: Date.now(),
        cache: true
      },
      '/api/projects': {
        status: 'loading',
        lastFetch: null,
        cache: false
      },
      '/api/settings': {
        status: 'error',
        lastFetch: Date.now() - 30000,
        cache: false
      }
    }
  };
};
var generateMockDataBindings = function generateMockDataBindings() {
  return [{
    id: 'binding-1',
    source: 'global.user.name',
    target: 'component-1.props.title',
    type: 'direct',
    active: true,
    lastUpdate: Date.now()
  }, {
    id: 'binding-2',
    source: 'api./api/users',
    target: 'component-2.data.users',
    type: 'async',
    active: true,
    lastUpdate: Date.now() - 5000
  }, {
    id: 'binding-3',
    source: 'components.component-1.data.items',
    target: 'components.component-3.props.count',
    type: 'computed',
    active: false,
    lastUpdate: Date.now() - 10000
  }];
};

/**
 * DataManagementTools Component
 */
var DataManagementTools = function DataManagementTools(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onDataChange = _ref.onDataChange,
    onBindingCreate = _ref.onBindingCreate,
    onBindingUpdate = _ref.onBindingUpdate,
    onBindingDelete = _ref.onBindingDelete,
    _ref$realTimeUpdates = _ref.realTimeUpdates,
    realTimeUpdates = _ref$realTimeUpdates === void 0 ? true : _ref$realTimeUpdates,
    _ref$showVisualizatio = _ref.showVisualization,
    showVisualization = _ref$showVisualizatio === void 0 ? true : _ref$showVisualizatio,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(generateMockStateData()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState3, 2),
    stateData = _useState4[0],
    setStateData = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(generateMockDataBindings()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState5, 2),
    dataBindings = _useState6[0],
    setDataBindings = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState7, 2),
    selectedNode = _useState8[0],
    setSelectedNode = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState9, 2),
    showBindingModal = _useState0[0],
    setShowBindingModal = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState1, 2),
    editingBinding = _useState10[0],
    setEditingBinding = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_useState11, 2),
    monitoringEnabled = _useState12[0],
    setMonitoringEnabled = _useState12[1];
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_8__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];

  // Computed metrics
  var dataMetrics = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {
    var totalBindings = dataBindings.length;
    var activeBindings = dataBindings.filter(function (b) {
      return b.active;
    }).length;
    var totalComponents = Object.keys(stateData.components).length;
    var loadingComponents = Object.values(stateData.components).filter(function (c) {
      return c.loading;
    }).length;
    return {
      totalBindings: totalBindings,
      activeBindings: activeBindings,
      totalComponents: totalComponents,
      loadingComponents: loadingComponents,
      bindingHealth: totalBindings > 0 ? Math.round(activeBindings / totalBindings * 100) : 100
    };
  }, [dataBindings, stateData]);

  // Generate tree data for state visualization
  var stateTreeData = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {
    var _createTreeNode = function createTreeNode(key, value) {
      var path = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';
      var fullPath = path ? "".concat(path, ".").concat(key) : key;
      if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(value) === 'object' && value !== null && !Array.isArray(value)) {
        return {
          title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
            strong: true
          }, key), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
            size: "small"
          }, "object")),
          key: fullPath,
          children: Object.entries(value).map(function (_ref2) {
            var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref2, 2),
              k = _ref3[0],
              v = _ref3[1];
            return _createTreeNode(k, v, fullPath);
          })
        };
      }
      return {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, null, key), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
          type: "secondary"
        }, ": ", JSON.stringify(value)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
          size: "small",
          color: typeof value === 'string' ? 'blue' : 'green'
        }, (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(value))),
        key: fullPath,
        isLeaf: true
      };
    };
    return Object.entries(stateData).map(function (_ref4) {
      var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref4, 2),
        key = _ref5[0],
        value = _ref5[1];
      return _createTreeNode(key, value);
    });
  }, [stateData]);

  // Data binding table columns
  var bindingColumns = [{
    title: 'Source',
    dataIndex: 'source',
    key: 'source',
    render: function render(text) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
        code: true
      }, text);
    }
  }, {
    title: 'Target',
    dataIndex: 'target',
    key: 'target',
    render: function render(text) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
        code: true
      }, text);
    }
  }, {
    title: 'Type',
    dataIndex: 'type',
    key: 'type',
    render: function render(type) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
        color: type === 'direct' ? 'blue' : type === 'async' ? 'orange' : 'purple'
      }, type.toUpperCase());
    }
  }, {
    title: 'Status',
    dataIndex: 'active',
    key: 'active',
    render: function render(active) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Badge */ .Ex, {
        status: active ? 'success' : 'default',
        text: active ? 'Active' : 'Inactive'
      });
    }
  }, {
    title: 'Last Update',
    dataIndex: 'lastUpdate',
    key: 'lastUpdate',
    render: function render(timestamp) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
        type: "secondary"
      }, new Date(timestamp).toLocaleTimeString());
    }
  }, {
    title: 'Actions',
    key: 'actions',
    render: function render(_, record) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
        size: "small",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EditOutlined */ .xjh, null),
        onClick: function onClick() {
          return handleEditBinding(record);
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
        size: "small",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DeleteOutlined */ .SUY, null),
        danger: true,
        onClick: function onClick() {
          return handleDeleteBinding(record.id);
        }
      }));
    }
  }];

  // Handle binding operations
  var handleCreateBinding = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function () {
    setEditingBinding(null);
    form.resetFields();
    setShowBindingModal(true);
  }, [form]);
  var handleEditBinding = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (binding) {
    setEditingBinding(binding);
    form.setFieldsValue(binding);
    setShowBindingModal(true);
  }, [form]);
  var handleDeleteBinding = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (bindingId) {
    setDataBindings(function (prev) {
      return prev.filter(function (b) {
        return b.id !== bindingId;
      });
    });
    if (onBindingDelete) {
      onBindingDelete(bindingId);
    }
    antd__WEBPACK_IMPORTED_MODULE_8__/* .notification */ .Ew.success({
      message: 'Binding Deleted',
      description: 'Data binding has been removed successfully.',
      duration: 3
    });
  }, [onBindingDelete]);
  var handleSaveBinding = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee() {
    var values, binding, _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 1;
          return form.validateFields();
        case 1:
          values = _context.sent;
          binding = _objectSpread(_objectSpread({}, values), {}, {
            id: (editingBinding === null || editingBinding === void 0 ? void 0 : editingBinding.id) || "binding-".concat(Date.now()),
            lastUpdate: Date.now(),
            active: true
          });
          if (editingBinding) {
            setDataBindings(function (prev) {
              return prev.map(function (b) {
                return b.id === editingBinding.id ? binding : b;
              });
            });
            if (onBindingUpdate) {
              onBindingUpdate(binding);
            }
          } else {
            setDataBindings(function (prev) {
              return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [binding]);
            });
            if (onBindingCreate) {
              onBindingCreate(binding);
            }
          }
          setShowBindingModal(false);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .notification */ .Ew.success({
            message: editingBinding ? 'Binding Updated' : 'Binding Created',
            description: 'Data binding has been saved successfully.',
            duration: 3
          });
          _context.next = 3;
          break;
        case 2:
          _context.prev = 2;
          _t = _context["catch"](0);
          console.error('Form validation failed:', _t);
        case 3:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 2]]);
  })), [form, editingBinding, onBindingCreate, onBindingUpdate]);

  // Simulate real-time updates
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    if (!realTimeUpdates || !monitoringEnabled) return;
    var interval = setInterval(function () {
      // Simulate state changes
      setStateData(function (prev) {
        var _prev$components;
        return _objectSpread(_objectSpread({}, prev), {}, {
          components: _objectSpread(_objectSpread({}, prev.components), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, "component-".concat(Math.floor(Math.random() * 3) + 1), _objectSpread(_objectSpread({}, prev.components["component-".concat(Math.floor(Math.random() * 3) + 1)]), {}, {
            loading: Math.random() > 0.7,
            data: _objectSpread(_objectSpread({}, (_prev$components = prev.components["component-".concat(Math.floor(Math.random() * 3) + 1)]) === null || _prev$components === void 0 ? void 0 : _prev$components.data), {}, {
              lastUpdate: Date.now()
            })
          })))
        });
      });

      // Update binding timestamps
      setDataBindings(function (prev) {
        return prev.map(function (binding) {
          return _objectSpread(_objectSpread({}, binding), {}, {
            lastUpdate: binding.active ? Date.now() : binding.lastUpdate
          });
        });
      });
    }, 3000);
    return function () {
      return clearInterval(interval);
    };
  }, [realTimeUpdates, monitoringEnabled]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DataContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    justify: "space-between",
    align: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Title, {
    level: 4,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DatabaseOutlined */ .ose, {
    style: {
      marginRight: 8,
      color: '#1890ff'
    }
  }), "Data Management")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Switch */ .dO, {
    checked: monitoringEnabled,
    onChange: setMonitoringEnabled,
    checkedChildren: "Live",
    unCheckedChildren: "Static"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SettingOutlined */ .JO7, null),
    onClick: function onClick() {/* Settings modal */}
  }, "Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .PlusOutlined */ .bW0, null),
    onClick: handleCreateBinding
  }, "New Binding"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DataMetrics, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Total Bindings",
    value: dataMetrics.totalBindings,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .LinkOutlined */ .t7c, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Active Bindings",
    value: dataMetrics.activeBindings,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ThunderboltOutlined */ .CwG, null),
    valueStyle: {
      color: '#3f8600'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Components",
    value: dataMetrics.totalComponents,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .NodeIndexOutlined */ .lqK, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Statistic */ .jL, {
    title: "Binding Health",
    value: dataMetrics.bindingHealth,
    suffix: "%",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .MonitorOutlined */ .rwR, null),
    valueStyle: {
      color: dataMetrics.bindingHealth >= 80 ? '#3f8600' : '#cf1322'
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Overview",
    key: "overview"
  }, showVisualization && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Data Flow Visualization",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DataFlowVisualization, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      textAlign: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .BranchesOutlined */ .lhN, {
    style: {
      fontSize: 48,
      color: '#d9d9d9',
      marginBottom: 16
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Data flow visualization will be displayed here"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
    type: "secondary"
  }, "Showing connections between ", dataMetrics.totalBindings, " bindings"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Recent Activity",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      maxHeight: 200,
      overflowY: 'auto'
    }
  }, dataBindings.sort(function (a, b) {
    return b.lastUpdate - a.lastUpdate;
  }).slice(0, 5).map(function (binding) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      key: binding.id,
      style: {
        marginBottom: 8,
        padding: 8,
        background: '#fafafa',
        borderRadius: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      strong: true
    }, binding.source), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary"
    }, " \u2192 "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, null, binding.target), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 12
      }
    }, new Date(binding.lastUpdate).toLocaleString()));
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Component Status",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      maxHeight: 200,
      overflowY: 'auto'
    }
  }, Object.entries(stateData.components).map(function (_ref7) {
    var _ref8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref7, 2),
      id = _ref8[0],
      component = _ref8[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      key: id,
      style: {
        marginBottom: 8,
        padding: 8,
        background: '#fafafa',
        borderRadius: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      strong: true
    }, id), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Badge */ .Ex, {
      status: component.loading ? 'processing' : component.visible ? 'success' : 'default'
    }), component.loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
      color: "orange"
    }, "Loading"), !component.visible && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
      color: "default"
    }, "Hidden")));
  })))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "State Tree",
    key: "state"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Application State",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ReloadOutlined */ .KF4, null),
      onClick: function onClick() {
        return setStateData(generateMockStateData());
      }
    }, "Refresh")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tree */ .PH, {
    treeData: stateTreeData,
    onSelect: function onSelect(selectedKeys) {
      return setSelectedNode(selectedKeys[0]);
    },
    showLine: true,
    showIcon: false,
    defaultExpandAll: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "Data Bindings",
    key: "bindings"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Data Bindings",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      type: "primary",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .PlusOutlined */ .bW0, null),
      onClick: handleCreateBinding
    }, "Add Binding")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Table */ .XI, {
    dataSource: dataBindings,
    columns: bindingColumns,
    rowKey: "id",
    size: "small",
    pagination: {
      pageSize: 10
    }
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TabPane, {
    tab: "API Monitor",
    key: "api"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "API Endpoints"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, Object.entries(stateData.api).map(function (_ref9) {
    var _ref0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(_ref9, 2),
      endpoint = _ref0[0],
      status = _ref0[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
      key: endpoint,
      size: "small",
      style: {
        marginBottom: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Row */ .fI, {
      justify: "space-between",
      align: "middle"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      code: true
    }, endpoint), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Badge */ .Ex, {
      status: status.status === 'success' ? 'success' : status.status === 'loading' ? 'processing' : 'error',
      text: status.status.toUpperCase()
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, status.cache && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
      color: "blue"
    }, "Cached"), status.lastFetch && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: 12
      }
    }, new Date(status.lastFetch).toLocaleTimeString())))));
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: editingBinding ? 'Edit Data Binding' : 'Create Data Binding',
    open: showBindingModal,
    onCancel: function onCancel() {
      return setShowBindingModal(false);
    },
    onOk: handleSaveBinding,
    width: 600
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Form */ .lV, {
    form: form,
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Form */ .lV.Item, {
    name: "source",
    label: "Source Path",
    rules: [{
      required: true,
      message: 'Please enter source path'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Input */ .pd, {
    placeholder: "e.g., global.user.name"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Form */ .lV.Item, {
    name: "target",
    label: "Target Path",
    rules: [{
      required: true,
      message: 'Please enter target path'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Input */ .pd, {
    placeholder: "e.g., component-1.props.title"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Form */ .lV.Item, {
    name: "type",
    label: "Binding Type",
    rules: [{
      required: true,
      message: 'Please select binding type'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6, {
    placeholder: "Select binding type"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "direct"
  }, "Direct"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "async"
  }, "Async"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(Option, {
    value: "computed"
  }, "Computed"))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataManagementTools);

/***/ }),

/***/ 32150:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;




var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU.TabPane;
var ExamplesContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 24px;\n  max-width: 1200px;\n  margin: 0 auto;\n"])));
var ExampleCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 24px;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  \n  .ant-card-head {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    border-radius: 12px 12px 0 0;\n    \n    .ant-card-head-title {\n      color: white;\n    }\n  }\n"])));
var DemoArea = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 16px 0;\n  min-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n"])));
var CodeBlock = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.pre(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: #2d3748;\n  color: #e2e8f0;\n  padding: 16px;\n  border-radius: 8px;\n  overflow-x: auto;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  margin: 16px 0;\n"])));
var FeatureHighlight = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: linear-gradient(135deg, #667eea20 0%, #764ba220 100%);\n  border-left: 4px solid #667eea;\n  padding: 16px;\n  margin: 16px 0;\n  border-radius: 0 8px 8px 0;\n"])));
var AppBuilderExamples = function AppBuilderExamples() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('component-builder'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    activeExample = _useState2[0],
    setActiveExample = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    demoComponent = _useState4[0],
    setDemoComponent = _useState4[1];

  // Component Builder Example
  var ComponentBuilderExample = function ComponentBuilderExample() {
    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('button'),
      _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
      componentType = _useState6[0],
      setComponentType = _useState6[1];
    var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
        text: 'Click Me',
        variant: 'primary',
        size: 'medium'
      }),
      _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
      componentProps = _useState8[0],
      setComponentProps = _useState8[1];
    var createComponent = function createComponent() {
      var newComponent = {
        id: Date.now().toString(),
        type: componentType,
        props: componentProps,
        createdAt: new Date().toISOString()
      };
      setDemoComponent(newComponent);
    };
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ExampleCard, {
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .AppstoreOutlined */ .rS9, null), "Component Builder Example"),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        type: "primary",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .PlayCircleOutlined */ .VgC, null),
        onClick: createComponent
      }, "Create Component")
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FeatureHighlight, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BulbOutlined */ .o3f, {
      style: {
        color: '#667eea',
        marginRight: 8
      }
    }), "What you'll learn:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("ul", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "How to create components with custom properties"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Property validation and error handling"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Real-time preview of component changes"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Component type selection and configuration"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "The Component Builder allows you to create reusable UI components with custom properties. Try changing the component type and properties below, then click \"Create Component\" to see it in action."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Component Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      style: {
        marginLeft: 16
      }
    }, ['button', 'input', 'text', 'container'].map(function (type) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        key: type,
        type: componentType === type ? 'primary' : 'default',
        size: "small",
        onClick: function onClick() {
          return setComponentType(type);
        }
      }, type);
    }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Properties:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(CodeBlock, null, JSON.stringify(componentProps, null, 2)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DemoArea, null, demoComponent ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4
    }, "Created Component:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
      size: "small"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Type:"), " ", demoComponent.type, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "ID:"), " ", demoComponent.id, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Props:"), " ", JSON.stringify(demoComponent.props))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        textAlign: 'center',
        color: '#6c757d'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .AppstoreOutlined */ .rS9, {
      style: {
        fontSize: 48,
        marginBottom: 16
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, "Click \"Create Component\" to see your component here"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
      message: "Pro Tip",
      description: "Use the property editor to fine-tune your component's appearance and behavior. The reset button will restore original values if you make a mistake.",
      type: "info",
      showIcon: true
    }));
  };

  // Layout Designer Example
  var LayoutDesignerExample = function LayoutDesignerExample() {
    var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('grid'),
      _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
      layoutType = _useState0[0],
      setLayoutType = _useState0[1];
    var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
      _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
      layoutItems = _useState10[0],
      setLayoutItems = _useState10[1];
    var addLayoutItem = function addLayoutItem() {
      var newItem = {
        id: Date.now().toString(),
        name: "Item ".concat(layoutItems.length + 1),
        x: Math.floor(Math.random() * 8) + 1,
        y: Math.floor(Math.random() * 4) + 1,
        width: 2,
        height: 1
      };
      setLayoutItems([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(layoutItems), [newItem]));
    };
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ExampleCard, {
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LayoutOutlined */ .hy2, null), "Layout Designer Example"),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        type: "primary",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .PlayCircleOutlined */ .VgC, null),
        onClick: addLayoutItem
      }, "Add Layout Item")
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FeatureHighlight, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BulbOutlined */ .o3f, {
      style: {
        color: '#667eea',
        marginRight: 8
      }
    }), "What you'll learn:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("ul", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "How to create responsive grid layouts"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Drag and drop component positioning"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Layout tools for alignment and distribution"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Responsive breakpoint management"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "The Layout Designer helps you create responsive layouts using a visual grid system. Components can be dragged and positioned, and layouts adapt to different screen sizes."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Layout Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      style: {
        marginLeft: 16
      }
    }, ['grid', 'flex', 'stack', 'masonry'].map(function (type) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        key: type,
        type: layoutType === type ? 'primary' : 'default',
        size: "small",
        onClick: function onClick() {
          return setLayoutType(type);
        }
      }, type);
    })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DemoArea, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4
    }, "Layout Preview (", layoutType, ")"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'grid',
        gridTemplateColumns: 'repeat(8, 1fr)',
        gap: '8px',
        background: 'white',
        padding: '16px',
        borderRadius: '8px',
        minHeight: '120px'
      }
    }, layoutItems.map(function (item) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        key: item.id,
        style: {
          gridColumn: "".concat(item.x, " / span ").concat(item.width),
          gridRow: "".concat(item.y, " / span ").concat(item.height),
          background: '#667eea',
          color: 'white',
          padding: '8px',
          borderRadius: '4px',
          fontSize: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }
      }, item.name);
    })), layoutItems.length === 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        textAlign: 'center',
        color: '#6c757d',
        padding: '40px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LayoutOutlined */ .hy2, {
      style: {
        fontSize: 48,
        marginBottom: 16
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, null, "Click \"Add Layout Item\" to start building your layout")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
      message: "Layout Tools",
      description: "Use the alignment tools to distribute items evenly, group related components, and manage z-index for layering effects.",
      type: "info",
      showIcon: true
    }));
  };

  // Theme Manager Example
  var ThemeManagerExample = function ThemeManagerExample() {
    var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({
        name: 'Custom Theme',
        primaryColor: '#667eea',
        secondaryColor: '#764ba2',
        backgroundColor: '#ffffff',
        textColor: '#333333',
        fontFamily: 'Inter, sans-serif'
      }),
      _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
      currentTheme = _useState12[0],
      setCurrentTheme = _useState12[1];
    var applyTheme = function applyTheme(theme) {
      setCurrentTheme(theme);
    };
    var predefinedThemes = [{
      name: 'Ocean Blue',
      primaryColor: '#0066cc',
      secondaryColor: '#00aaff',
      backgroundColor: '#f0f8ff',
      textColor: '#003366',
      fontFamily: 'Arial, sans-serif'
    }, {
      name: 'Forest Green',
      primaryColor: '#228b22',
      secondaryColor: '#32cd32',
      backgroundColor: '#f0fff0',
      textColor: '#006400',
      fontFamily: 'Georgia, serif'
    }, {
      name: 'Sunset Orange',
      primaryColor: '#ff6347',
      secondaryColor: '#ffa500',
      backgroundColor: '#fff8dc',
      textColor: '#8b4513',
      fontFamily: 'Verdana, sans-serif'
    }];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ExampleCard, {
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager Example")
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FeatureHighlight, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 4
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BulbOutlined */ .o3f, {
      style: {
        color: '#667eea',
        marginRight: 8
      }
    }), "What you'll learn:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("ul", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "How to create and customize themes"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Real-time theme preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Color palette management"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("li", null, "Theme import/export functionality"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "The Theme Manager allows you to create consistent visual themes across your application. Try applying different themes below to see how they affect the preview."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
      strong: true
    }, "Predefined Themes:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        marginTop: 8,
        display: 'flex',
        gap: 8,
        flexWrap: 'wrap'
      }
    }, predefinedThemes.map(function (theme, index) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
        key: index,
        onClick: function onClick() {
          return applyTheme(theme);
        },
        style: {
          background: theme.primaryColor,
          borderColor: theme.primaryColor,
          color: 'white'
        }
      }, theme.name);
    })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DemoArea, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        width: '100%',
        background: currentTheme.backgroundColor,
        color: currentTheme.textColor,
        fontFamily: currentTheme.fontFamily,
        padding: '20px',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
      level: 3,
      style: {
        color: currentTheme.textColor,
        margin: '0 0 16px 0'
      }
    }, currentTheme.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, {
      style: {
        color: currentTheme.textColor
      }
    }, "This is a preview of how your theme will look. The colors, typography, and spacing are all applied according to your theme settings."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      style: {
        background: currentTheme.primaryColor,
        borderColor: currentTheme.primaryColor,
        color: 'white'
      }
    }, "Primary Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      style: {
        background: currentTheme.secondaryColor,
        borderColor: currentTheme.secondaryColor,
        color: 'white'
      }
    }, "Secondary Button")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        marginTop: 16,
        display: 'flex',
        gap: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        width: 20,
        height: 20,
        borderRadius: '50%',
        background: currentTheme.primaryColor,
        border: '1px solid #ccc'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        width: 20,
        height: 20,
        borderRadius: '50%',
        background: currentTheme.secondaryColor,
        border: '1px solid #ccc'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        width: 20,
        height: 20,
        borderRadius: '50%',
        background: currentTheme.backgroundColor,
        border: '1px solid #ccc'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        width: 20,
        height: 20,
        borderRadius: '50%',
        background: currentTheme.textColor,
        border: '1px solid #ccc'
      }
    })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
      message: "Theme Consistency",
      description: "Themes ensure visual consistency across your entire application. Export themes to share with team members or import community themes.",
      type: "info",
      showIcon: true
    }));
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ExamplesContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      textAlign: 'center',
      marginBottom: 32
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 1
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RocketOutlined */ .PKb, {
    style: {
      color: '#667eea',
      marginRight: 16
    }
  }), "App Builder Examples"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, {
    style: {
      fontSize: 18,
      color: '#6c757d'
    }
  }, "Learn how to use each feature with practical, interactive examples")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU, {
    activeKey: activeExample,
    onChange: setActiveExample,
    size: "large"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
    key: "component-builder"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ComponentBuilderExample, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
    key: "layout-designer"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(LayoutDesignerExample, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
    key: "theme-manager"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ThemeManagerExample, null))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
    style: {
      textAlign: 'center',
      background: 'linear-gradient(135deg, #667eea20 0%, #764ba220 100%)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 3
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ToolOutlined */ .xuD, {
    style: {
      color: '#667eea',
      marginRight: 8
    }
  }), "Ready to Build?"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "Now that you've seen how each feature works, you're ready to start building your own application!"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    size: "large",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RocketOutlined */ .PKb, null)
  }, "Start Building"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "large",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CodeOutlined */ .C$o, null)
  }, "View Documentation"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppBuilderExamples);

/***/ }),

/***/ 32361:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ ResponsiveAppLayout)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(16918);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(80582);
/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(92382);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Responsive App Layout
 * 
 * Enhanced layout component with optimized panel sizing, collapsible sections,
 * improved workspace efficiency, mobile responsiveness, and consistent navigation patterns.
 */


// Optimized Ant Design imports for better tree-shaking






var Header = _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Layout */ .PE.Header,
  Sider = _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Layout */ .PE.Sider,
  Content = _utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Layout */ .PE.Content;

// Enhanced responsive styled components
var ResponsiveLayout = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Layout */ .PE)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  height: 100vh;\n  background: ", ";\n  overflow: hidden;\n  \n  /* Smooth transitions for layout changes */\n  transition: ", ";\n  \n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"]);
var AdaptiveHeader = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(Header)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border-bottom: 1px solid ", ";\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  box-shadow: ", ";\n  z-index: ", ";\n  height: ", ";\n  transition: ", ";\n  \n  /* Mobile optimizations */\n  ", " {\n    height: 48px;\n    padding: 0 ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.appHeader, function (props) {
  return props.compact ? '48px' : '64px';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]);
var FlexibleSider = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(Sider)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border-right: 1px solid ", ";\n  box-shadow: ", ";\n  z-index: ", ";\n  transition: ", ";\n  \n  /* Custom trigger styling */\n  .ant-layout-sider-trigger {\n    background: ", ";\n    color: ", ";\n    border: none;\n    height: 40px;\n    line-height: 40px;\n    transition: ", ";\n    \n    &:hover {\n      background: ", ";\n    }\n  }\n  \n  /* Responsive behavior */\n  ", " {\n    position: fixed !important;\n    height: 100vh;\n    z-index: ", ";\n  }\n  \n  /* Smooth width transitions */\n  &.ant-layout-sider-collapsed {\n    .sider-content {\n      opacity: 0;\n      pointer-events: none;\n    }\n  }\n  \n  .sider-content {\n    opacity: 1;\n    transition: opacity 0.2s ease;\n    height: 100%;\n    overflow: hidden;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.appSidebar, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxLg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.modal);
var AdaptiveContent = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(Content)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  overflow: hidden;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  \n  /* Responsive margins for mobile */\n  ", " {\n    margin-left: 0 !important;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxLg);
var PanelResizer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: transparent;\n  cursor: col-resize;\n  z-index: ", ";\n  transition: ", ";\n  \n  ", "\n  \n  ", "\n  \n  &:hover {\n    background: ", ";\n  }\n  \n  &:active {\n    background: ", ";\n  }\n  \n  /* Hide on mobile */\n  ", " {\n    display: none;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.docked, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], function (props) {
  return props.position === 'left' && "\n    right: -2px;\n  ";
}, function (props) {
  return props.position === 'right' && "\n    left: -2px;\n  ";
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxLg);
var WorkspaceContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  position: relative;\n"])));
var ToolbarContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: 0 ", ";\n  \n  ", " {\n    padding: 0 ", ";\n    gap: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var MobileDrawerOverlay = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: ", ";\n  opacity: ", ";\n  pointer-events: ", ";\n  transition: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.overlay, function (props) {
  return props.visible ? 1 : 0;
}, function (props) {
  return props.visible ? 'auto' : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"]);
var FloatingActionButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .FloatButton */ .ff.Group)(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  /* Custom positioning for mobile */\n  ", " {\n    .ant-float-btn-group {\n      bottom: ", " !important;\n      right: ", " !important;\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4]);
var BreakpointIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: ", ";\n  left: 50%;\n  transform: translateX(-50%);\n  background: ", ";\n  color: ", ";\n  padding: ", " ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  z-index: ", ";\n  opacity: ", ";\n  transition: ", ";\n  pointer-events: none;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.tooltip, function (props) {
  return props.visible ? 1 : 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"]);

// Default panel sizes for different breakpoints
var PANEL_SIZES = {
  desktop: {
    left: 320,
    right: 320,
    minLeft: 240,
    maxLeft: 480,
    minRight: 240,
    maxRight: 480
  },
  tablet: {
    left: 280,
    right: 280,
    minLeft: 200,
    maxLeft: 400,
    minRight: 200,
    maxRight: 400
  },
  mobile: {
    left: '100vw',
    right: '100vw'
  }
};
function ResponsiveAppLayout(_ref) {
  var leftPanel = _ref.leftPanel,
    rightPanel = _ref.rightPanel,
    children = _ref.children,
    headerContent = _ref.headerContent,
    _ref$showBreakpointIn = _ref.showBreakpointIndicator,
    showBreakpointIndicator = _ref$showBreakpointIn === void 0 ? false : _ref$showBreakpointIn,
    _ref$enablePanelResiz = _ref.enablePanelResize,
    enablePanelResize = _ref$enablePanelResiz === void 0 ? true : _ref$enablePanelResiz,
    _ref$persistLayout = _ref.persistLayout,
    persistLayout = _ref$persistLayout === void 0 ? true : _ref$persistLayout,
    _ref$compactMode = _ref.compactMode,
    compactMode = _ref$compactMode === void 0 ? false : _ref$compactMode;
  // Responsive breakpoint detection
  var isMobile = (0,_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd);
  var isTablet = (0,_hooks_useMediaQuery__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxLg);
  var isDesktop = !isTablet;

  // Layout state with persistence
  var _useLocalStorage = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_9__/* .useLocalStorage */ .M)('app-builder-layout', {
      leftSiderCollapsed: false,
      rightSiderCollapsed: false,
      leftSiderWidth: PANEL_SIZES.desktop.left,
      rightSiderWidth: PANEL_SIZES.desktop.right,
      isFullscreen: false
    }),
    _useLocalStorage2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useLocalStorage, 2),
    layoutState = _useLocalStorage2[0],
    setLayoutState = _useLocalStorage2[1];

  // Mobile-specific state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    mobileLeftDrawerVisible = _useState2[0],
    setMobileLeftDrawerVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    mobileRightDrawerVisible = _useState4[0],
    setMobileRightDrawerVisible = _useState4[1];

  // Panel resizing state
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    isResizing = _useState6[0],
    setIsResizing = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    resizingPanel = _useState8[0],
    setResizingPanel = _useState8[1];

  // Get current breakpoint
  var currentBreakpoint = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    if (isMobile) return 'mobile';
    if (isTablet) return 'tablet';
    return 'desktop';
  }, [isMobile, isTablet]);

  // Get panel sizes for current breakpoint
  var panelSizes = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return PANEL_SIZES[currentBreakpoint];
  }, [currentBreakpoint]);

  // Update layout state
  var updateLayoutState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (updates) {
    setLayoutState(function (prev) {
      return _objectSpread(_objectSpread({}, prev), updates);
    });
  }, [setLayoutState]);

  // Toggle left panel
  var toggleLeftPanel = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (isMobile) {
      setMobileLeftDrawerVisible(function (prev) {
        return !prev;
      });
    } else {
      updateLayoutState({
        leftSiderCollapsed: !layoutState.leftSiderCollapsed
      });
    }
  }, [isMobile, layoutState.leftSiderCollapsed, updateLayoutState]);

  // Toggle right panel
  var toggleRightPanel = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (isMobile) {
      setMobileRightDrawerVisible(function (prev) {
        return !prev;
      });
    } else {
      updateLayoutState({
        rightSiderCollapsed: !layoutState.rightSiderCollapsed
      });
    }
  }, [isMobile, layoutState.rightSiderCollapsed, updateLayoutState]);

  // Toggle fullscreen
  var toggleFullscreen = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      updateLayoutState({
        isFullscreen: true
      });
    } else {
      document.exitFullscreen();
      updateLayoutState({
        isFullscreen: false
      });
    }
  }, [updateLayoutState]);

  // Handle panel resizing
  var handleResizeStart = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (panel, e) {
    if (!enablePanelResize || isMobile) return;
    setIsResizing(true);
    setResizingPanel(panel);
    var startX = e.clientX;
    var startWidth = panel === 'left' ? layoutState.leftSiderWidth : layoutState.rightSiderWidth;
    var handleMouseMove = function handleMouseMove(e) {
      var deltaX = panel === 'left' ? e.clientX - startX : startX - e.clientX;
      var newWidth = Math.max(panelSizes.minLeft || panelSizes.minRight, Math.min(panelSizes.maxLeft || panelSizes.maxRight, startWidth + deltaX));
      updateLayoutState((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, panel === 'left' ? 'leftSiderWidth' : 'rightSiderWidth', newWidth));
    };
    var _handleMouseUp = function handleMouseUp() {
      setIsResizing(false);
      setResizingPanel(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', _handleMouseUp);
    };
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', _handleMouseUp);
  }, [enablePanelResize, isMobile, layoutState, panelSizes, updateLayoutState]);

  // Handle fullscreen changes
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    var handleFullscreenChange = function handleFullscreenChange() {
      updateLayoutState({
        isFullscreen: !!document.fullscreenElement
      });
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return function () {
      return document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [updateLayoutState]);

  // Close mobile drawers when switching to desktop
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!isMobile) {
      setMobileLeftDrawerVisible(false);
      setMobileRightDrawerVisible(false);
    }
  }, [isMobile]);

  // Adjust panel sizes for different breakpoints
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (currentBreakpoint !== 'mobile') {
      var sizes = PANEL_SIZES[currentBreakpoint];
      updateLayoutState({
        leftSiderWidth: Math.min(layoutState.leftSiderWidth, sizes.maxLeft),
        rightSiderWidth: Math.min(layoutState.rightSiderWidth, sizes.maxRight)
      });
    }
  }, [currentBreakpoint, layoutState.leftSiderWidth, layoutState.rightSiderWidth, updateLayoutState]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ResponsiveLayout, null, showBreakpointIndicator && "production" === 'development' && /*#__PURE__*/0, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(AdaptiveHeader, {
    compact: compactMode
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ToolbarContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: layoutState.leftSiderCollapsed || mobileLeftDrawerVisible ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MenuUnfoldOutlined */ .kxZ, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MenuFoldOutlined */ .lxx, null),
    onClick: toggleLeftPanel,
    size: compactMode ? 'small' : 'middle'
  }), headerContent), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ToolbarContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: "Current: ".concat(currentBreakpoint)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: currentBreakpoint === 'mobile' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MobileOutlined */ .jHj, null) : currentBreakpoint === 'tablet' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .TabletOutlined */ .pLH, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DesktopOutlined */ .zlw, null),
    size: compactMode ? 'small' : 'middle'
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: layoutState.isFullscreen ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenExitOutlined */ .kW6, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null),
    onClick: toggleFullscreen,
    size: compactMode ? 'small' : 'middle'
  }), !isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: layoutState.rightSiderCollapsed ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MenuUnfoldOutlined */ .kxZ, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MenuFoldOutlined */ .lxx, null),
    onClick: toggleRightPanel,
    size: compactMode ? 'small' : 'middle'
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Layout */ .PE, null, !isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FlexibleSider, {
    width: layoutState.leftSiderWidth,
    collapsed: layoutState.leftSiderCollapsed,
    collapsible: true,
    trigger: null,
    theme: "light"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "sider-content"
  }, leftPanel), enablePanelResize && !layoutState.leftSiderCollapsed && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PanelResizer, {
    position: "left",
    onMouseDown: function onMouseDown(e) {
      return handleResizeStart('left', e);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(AdaptiveContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(WorkspaceContainer, null, children)), !isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FlexibleSider, {
    width: layoutState.rightSiderWidth,
    collapsed: layoutState.rightSiderCollapsed,
    collapsible: true,
    trigger: null,
    theme: "light",
    reverseArrow: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "sider-content"
  }, rightPanel), enablePanelResize && !layoutState.rightSiderCollapsed && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PanelResizer, {
    position: "right",
    onMouseDown: function onMouseDown(e) {
      return handleResizeStart('right', e);
    }
  }))), isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: "Components",
    placement: "left",
    open: mobileLeftDrawerVisible,
    onClose: function onClose() {
      return setMobileLeftDrawerVisible(false);
    },
    width: "100vw",
    styles: {
      body: {
        padding: 0
      }
    }
  }, leftPanel), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: "Properties",
    placement: "right",
    open: mobileRightDrawerVisible,
    onClose: function onClose() {
      return setMobileRightDrawerVisible(false);
    },
    width: "100vw",
    styles: {
      body: {
        padding: 0
      }
    }
  }, rightPanel)), isMobile && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(FloatingActionButton, {
    trigger: "click",
    type: "primary",
    style: {
      right: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4],
      bottom: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4]
    },
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MenuFoldOutlined */ .lxx, null),
    tooltip: "Components",
    onClick: function onClick() {
      return setMobileLeftDrawerVisible(true);
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
    tooltip: "Properties",
    onClick: function onClick() {
      return setMobileRightDrawerVisible(true);
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_utils_optimizedAntdImports__WEBPACK_IMPORTED_MODULE_4__/* .FloatButton */ .ff, {
    icon: layoutState.isFullscreen ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenExitOutlined */ .kW6, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null),
    tooltip: "Fullscreen",
    onClick: toggleFullscreen
  })), isResizing && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      cursor: 'col-resize',
      zIndex: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.modal,
      pointerEvents: 'none'
    }
  }));
}

/***/ }),

/***/ 38787:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AIComponents: () => (/* binding */ AIComponents),
/* harmony export */   AIDesignSuggestions: () => (/* binding */ AIDesignSuggestions),
/* harmony export */   AILayoutSuggestions: () => (/* binding */ AILayoutSuggestions),
/* harmony export */   AISuggestionsPanel: () => (/* binding */ AISuggestionsPanel),
/* harmony export */   AnalyticsPanel: () => (/* binding */ AnalyticsPanel),
/* harmony export */   AnimationEditor: () => (/* binding */ AnimationEditor),
/* harmony export */   CodeExporter: () => (/* binding */ CodeExporter),
/* harmony export */   CollaborationComponents: () => (/* binding */ CollaborationComponents),
/* harmony export */   CollaborationIndicator: () => (/* binding */ CollaborationIndicator),
/* harmony export */   CollaborationPanel: () => (/* binding */ CollaborationPanel),
/* harmony export */   ColorPicker: () => (/* binding */ ColorPicker),
/* harmony export */   ComponentBuilder: () => (/* binding */ ComponentBuilder),
/* harmony export */   ExportComponents: () => (/* binding */ ExportComponents),
/* harmony export */   ExportPreview: () => (/* binding */ ExportPreview),
/* harmony export */   ExportSettings: () => (/* binding */ ExportSettings),
/* harmony export */   FeatureComponents: () => (/* binding */ FeatureComponents),
/* harmony export */   LiveCursors: () => (/* binding */ LiveCursors),
/* harmony export */   LoadingPriority: () => (/* binding */ LoadingPriority),
/* harmony export */   MultiDevicePreview: () => (/* binding */ MultiDevicePreview),
/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),
/* harmony export */   PreviewComponents: () => (/* binding */ PreviewComponents),
/* harmony export */   PropertyEditorComponents: () => (/* binding */ PropertyEditorComponents),
/* harmony export */   ResponsivePreview: () => (/* binding */ ResponsivePreview),
/* harmony export */   SpacingEditor: () => (/* binding */ SpacingEditor),
/* harmony export */   TemplateComponents: () => (/* binding */ TemplateComponents),
/* harmony export */   TemplateEditor: () => (/* binding */ TemplateEditor),
/* harmony export */   TemplateGallery: () => (/* binding */ TemplateGallery),
/* harmony export */   TemplateManager: () => (/* binding */ TemplateManager),
/* harmony export */   TutorialAssistant: () => (/* binding */ TutorialAssistant),
/* harmony export */   TutorialComponents: () => (/* binding */ TutorialComponents),
/* harmony export */   TutorialOverlay: () => (/* binding */ TutorialOverlay),
/* harmony export */   TutorialProgress: () => (/* binding */ TutorialProgress),
/* harmony export */   TypographyEditor: () => (/* binding */ TypographyEditor)
/* harmony export */ });
/* harmony import */ var _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(56702);


/**
 * Centralized Lazy Component Configuration
 * Manages all lazy-loaded components with appropriate loading states and error handling
 */

// Tutorial System Components
var TutorialAssistant = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 90720));
}, {
  componentName: 'TutorialAssistant',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.withDescription('Loading tutorial system...'),
  retryAttempts: 3,
  preload: false // Load on demand only
});
var TutorialOverlay = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 1163));
}, {
  componentName: 'TutorialOverlay',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2
});
var TutorialProgress = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 1549));
}, {
  componentName: 'TutorialProgress',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2
});

// AI Suggestions Components
var AIDesignSuggestions = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(1538)]).then(__webpack_require__.bind(__webpack_require__, 44459));
}, {
  componentName: 'AIDesignSuggestions',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.withDescription('Loading AI suggestions engine...'),
  retryAttempts: 3,
  preload: false
});
var AISuggestionsPanel = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(9157)]).then(__webpack_require__.bind(__webpack_require__, 83719));
}, {
  componentName: 'AISuggestionsPanel',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2
});
var AILayoutSuggestions = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 26379));
}, {
  componentName: 'AILayoutSuggestions',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2
});

// Template Management Components
var TemplateManager = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 17438));
}, {
  componentName: 'TemplateManager',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.withDescription('Loading template management...'),
  retryAttempts: 3,
  preload: false
});
var TemplateGallery = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 19871));
}, {
  componentName: 'TemplateGallery',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2
});
var TemplateEditor = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 77844));
}, {
  componentName: 'TemplateEditor',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2
});

// Code Export Components
var CodeExporter = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(5651), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538)]).then(__webpack_require__.bind(__webpack_require__, 48796));
}, {
  componentName: 'CodeExporter',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.withDescription('Loading code export functionality...'),
  retryAttempts: 3,
  preload: false
});
var ExportPreview = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(5651), __webpack_require__.e(8617), __webpack_require__.e(4200), __webpack_require__.e(1557), __webpack_require__.e(875), __webpack_require__.e(5245), __webpack_require__.e(177), __webpack_require__.e(567), __webpack_require__.e(8554), __webpack_require__.e(9508), __webpack_require__.e(4723), __webpack_require__.e(5249), __webpack_require__.e(5614), __webpack_require__.e(3914), __webpack_require__.e(8544), __webpack_require__.e(7139), __webpack_require__.e(2769), __webpack_require__.e(5365), __webpack_require__.e(6427), __webpack_require__.e(7467), __webpack_require__.e(1056), __webpack_require__.e(351), __webpack_require__.e(2258), __webpack_require__.e(664), __webpack_require__.e(3323), __webpack_require__.e(8700), __webpack_require__.e(4855), __webpack_require__.e(3364), __webpack_require__.e(497), __webpack_require__.e(7844), __webpack_require__.e(1179), __webpack_require__.e(7914), __webpack_require__.e(473), __webpack_require__.e(9170), __webpack_require__.e(6676), __webpack_require__.e(645), __webpack_require__.e(503), __webpack_require__.e(8854), __webpack_require__.e(7659), __webpack_require__.e(4742), __webpack_require__.e(4750), __webpack_require__.e(4227), __webpack_require__.e(1489), __webpack_require__.e(8941), __webpack_require__.e(2538)]).then(__webpack_require__.bind(__webpack_require__, 10256));
}, {
  componentName: 'ExportPreview',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2
});
var ExportSettings = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 36243));
}, {
  componentName: 'ExportSettings',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2
});

// Collaboration Components
var CollaborationIndicator = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(5111)]).then(__webpack_require__.bind(__webpack_require__, 27379));
}, {
  componentName: 'CollaborationIndicator',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2,
  preload: true // Preload since it's likely to be used
});
var CollaborationPanel = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 80344));
}, {
  componentName: 'CollaborationPanel',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2
});
var LiveCursors = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774)]).then(__webpack_require__.bind(__webpack_require__, 76508));
}, {
  componentName: 'LiveCursors',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2
});

// Advanced Property Editors
var ColorPicker = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 6089).then(__webpack_require__.bind(__webpack_require__, 66072));
}, {
  componentName: 'ColorPicker',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2,
  preload: false
});
var SpacingEditor = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 6089).then(__webpack_require__.bind(__webpack_require__, 82353));
}, {
  componentName: 'SpacingEditor',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2,
  preload: false
});
var TypographyEditor = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 6089).then(__webpack_require__.bind(__webpack_require__, 84477));
}, {
  componentName: 'TypographyEditor',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2,
  preload: false
});
var AnimationEditor = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 6089).then(__webpack_require__.bind(__webpack_require__, 76990));
}, {
  componentName: 'AnimationEditor',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2,
  preload: false
});

// Preview Components
var MultiDevicePreview = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 1939).then(__webpack_require__.bind(__webpack_require__, 41939));
}, {
  componentName: 'MultiDevicePreview',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.withDescription('Loading device preview...'),
  retryAttempts: 2,
  preload: false
});
var ResponsivePreview = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 4354).then(__webpack_require__.bind(__webpack_require__, 34354));
}, {
  componentName: 'ResponsivePreview',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2,
  preload: false
});

// Performance and Analytics
var PerformanceMonitor = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(7749), __webpack_require__.e(9975)]).then(__webpack_require__.bind(__webpack_require__, 2356));
}, {
  componentName: 'PerformanceMonitor',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.minimal,
  retryAttempts: 2,
  preload: false
});
var AnalyticsPanel = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return __webpack_require__.e(/* import() */ 8256).then(__webpack_require__.bind(__webpack_require__, 18256));
}, {
  componentName: 'AnalyticsPanel',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.standard,
  retryAttempts: 2,
  preload: false
});

// Enhanced Components (if they exist and are large)
var ComponentBuilder = (0,_utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .createLazyComponent */ .s_)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(8267), __webpack_require__.e(7208), __webpack_require__.e(9787), __webpack_require__.e(9774), __webpack_require__.e(3815), __webpack_require__.e(5447), __webpack_require__.e(8293), __webpack_require__.e(6619), __webpack_require__.e(8292), __webpack_require__.e(8294), __webpack_require__.e(4922), __webpack_require__.e(6474), __webpack_require__.e(7101)]).then(__webpack_require__.bind(__webpack_require__, 16030))["catch"](function () {
    // Fallback if component doesn't exist
    return {
      "default": function _default() {
        return /*#__PURE__*/React.createElement("div", null, "Component Builder not available");
      }
    };
  });
}, {
  componentName: 'ComponentBuilder',
  fallback: _utils_lazyLoading__WEBPACK_IMPORTED_MODULE_0__/* .LoadingStates */ .PF.withDescription('Loading component builder...'),
  retryAttempts: 2,
  preload: false
});

// Create fallback components for missing ones

// Grouping for progressive loading
var TutorialComponents = [TutorialAssistant, TutorialOverlay, TutorialProgress];
var AIComponents = [AIDesignSuggestions, AISuggestionsPanel, AILayoutSuggestions];
var TemplateComponents = [TemplateManager, TemplateGallery, TemplateEditor];
var ExportComponents = [CodeExporter, ExportPreview, ExportSettings];
var CollaborationComponents = [CollaborationIndicator, CollaborationPanel, LiveCursors];
var PropertyEditorComponents = [ColorPicker, SpacingEditor, TypographyEditor, AnimationEditor];
var PreviewComponents = [MultiDevicePreview, ResponsivePreview];

// Feature-based component groups for conditional loading
var FeatureComponents = {
  tutorial: TutorialComponents,
  aiSuggestions: AIComponents,
  templates: TemplateComponents,
  codeExport: ExportComponents,
  collaboration: CollaborationComponents,
  advancedProperties: PropertyEditorComponents,
  preview: PreviewComponents
};

// Priority loading order (high priority components load first)
var LoadingPriority = {
  high: [CollaborationIndicator],
  // Always visible
  medium: [TutorialAssistant, AIDesignSuggestions],
  // Feature entry points
  low: [TemplateManager, CodeExporter],
  // On-demand features
  veryLow: PropertyEditorComponents.concat(PreviewComponents) // Specific use cases
};

/***/ }),

/***/ 51311:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DragDropProvider: () => (/* binding */ DragDropProvider),
/* harmony export */   EnhancedDraggable: () => (/* binding */ EnhancedDraggable),
/* harmony export */   EnhancedDropZone: () => (/* binding */ EnhancedDropZone),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(40961);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(70572);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(35346);




var _excluded = ["children", "data", "disabled", "showIndicator", "ghostContent", "onDragStart", "onDragEnd", "className", "style", "ariaLabel"],
  _excluded2 = ["children", "onDrop", "onDragOver", "onDragEnter", "onDragLeave", "accepts", "minHeight", "disabled", "showMessage", "validMessage", "invalidMessage", "emptyMessage", "className", "style", "ariaLabel"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14;
/**
 * Enhanced Drag and Drop System
 * 
 * Comprehensive drag-and-drop system with improved visual feedback,
 * drop zone highlighting, component ghost previews, smooth animations,
 * and accessibility support.
 */







// Drag animations
var dragPulse = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .keyframes */ .i7)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  0% { transform: scale(1); }\n  50% { transform: scale(1.05); }\n  100% { transform: scale(1); }\n"])));
var dropZoneGlow = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .keyframes */ .i7)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  0% { box-shadow: 0 0 0 0 ", "40; }\n  50% { box-shadow: 0 0 0 8px ", "20; }\n  100% { box-shadow: 0 0 0 0 ", "40; }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);
var ghostFloat = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .keyframes */ .i7)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-2px); }\n  100% { transform: translateY(0px); }\n"])));

// Enhanced styled components for drag and drop
var DraggableWrapper = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['isDragging', 'isDraggable', 'disabled'].includes(prop);
  }
})(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: relative;\n  cursor: ", ";\n  transition: ", ";\n  user-select: none;\n\n  ", "\n\n  ", "\n\n  ", "\n  \n  /* Accessibility enhancements */\n  &:focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n  }\n  \n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 2px solid;\n  }\n\n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n    animation: none;\n  }\n"])), function (props) {
  return props.isDragging ? 'grabbing' : 'grab';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.transitions.fast, function (props) {
  return props.isDraggable && !props.disabled && (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .css */ .AH)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n    &:hover {\n      transform: translateY(-1px);\n      box-shadow: ", ";\n\n      .drag-indicator {\n        opacity: 1;\n        transform: scale(1);\n      }\n    }\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.md);
}, function (props) {
  return props.isDragging && (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .css */ .AH)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n    opacity: 0.8;\n    transform: rotate(2deg) scale(1.02);\n    box-shadow: ", ";\n    z-index: ", ";\n    animation: ", " 2s infinite;\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.dragOverlay, dragPulse);
}, function (props) {
  return props.disabled && (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .css */ .AH)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n    cursor: not-allowed;\n    opacity: 0.6;\n    filter: grayscale(50%);\n  "])));
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);
var DragIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  right: ", ";\n  background: ", ";\n  color: ", ";\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: ", ";\n  z-index: 2;\n  \n  &.drag-indicator {\n    opacity: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_7__.transitions.fast, function (props) {
  return props.visible ? 1 : 0;
});
var DropZone = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['minHeight', 'isActive', 'isValid', 'isInvalid'].includes(prop);
  }
})(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: relative;\n  min-height: ", ";\n  border: 2px dashed ", ";\n  border-radius: ", ";\n  background: ", ";\n  transition: ", ";\n\n  ", "\n\n  ", "\n  \n  ", "\n  \n  /* Accessibility */\n  &[role=\"region\"] {\n    outline: none;\n  }\n  \n  &:focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n  }\n"])), function (props) {
  return props.minHeight || '100px';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.transitions["default"], function (props) {
  return props.isActive && (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .css */ .AH)(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n    border-color: ", ";\n    background: ", ";\n    animation: ", " 2s infinite;\n\n    .drop-message {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.light, dropZoneGlow);
}, function (props) {
  return props.isValid && (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .css */ .AH)(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n    border-color: ", ";\n    background: ", ";\n\n    .drop-message {\n      color: ", ";\n    }\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.dark);
}, function (props) {
  return props.isInvalid && (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* .css */ .AH)(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n    border-color: ", ";\n    background: ", ";\n    \n    .drop-message {\n      color: ", ";\n    }\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.dark);
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);
var DropMessage = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject11 || (_templateObject11 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%) translateY(10px);\n  text-align: center;\n  color: ", ";\n  font-weight: ", ";\n  opacity: 0;\n  transition: ", ";\n  pointer-events: none;\n  \n  &.drop-message {\n    opacity: ", ";\n    transform: translate(-50%, -50%) translateY(", ");\n  }\n  \n  .drop-icon {\n    font-size: 24px;\n    margin-bottom: ", ";\n    display: block;\n  }\n  \n  .drop-text {\n    font-size: ", ";\n    line-height: ", ";\n  }\n  \n  .drop-hint {\n    font-size: ", ";\n    color: ", ";\n    margin-top: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.transitions["default"], function (props) {
  return props.visible ? 1 : 0;
}, function (props) {
  return props.visible ? '0' : '10px';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.lineHeight.normal, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var DragGhost = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject12 || (_templateObject12 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  pointer-events: none;\n  z-index: ", ";\n  background: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  padding: ", " ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  white-space: nowrap;\n  animation: ", " 2s infinite;\n  \n  /* Smooth follow cursor */\n  transition: transform 0.1s ease-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    background: linear-gradient(45deg, ", ", ", ");\n    border-radius: ", ";\n    z-index: -1;\n    opacity: 0.3;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.tooltip, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.primary, ghostFloat, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.accent.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md);
var DropPreview = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject13 || (_templateObject13 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  background: ", ";\n  border: 2px dashed ", ";\n  border-radius: ", ";\n  opacity: 0.7;\n  pointer-events: none;\n  z-index: ", ";\n  transition: ", ";\n  \n  &::after {\n    content: 'Drop here';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    font-size: ", ";\n    font-weight: ", ";\n    color: ", ";\n    white-space: nowrap;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.docked, _design_system__WEBPACK_IMPORTED_MODULE_7__.transitions.fast, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark);
var DragOverlay = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject14 || (_templateObject14 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.1);\n  z-index: ", ";\n  pointer-events: none;\n  opacity: ", ";\n  transition: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.overlay, function (props) {
  return props.visible ? 1 : 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.transitions["default"]);

// Enhanced Draggable Component
var EnhancedDraggable = function EnhancedDraggable(_ref) {
  var children = _ref.children,
    data = _ref.data,
    _ref$disabled = _ref.disabled,
    disabled = _ref$disabled === void 0 ? false : _ref$disabled,
    _ref$showIndicator = _ref.showIndicator,
    showIndicator = _ref$showIndicator === void 0 ? true : _ref$showIndicator,
    ghostContent = _ref.ghostContent,
    onDragStart = _ref.onDragStart,
    onDragEnd = _ref.onDragEnd,
    className = _ref.className,
    style = _ref.style,
    ariaLabel = _ref.ariaLabel,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref, _excluded);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isDragging = _useState2[0],
    setIsDragging = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      x: 0,
      y: 0
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    ghostPosition = _useState4[0],
    setGhostPosition = _useState4[1];
  var dragRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var ghostRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    if (disabled) {
      e.preventDefault();
      return;
    }
    setIsDragging(true);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify(data));
    e.dataTransfer.effectAllowed = 'move';

    // Create custom drag image
    if (dragRef.current) {
      var dragImage = dragRef.current.cloneNode(true);
      dragImage.style.transform = 'rotate(2deg)';
      dragImage.style.opacity = '0.8';
      e.dataTransfer.setDragImage(dragImage, 50, 25);
    }

    // Announce drag start for screen readers
    if (ariaLabel) {
      var announcement = "Started dragging ".concat(ariaLabel);
      // Create live region announcement
      var liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'assertive');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      document.body.appendChild(liveRegion);
      setTimeout(function () {
        return document.body.removeChild(liveRegion);
      }, 1000);
    }
    if (onDragStart) {
      onDragStart(e, data);
    }
  }, [disabled, data, ariaLabel, onDragStart]);
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    setIsDragging(false);
    setGhostPosition({
      x: 0,
      y: 0
    });

    // Announce drag end for screen readers
    if (ariaLabel) {
      var announcement = "Finished dragging ".concat(ariaLabel);
      var liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      document.body.appendChild(liveRegion);
      setTimeout(function () {
        return document.body.removeChild(liveRegion);
      }, 1000);
    }
    if (onDragEnd) {
      onDragEnd(e, data);
    }
  }, [ariaLabel, onDragEnd, data]);

  // Track mouse position for ghost
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (!isDragging) return;
    var handleMouseMove = function handleMouseMove(e) {
      setGhostPosition({
        x: e.clientX + 10,
        y: e.clientY + 10
      });
    };
    document.addEventListener('mousemove', handleMouseMove);
    return function () {
      return document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isDragging]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DraggableWrapper, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
    ref: dragRef,
    draggable: !disabled,
    isDraggable: !disabled,
    isDragging: isDragging,
    disabled: disabled,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
    className: className,
    style: style,
    role: "button",
    "aria-label": ariaLabel || 'Draggable item',
    "aria-grabbed": isDragging,
    tabIndex: disabled ? -1 : 0
  }, props), children, showIndicator && !disabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DragIndicator, {
    className: "drag-indicator",
    visible: !isDragging
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DragOutlined */ .duJ, null))), isDragging && ghostContent && /*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_5__.createPortal)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DragGhost, {
    ref: ghostRef,
    style: {
      left: ghostPosition.x,
      top: ghostPosition.y
    }
  }, ghostContent), document.body));
};

// Enhanced Drop Zone Component
var EnhancedDropZone = function EnhancedDropZone(_ref2) {
  var children = _ref2.children,
    onDrop = _ref2.onDrop,
    onDragOver = _ref2.onDragOver,
    onDragEnter = _ref2.onDragEnter,
    onDragLeave = _ref2.onDragLeave,
    _ref2$accepts = _ref2.accepts,
    accepts = _ref2$accepts === void 0 ? [] : _ref2$accepts,
    minHeight = _ref2.minHeight,
    _ref2$disabled = _ref2.disabled,
    disabled = _ref2$disabled === void 0 ? false : _ref2$disabled,
    _ref2$showMessage = _ref2.showMessage,
    showMessage = _ref2$showMessage === void 0 ? true : _ref2$showMessage,
    _ref2$validMessage = _ref2.validMessage,
    validMessage = _ref2$validMessage === void 0 ? "Drop here" : _ref2$validMessage,
    _ref2$invalidMessage = _ref2.invalidMessage,
    invalidMessage = _ref2$invalidMessage === void 0 ? "Cannot drop here" : _ref2$invalidMessage,
    _ref2$emptyMessage = _ref2.emptyMessage,
    emptyMessage = _ref2$emptyMessage === void 0 ? "Drag items here" : _ref2$emptyMessage,
    className = _ref2.className,
    style = _ref2.style,
    ariaLabel = _ref2.ariaLabel,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref2, _excluded2);
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      isActive: false,
      isValid: false,
      draggedData: null
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    dragState = _useState6[0],
    setDragState = _useState6[1];
  var dropRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var dragCounter = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(0);
  var validateDrop = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (draggedData) {
    if (accepts.length === 0) return true;
    return accepts.some(function (accept) {
      if (typeof accept === 'string') {
        return (draggedData === null || draggedData === void 0 ? void 0 : draggedData.type) === accept;
      }
      if (typeof accept === 'function') {
        return accept(draggedData);
      }
      return false;
    });
  }, [accepts]);
  var handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    e.preventDefault();
    dragCounter.current++;
    if (disabled) return;
    try {
      var draggedData = JSON.parse(e.dataTransfer.getData('application/json') || '{}');
      var isValid = validateDrop(draggedData);
      setDragState({
        isActive: true,
        isValid: isValid,
        draggedData: draggedData
      });
      if (onDragEnter) {
        onDragEnter(e, {
          isValid: isValid,
          draggedData: draggedData
        });
      }
    } catch (error) {
      console.warn('Invalid drag data:', error);
    }
  }, [disabled, validateDrop, onDragEnter]);
  var handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = dragState.isValid ? 'move' : 'none';
    if (onDragOver) {
      onDragOver(e, dragState);
    }
  }, [dragState, onDragOver]);
  var handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    e.preventDefault();
    dragCounter.current--;
    if (dragCounter.current === 0) {
      setDragState({
        isActive: false,
        isValid: false,
        draggedData: null
      });
      if (onDragLeave) {
        onDragLeave(e);
      }
    }
  }, [onDragLeave]);
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    e.preventDefault();
    dragCounter.current = 0;
    if (disabled || !dragState.isValid) {
      setDragState({
        isActive: false,
        isValid: false,
        draggedData: null
      });
      return;
    }

    // Announce successful drop for screen readers
    if (ariaLabel) {
      var announcement = "Dropped item in ".concat(ariaLabel);
      var liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'assertive');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.textContent = announcement;
      document.body.appendChild(liveRegion);
      setTimeout(function () {
        return document.body.removeChild(liveRegion);
      }, 1000);
    }
    if (onDrop) {
      onDrop(e, dragState.draggedData);
    }
    setDragState({
      isActive: false,
      isValid: false,
      draggedData: null
    });
  }, [disabled, dragState, ariaLabel, onDrop]);
  var getMessage = function getMessage() {
    if (!dragState.isActive) return emptyMessage;
    return dragState.isValid ? validMessage : invalidMessage;
  };
  var getIcon = function getIcon() {
    if (!dragState.isActive) return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DragOutlined */ .duJ, null);
    return dragState.isValid ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckCircleOutlined */ .hWy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExclamationCircleOutlined */ .G2i, null);
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DropZone, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({
    ref: dropRef,
    isActive: dragState.isActive,
    isValid: dragState.isValid,
    isInvalid: dragState.isActive && !dragState.isValid,
    minHeight: minHeight,
    onDragEnter: handleDragEnter,
    onDragOver: handleDragOver,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop,
    className: className,
    style: style,
    role: "region",
    "aria-label": ariaLabel || 'Drop zone',
    "aria-dropeffect": dragState.isActive ? dragState.isValid ? 'move' : 'none' : 'none',
    tabIndex: -1
  }, props), children, showMessage && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DropMessage, {
    className: "drop-message",
    visible: dragState.isActive || !children
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
    className: "drop-icon"
  }, getIcon()), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "drop-text"
  }, getMessage()), !dragState.isActive && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "drop-hint"
  }, accepts.length > 0 ? "Accepts: ".concat(accepts.join(', ')) : 'Accepts any item')));
};

// Drag and Drop Context Provider
var DragDropProvider = function DragDropProvider(_ref3) {
  var children = _ref3.children,
    _ref3$showOverlay = _ref3.showOverlay,
    showOverlay = _ref3$showOverlay === void 0 ? true : _ref3$showOverlay;
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isDragging = _useState8[0],
    setIsDragging = _useState8[1];
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    var handleDragStart = function handleDragStart() {
      return setIsDragging(true);
    };
    var handleDragEnd = function handleDragEnd() {
      return setIsDragging(false);
    };
    document.addEventListener('dragstart', handleDragStart);
    document.addEventListener('dragend', handleDragEnd);
    return function () {
      document.removeEventListener('dragstart', handleDragStart);
      document.removeEventListener('dragend', handleDragEnd);
    };
  }, []);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, children, showOverlay && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DragOverlay, {
    visible: isDragging
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
  Draggable: EnhancedDraggable,
  DropZone: EnhancedDropZone,
  Provider: DragDropProvider
});

/***/ })

}]);