/**
 * Tutorial Overlay Component
 * 
 * Provides the main overlay system for tutorials with element highlighting,
 * step navigation, and interactive guidance.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Button, Progress, Typography, Space, Tooltip } from 'antd';
import {
  CloseOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  StepForwardOutlined as SkipNextOutlined // Using StepForwardOutlined as replacement
} from '@ant-design/icons';
import styled from 'styled-components';
import { useTutorial } from './TutorialManager';
import {
  TUTORIAL_STEP_TYPES,
  TUTORIAL_SHORTCUTS,
  ANIMATION_DURATIONS,
  Z_INDEX
} from './types';
import {
  TutorialVisualFeedback,
  TutorialProgressRing,
  injectTutorialStyles
} from './TutorialVisualEnhancements';

const { Title, Text } = Typography;

// Styled Components
const OverlayContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: ${Z_INDEX.TUTORIAL_OVERLAY};
  pointer-events: ${props => props.allowInteraction ? 'none' : 'auto'};
  transition: opacity ${ANIMATION_DURATIONS.OVERLAY_FADE}ms ease-in-out;
  opacity: ${props => props.visible ? 1 : 0};
`;

const HighlightArea = styled.div`
  position: absolute;
  background: transparent;
  border: 2px solid #1890ff;
  border-radius: ${props => props.borderRadius || 4}px;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2);
  z-index: ${Z_INDEX.TUTORIAL_HIGHLIGHT};
  pointer-events: none;
  transition: all ${ANIMATION_DURATIONS.HIGHLIGHT_FADE_IN}ms ease-in-out;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0% { box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2); }
    50% { box-shadow: 0 0 0 8px rgba(24, 144, 255, 0.1); }
    100% { box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.2); }
  }
`;

const StepTooltip = styled.div`
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
  max-width: 350px;
  min-width: 280px;
  z-index: ${Z_INDEX.TUTORIAL_TOOLTIP};
  pointer-events: auto;
  
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    
    ${props => {
    switch (props.position) {
      case 'top':
        return `
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 8px 8px 0 8px;
            border-color: white transparent transparent transparent;
          `;
      case 'bottom':
        return `
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 0 8px 8px 8px;
            border-color: transparent transparent white transparent;
          `;
      case 'left':
        return `
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            border-width: 8px 0 8px 8px;
            border-color: transparent transparent transparent white;
          `;
      case 'right':
        return `
            left: -8px;
            top: 50%;
            transform: translateY(-50%);
            border-width: 8px 8px 8px 0;
            border-color: transparent white transparent transparent;
          `;
      default:
        return '';
    }
  }}
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  gap: 8px;
`;

const ProgressContainer = styled.div`
  margin: 12px 0;
`;

const TutorialOverlay = () => {
  const {
    isActive,
    isPaused,
    activeTutorial,
    currentStep,
    currentStepIndex,
    nextStep,
    previousStep,
    pauseTutorial,
    resumeTutorial,
    skipTutorial,
    completeTutorial,
    preferences
  } = useTutorial();

  const [highlightRect, setHighlightRect] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [tooltipPlacement, setTooltipPlacement] = useState('bottom');
  const overlayRef = useRef(null);
  const resizeObserverRef = useRef(null);
  const [targetElement, setTargetElement] = useState(null);

  // Inject enhanced tutorial styles
  useEffect(() => {
    injectTutorialStyles();
  }, []);

  // Calculate element position and highlight
  const updateHighlight = useCallback(() => {
    if (!currentStep || !currentStep.targetSelector) {
      setHighlightRect(null);
      return;
    }

    const element = document.querySelector(currentStep.targetSelector);
    if (!element) {
      console.warn(`Target element not found: ${currentStep.targetSelector}`);
      setHighlightRect(null);
      setTargetElement(null);
      return;
    }

    setTargetElement(element);

    const rect = element.getBoundingClientRect();
    const padding = currentStep.highlightPadding || 8;

    const highlightRect = {
      top: rect.top - padding,
      left: rect.left - padding,
      width: rect.width + (padding * 2),
      height: rect.height + (padding * 2)
    };

    setHighlightRect(highlightRect);

    // Calculate tooltip position
    const tooltipWidth = 350;
    const tooltipHeight = 200; // Estimated
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let x, y, placement;

    // Determine best placement
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    const spaceRight = viewportWidth - rect.right;
    const spaceLeft = rect.left;

    if (currentStep.position === 'auto') {
      if (spaceBelow >= tooltipHeight) {
        placement = 'bottom';
        x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
        y = rect.bottom + 16;
      } else if (spaceAbove >= tooltipHeight) {
        placement = 'top';
        x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
        y = rect.top - tooltipHeight - 16;
      } else if (spaceRight >= tooltipWidth) {
        placement = 'right';
        x = rect.right + 16;
        y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
      } else if (spaceLeft >= tooltipWidth) {
        placement = 'left';
        x = rect.left - tooltipWidth - 16;
        y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
      } else {
        // Center on screen if no good position
        placement = 'center';
        x = (viewportWidth - tooltipWidth) / 2;
        y = (viewportHeight - tooltipHeight) / 2;
      }
    } else {
      placement = currentStep.position;
      // Use specified position logic
      switch (placement) {
        case 'bottom':
          x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
          y = rect.bottom + 16;
          break;
        case 'top':
          x = Math.max(10, Math.min(rect.left, viewportWidth - tooltipWidth - 10));
          y = rect.top - tooltipHeight - 16;
          break;
        case 'right':
          x = rect.right + 16;
          y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
          break;
        case 'left':
          x = rect.left - tooltipWidth - 16;
          y = Math.max(10, Math.min(rect.top, viewportHeight - tooltipHeight - 10));
          break;
        default:
          x = (viewportWidth - tooltipWidth) / 2;
          y = (viewportHeight - tooltipHeight) / 2;
      }
    }

    setTooltipPosition({ x, y });
    setTooltipPlacement(placement);
  }, [currentStep]);

  // Update highlight when step changes or window resizes
  useEffect(() => {
    if (!isActive || !currentStep) return;

    updateHighlight();

    // Set up resize observer for target element
    if (currentStep.targetSelector) {
      const targetElement = document.querySelector(currentStep.targetSelector);
      if (targetElement && window.ResizeObserver) {
        resizeObserverRef.current = new ResizeObserver(updateHighlight);
        resizeObserverRef.current.observe(targetElement);
      }
    }

    // Listen for window resize
    window.addEventListener('resize', updateHighlight);
    window.addEventListener('scroll', updateHighlight);

    return () => {
      window.removeEventListener('resize', updateHighlight);
      window.removeEventListener('scroll', updateHighlight);
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [isActive, currentStep, updateHighlight]);

  // Keyboard shortcuts
  useEffect(() => {
    if (!isActive || !preferences.enableKeyboardShortcuts) return;

    const handleKeyDown = (e) => {
      switch (e.key) {
        case TUTORIAL_SHORTCUTS.NEXT_STEP:
          e.preventDefault();
          nextStep();
          break;
        case TUTORIAL_SHORTCUTS.PREVIOUS_STEP:
          e.preventDefault();
          previousStep();
          break;
        case TUTORIAL_SHORTCUTS.SKIP_TUTORIAL:
          e.preventDefault();
          skipTutorial();
          break;
        case TUTORIAL_SHORTCUTS.PAUSE_TUTORIAL:
          e.preventDefault();
          if (isPaused) {
            resumeTutorial();
          } else {
            pauseTutorial();
          }
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isActive, isPaused, nextStep, previousStep, skipTutorial, pauseTutorial, resumeTutorial, preferences.enableKeyboardShortcuts]);

  // Auto-advance step
  useEffect(() => {
    if (!currentStep || !currentStep.autoAdvance || isPaused) return;

    const timer = setTimeout(() => {
      nextStep();
    }, currentStep.autoAdvanceDelay || 3000);

    return () => clearTimeout(timer);
  }, [currentStep, isPaused, nextStep]);

  // Call step lifecycle methods
  useEffect(() => {
    if (!currentStep) return;

    if (currentStep.onEnter) {
      currentStep.onEnter(currentStep, activeTutorial);
    }

    return () => {
      if (currentStep.onExit) {
        currentStep.onExit(currentStep, activeTutorial);
      }
    };
  }, [currentStep, activeTutorial]);

  if (!isActive || !activeTutorial || !currentStep) {
    return null;
  }

  const progress = ((currentStepIndex + 1) / activeTutorial.steps.length) * 100;
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === activeTutorial.steps.length - 1;

  const renderStepContent = () => {
    if (currentStep.customComponent) {
      return currentStep.customComponent;
    }

    return (
      <StepTooltip
        position={tooltipPlacement}
        style={{
          left: tooltipPosition.x,
          top: tooltipPosition.y
        }}
      >
        <div>
          <Title level={4} style={{ margin: '0 0 8px 0' }}>
            {currentStep.title}
          </Title>

          <Text>{currentStep.content}</Text>

          {preferences.showProgressIndicator && (
            <ProgressContainer>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <TutorialProgressRing
                  progress={currentStepIndex + 1}
                  total={activeTutorial.steps.length}
                  size={50}
                />
                <div>
                  <Progress
                    percent={progress}
                    size="small"
                    showInfo={false}
                    strokeColor="#1890ff"
                  />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    Step {currentStepIndex + 1} of {activeTutorial.steps.length}
                  </Text>
                </div>
              </div>
            </ProgressContainer>
          )}

          <ControlsContainer>
            <Space>
              <Tooltip title="Previous Step">
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={previousStep}
                  disabled={isFirstStep}
                  size="small"
                />
              </Tooltip>

              <Tooltip title={isPaused ? "Resume" : "Pause"}>
                <Button
                  icon={isPaused ? <PlayCircleOutlined /> : <PauseOutlined />}
                  onClick={isPaused ? resumeTutorial : pauseTutorial}
                  size="small"
                />
              </Tooltip>
            </Space>

            <Space>
              {currentStep.showSkip && (
                <Tooltip title="Skip Tutorial">
                  <Button
                    icon={<SkipNextOutlined />}
                    onClick={skipTutorial}
                    size="small"
                    type="text"
                  >
                    Skip
                  </Button>
                </Tooltip>
              )}

              <Button
                type="primary"
                icon={<ArrowRightOutlined />}
                onClick={isLastStep ? completeTutorial : nextStep}
                size="small"
              >
                {isLastStep ? 'Complete' : 'Next'}
              </Button>
            </Space>
          </ControlsContainer>
        </div>
      </StepTooltip>
    );
  };

  return createPortal(
    <OverlayContainer
      ref={overlayRef}
      visible={isActive && !isPaused}
      allowInteraction={currentStep.type === TUTORIAL_STEP_TYPES.INTERACTIVE}
    >
      {/* Enhanced Visual Feedback */}
      {targetElement && (
        <TutorialVisualFeedback
          targetElement={targetElement}
          highlightType={currentStep.type === TUTORIAL_STEP_TYPES.INTERACTIVE ? 'pulse' : 'glow'}
          showSpotlight={currentStep.showSpotlight || false}
          showInteractiveHint={currentStep.type === TUTORIAL_STEP_TYPES.INTERACTIVE}
          animationType="fadeIn"
        />
      )}

      {/* Highlight Area */}
      {highlightRect && (
        <HighlightArea
          style={{
            top: highlightRect.top,
            left: highlightRect.left,
            width: highlightRect.width,
            height: highlightRect.height
          }}
          borderRadius={currentStep.highlightBorderRadius}
        />
      )}

      {/* Step Content */}
      {renderStepContent()}

      {/* Close Button */}
      <Button
        icon={<CloseOutlined />}
        onClick={skipTutorial}
        style={{
          position: 'absolute',
          top: 20,
          right: 20,
          zIndex: Z_INDEX.TUTORIAL_CONTROLS
        }}
        type="text"
        size="large"
      />
    </OverlayContainer>,
    document.body
  );
};

export default TutorialOverlay;
