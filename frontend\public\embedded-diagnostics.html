<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embedded React Diagnostics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .diagnostic-overlay {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 80vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            overflow: hidden;
            border: 2px solid #3b82f6;
        }
        .diagnostic-header {
            background: #3b82f6;
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .diagnostic-content {
            padding: 16px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .status {
            padding: 6px 10px;
            border-radius: 4px;
            margin: 4px 0;
            font-size: 14px;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.info { background: #dbeafe; color: #1e40af; }
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .toggle-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            z-index: 9999;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <button id="toggleBtn" class="toggle-btn">🔍 Diagnostics</button>
    
    <div id="diagnosticOverlay" class="diagnostic-overlay hidden">
        <div class="diagnostic-header">
            <span>🔍 React Diagnostics</span>
            <button class="close-btn" onclick="hideDiagnostics()">×</button>
        </div>
        <div class="diagnostic-content" id="diagnosticContent">
            <div class="status info">🔄 Initializing diagnostics...</div>
        </div>
    </div>

    <script>
        let diagnosticInterval;
        let isVisible = false;

        function showDiagnostics() {
            document.getElementById('diagnosticOverlay').classList.remove('hidden');
            document.getElementById('toggleBtn').classList.add('hidden');
            isVisible = true;
            runContinuousDiagnostics();
        }

        function hideDiagnostics() {
            document.getElementById('diagnosticOverlay').classList.add('hidden');
            document.getElementById('toggleBtn').classList.remove('hidden');
            isVisible = false;
            if (diagnosticInterval) {
                clearInterval(diagnosticInterval);
            }
        }

        function runDiagnostics() {
            const content = document.getElementById('diagnosticContent');
            let html = '';

            // Get current timestamp
            const timestamp = new Date().toLocaleTimeString();
            html += `<div class="status info">🕐 Last check: ${timestamp}</div>`;

            // Check React globals
            const reactAvailable = typeof window.React !== 'undefined';
            const reactDOMAvailable = typeof window.ReactDOM !== 'undefined';
            const reactVersion = window.React?.version || null;

            html += `<div class="status ${reactAvailable ? 'success' : 'error'}">
                ${reactAvailable ? '✅' : '❌'} React: ${reactAvailable ? reactVersion : 'Not Available'}
            </div>`;

            html += `<div class="status ${reactDOMAvailable ? 'success' : 'error'}">
                ${reactDOMAvailable ? '✅' : '❌'} ReactDOM: ${reactDOMAvailable ? 'Available' : 'Not Available'}
            </div>`;

            // Check app state
            const appLoaded = window.__APP_LOADED__;
            const appLoading = window.__APP_LOADING__;
            const reactLoaded = window.__REACT_LOADED__;

            html += `<div class="status ${appLoaded ? 'success' : 'error'}">
                ${appLoaded ? '✅' : '❌'} App Loaded: ${appLoaded || 'false'}
            </div>`;

            html += `<div class="status ${!appLoading ? 'success' : 'error'}">
                ${!appLoading ? '✅' : '❌'} App Loading: ${appLoading || 'false'}
            </div>`;

            html += `<div class="status ${reactLoaded ? 'success' : 'error'}">
                ${reactLoaded ? '✅' : '❌'} React Loaded: ${reactLoaded || 'false'}
            </div>`;

            // Check DOM
            const rootElement = document.getElementById('root');
            const rootExists = !!rootElement;
            const rootHasContent = rootElement ? rootElement.innerHTML.trim().length > 0 : false;

            html += `<div class="status ${rootExists ? 'success' : 'error'}">
                ${rootExists ? '✅' : '❌'} Root Element: ${rootExists ? 'Found' : 'Not Found'}
            </div>`;

            html += `<div class="status ${rootHasContent ? 'success' : 'error'}">
                ${rootHasContent ? '✅' : '❌'} Root Content: ${rootHasContent ? 'Has Content' : 'Empty'}
            </div>`;

            // Check CSS
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]').length;
            html += `<div class="status ${cssLinks > 0 ? 'success' : 'error'}">
                ${cssLinks > 0 ? '✅' : '❌'} CSS Links: ${cssLinks}
            </div>`;

            // Overall status
            const allChecks = [reactAvailable, reactDOMAvailable, appLoaded, !appLoading, rootExists, rootHasContent, cssLinks > 0];
            const passedChecks = allChecks.filter(check => check).length;
            const totalChecks = allChecks.length;

            html += `<div class="status ${passedChecks === totalChecks ? 'success' : 'error'}">
                📊 Overall: ${passedChecks}/${totalChecks} checks passed
            </div>`;

            content.innerHTML = html;
        }

        function runContinuousDiagnostics() {
            runDiagnostics();
            diagnosticInterval = setInterval(runDiagnostics, 2000);
        }

        // Event listeners
        document.getElementById('toggleBtn').addEventListener('click', showDiagnostics);

        // Auto-inject into main app if we're in an iframe
        function injectIntoMainApp() {
            try {
                if (window.parent && window.parent !== window) {
                    const parentDoc = window.parent.document;
                    
                    // Check if already injected
                    if (parentDoc.getElementById('embeddedDiagnostics')) {
                        return;
                    }

                    // Create script element
                    const script = parentDoc.createElement('script');
                    script.id = 'embeddedDiagnostics';
                    script.innerHTML = `
                        // Embedded diagnostics code
                        if (!window.diagnosticsInjected) {
                            window.diagnosticsInjected = true;
                            
                            const style = document.createElement('style');
                            style.textContent = \`
                                .diagnostic-widget {
                                    position: fixed;
                                    bottom: 20px;
                                    right: 20px;
                                    background: #3b82f6;
                                    color: white;
                                    padding: 10px 16px;
                                    border-radius: 6px;
                                    cursor: pointer;
                                    font-weight: 500;
                                    z-index: 10000;
                                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                    font-size: 14px;
                                    border: none;
                                    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                                }
                                .diagnostic-widget:hover {
                                    background: #2563eb;
                                }
                            \`;
                            document.head.appendChild(style);
                            
                            const widget = document.createElement('button');
                            widget.className = 'diagnostic-widget';
                            widget.textContent = '🔍 React Status';
                            widget.onclick = () => {
                                window.open('/embedded-diagnostics.html', 'diagnostics', 'width=500,height=600');
                            };
                            document.body.appendChild(widget);
                        }
                    `;
                    parentDoc.head.appendChild(script);
                }
            } catch (error) {
                console.log('Cannot inject into parent:', error.message);
            }
        }

        // Try to inject into main app
        window.addEventListener('load', () => {
            setTimeout(injectIntoMainApp, 1000);
        });

        // If opened as popup, start diagnostics immediately
        if (window.opener) {
            window.addEventListener('load', () => {
                setTimeout(showDiagnostics, 500);
            });
        }
    </script>
</body>
</html>
