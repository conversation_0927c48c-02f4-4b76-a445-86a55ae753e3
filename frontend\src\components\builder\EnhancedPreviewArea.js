import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { Typography, Table, Form, Input, Button, Card, Divider, Empty, Tooltip, Space, Slider, Switch, Select, Badge } from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  BorderOutlined,
  DragOutlined,
  EyeOutlined,
  SettingOutlined,
  FullscreenOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  ReloadOutlined,
  SyncOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useSelector, useDispatch } from 'react-redux';
import { debounce } from 'lodash';
import useRealTimePreview from '../../hooks/useRealTimePreview';
import usePreviewPerformance from '../../hooks/usePreviewPerformance';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// Device configurations for responsive preview
const DEVICE_CONFIGS = {
  mobile: {
    name: 'Mobile',
    icon: <MobileOutlined />,
    width: 375,
    height: 667,
    scale: 0.8,
    frame: true
  },
  tablet: {
    name: 'Tablet',
    icon: <TabletOutlined />,
    width: 768,
    height: 1024,
    scale: 0.7,
    frame: true
  },
  desktop: {
    name: 'Desktop',
    icon: <DesktopOutlined />,
    width: 1200,
    height: 800,
    scale: 1,
    frame: false
  }
};

// Styled components for enhanced preview area
const PreviewContainer = styled.div`
  position: relative;
  height: 100%;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const PreviewToolbar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  flex-wrap: wrap;
  gap: 8px;

  @media (max-width: 768px) {
    padding: 6px 12px;
    gap: 6px;
  }
`;

const DeviceSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  background: #f5f5f5;
  border-radius: 6px;
`;

const DeviceButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 4px;
  border: none;
  background: ${props => props.active ? '#1890ff' : 'transparent'};
  color: ${props => props.active ? 'white' : '#666'};
  box-shadow: none;

  &:hover {
    background: ${props => props.active ? '#40a9ff' : '#e6f7ff'};
    color: ${props => props.active ? 'white' : '#1890ff'};
  }
`;

const DeviceFrame = styled.div`
  position: relative;
  margin: 20px auto;
  background: ${props => props.deviceType === 'mobile' ? '#333' : props.deviceType === 'tablet' ? '#444' : 'transparent'};
  border-radius: ${props => props.deviceType === 'mobile' ? '25px' : props.deviceType === 'tablet' ? '15px' : '0'};
  padding: ${props => props.deviceType === 'mobile' ? '20px 10px' : props.deviceType === 'tablet' ? '15px' : '0'};
  box-shadow: ${props => props.frame ? '0 8px 32px rgba(0, 0, 0, 0.3)' : 'none'};
  transition: all 0.3s ease;

  ${props => props.deviceType === 'mobile' && `
    &::before {
      content: '';
      position: absolute;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #666;
      border-radius: 2px;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 40px;
      border: 2px solid #666;
      border-radius: 50%;
    }
  `}

  ${props => props.deviceType === 'tablet' && `
    &::before {
      content: '';
      position: absolute;
      bottom: 6px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 30px;
      border: 2px solid #666;
      border-radius: 50%;
    }
  `}
`;

const ResponsiveCanvas = styled.div`
  width: ${props => props.deviceWidth}px;
  height: ${props => props.deviceHeight}px;
  max-width: 100%;
  max-height: 100%;
  background: white;
  border-radius: ${props => props.deviceType === 'mobile' ? '8px' : props.deviceType === 'tablet' ? '6px' : '0'};
  overflow: auto;
  position: relative;
  transform: scale(${props => props.scale});
  transform-origin: top center;
  transition: all 0.3s ease;

  @media (max-width: 1200px) {
    transform: scale(${props => Math.min(props.scale, 0.8)});
  }

  @media (max-width: 768px) {
    transform: scale(${props => Math.min(props.scale, 0.6)});
  }
`;

const CanvasContainer = styled.div`
  flex: 1;
  position: relative;
  overflow: auto;
  background: ${props => props.showGrid ?
    `radial-gradient(circle, #ddd 1px, transparent 1px)` :
    '#f5f5f5'
  };
  background-size: ${props => props.gridSize || 20}px ${props => props.gridSize || 20}px;
  background-position: ${props => props.gridOffset?.x || 0}px ${props => props.gridOffset?.y || 0}px;
`;

const Canvas = styled.div`
  min-height: 100%;
  min-width: 100%;
  position: relative;
  transform: scale(${props => props.zoom || 1});
  transform-origin: top left;
  transition: transform 0.2s ease;
  padding: ${props => props.previewMode ? '0' : '32px'};
`;

const ComponentWrapper = styled.div.withConfig({
  shouldForwardProp: (prop) => !['isSelected', 'previewMode', 'isDragOver'].includes(prop),
})`
  position: relative;
  margin: 8px 0;
  border: ${props => props.isSelected ? '2px solid #1890ff' : '1px dashed transparent'};
  border-radius: 4px;
  background: ${props => props.isSelected ? 'rgba(24, 144, 255, 0.05)' : 'white'};
  transition: all 0.3s ease;
  cursor: ${props => props.previewMode ? 'default' : 'pointer'};

  &:hover {
    border-color: ${props => props.previewMode ? 'transparent' : '#1890ff'};
    box-shadow: ${props => props.previewMode ? 'none' : '0 2px 8px rgba(24, 144, 255, 0.2)'};
    transform: ${props => props.previewMode ? 'none' : 'translateY(-1px)'};
  }

  ${props => props.isDragOver && `
    border-color: #52c41a !important;
    background: rgba(82, 196, 26, 0.1) !important;
    transform: scale(1.02);
  `}
`;

const ComponentControls = styled.div`
  position: absolute;
  top: -2px;
  right: -2px;
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.95);
  padding: 4px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: ${props => props.visible ? 1 : 0};
  transform: translateY(${props => props.visible ? '0' : '-10px'});
  transition: all 0.3s ease;
  z-index: 5;
`;

const ControlButton = styled(Button)`
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: none;
  
  &:hover {
    background: #f0f0f0;
    transform: scale(1.1);
  }
`;

const DropZone = styled.div.withConfig({
  shouldForwardProp: (prop) => !['visible', 'isActive'].includes(prop),
})`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  opacity: ${props => props.visible ? 1 : 0};
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  transition: all 0.3s ease;
  z-index: 1;

  ${props => props.isActive && `
    border-color: #52c41a;
    background: rgba(82, 196, 26, 0.1);

    &::before {
      content: 'Drop component here';
      color: #52c41a;
      font-weight: 600;
      font-size: 16px;
    }
  `}
`;

const Ruler = styled.div`
  position: absolute;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e8e8e8;
  font-size: 10px;
  color: #666;
  z-index: 5;
  
  ${props => props.orientation === 'horizontal' && `
    top: 0;
    left: 32px;
    right: 0;
    height: 20px;
    border-bottom: 1px solid #e8e8e8;
    background-image: repeating-linear-gradient(
      to right,
      transparent,
      transparent 9px,
      #e8e8e8 9px,
      #e8e8e8 10px
    );
  `}
  
  ${props => props.orientation === 'vertical' && `
    top: 20px;
    left: 0;
    bottom: 0;
    width: 32px;
    border-right: 1px solid #e8e8e8;
    background-image: repeating-linear-gradient(
      to bottom,
      transparent,
      transparent 9px,
      #e8e8e8 9px,
      #e8e8e8 10px
    );
  `}
`;

const EnhancedPreviewArea = ({
  components = [],
  onSelectComponent,
  onDeleteComponent,
  onUpdateComponent,
  onMoveComponent,
  previewMode = false,
  selectedComponentId,
  onDrop,
  onDragOver,
  onDragLeave,
  realTimeUpdates = true,
  websocketConnected = false
}) => {
  // Device and preview state
  const [deviceType, setDeviceType] = useState('desktop');
  const [zoom, setZoom] = useState(1);
  const [showGrid, setShowGrid] = useState(true);
  const [gridSize, setGridSize] = useState(20);
  const [showRulers, setShowRulers] = useState(false);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragOverComponent, setDragOverComponent] = useState(null);
  const [hoveredComponent, setHoveredComponent] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // Refs
  const canvasRef = useRef(null);
  const updateTimeoutRef = useRef(null);

  // Redux state
  const dispatch = useDispatch();
  const websocketState = useSelector(state => state.websocket || {});
  const websocketService = useSelector(state => state.websocket?.service);

  // Get current device configuration
  const currentDevice = DEVICE_CONFIGS[deviceType];

  // Memoized device-specific styles
  const deviceStyles = useMemo(() => ({
    width: currentDevice.width,
    height: currentDevice.height,
    scale: currentDevice.scale
  }), [currentDevice]);

  // Real-time preview hook
  const {
    isUpdating: realtimeUpdating,
    lastUpdateTime: realtimeLastUpdate,
    websocketConnected: realtimeWebsocketConnected,
    updateComponent: realtimeUpdateComponent,
    addComponent: realtimeAddComponent,
    deleteComponent: realtimeDeleteComponent,
    getAllComponents: realtimeGetAllComponents,
    forceUpdate: realtimeForceUpdate
  } = useRealTimePreview({
    components,
    onUpdateComponent,
    onAddComponent: (componentData) => {
      // Handle adding component through parent callback
      if (onUpdateComponent) {
        onUpdateComponent(componentData);
      }
    },
    onDeleteComponent,
    websocketService,
    enableWebSocket: realTimeUpdates && websocketConnected
  });

  // Performance optimization hook
  const {
    visibleComponents,
    getContainerProps,
    getSpacerProps,
    renderTime,
    frameRate,
    memoryUsage,
    startRenderMeasurement,
    endRenderMeasurement,
    getCachedComponent
  } = usePreviewPerformance({
    components: realtimeGetAllComponents(),
    containerHeight: deviceStyles.height,
    itemHeight: deviceType === 'mobile' ? 60 : deviceType === 'tablet' ? 80 : 100,
    enableVirtualization: components.length > 20,
    enablePerformanceMonitoring: true
  });

  // Handle device type change
  const handleDeviceChange = useCallback((newDeviceType) => {
    setDeviceType(newDeviceType);
    // Adjust zoom for better fit
    const device = DEVICE_CONFIGS[newDeviceType];
    if (device.scale !== zoom) {
      setZoom(device.scale);
    }
  }, [zoom]);

  // Handle zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 2));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.5));
  const handleZoomReset = () => setZoom(currentDevice.scale);

  // Real-time component update handler
  const handleRealTimeUpdate = useCallback((componentId, updates, immediate = true) => {
    if (realTimeUpdates) {
      realtimeUpdateComponent(componentId, updates, immediate);
      setIsUpdating(true);
      setLastUpdateTime(new Date());

      // Clear updating state after a short delay
      setTimeout(() => setIsUpdating(false), 500);
    }
  }, [realTimeUpdates, realtimeUpdateComponent]);

  // Update state when real-time hooks change
  useEffect(() => {
    if (realtimeUpdating !== isUpdating) {
      setIsUpdating(realtimeUpdating);
    }
    if (realtimeLastUpdate && realtimeLastUpdate !== lastUpdateTime) {
      setLastUpdateTime(realtimeLastUpdate);
    }
  }, [realtimeUpdating, realtimeLastUpdate, isUpdating, lastUpdateTime]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Handle drag and drop
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    if (onDragOver) onDragOver(e);
  }, [onDragOver]);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      setDragOverComponent(null);
      if (onDragLeave) onDragLeave(e);
    }
  }, [onDragLeave]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    setDragOverComponent(null);

    if (onDrop) {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        const x = (e.clientX - rect.left) / zoom;
        const y = (e.clientY - rect.top) / zoom;

        // Snap to grid if enabled
        const finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x;
        const finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y;

        onDrop(e, { x: finalX, y: finalY });
      }
    }
  }, [onDrop, zoom, snapToGrid, gridSize]);

  // Component drag over handler
  const handleComponentDragOver = useCallback((e, componentId) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverComponent(componentId);
  }, []);

  const handleComponentDragLeave = useCallback((e, componentId) => {
    e.preventDefault();
    e.stopPropagation();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDragOverComponent(null);
    }
  }, []);

  // Render individual component with responsive behavior and caching
  const renderComponent = useCallback((componentData, index) => {
    const component = componentData.component || componentData;
    const componentIndex = componentData.index !== undefined ? componentData.index : index;

    return getCachedComponent(component.id, () => {
      startRenderMeasurement();

      const isSelected = component.id === selectedComponentId;
      const isHovered = hoveredComponent === component.id;
      const isDraggedOver = dragOverComponent === component.id;

      // Get responsive styles based on device type
      const getResponsiveStyles = () => {
        const baseStyles = {
          fontSize: deviceType === 'mobile' ? '14px' : deviceType === 'tablet' ? '15px' : '16px',
          padding: deviceType === 'mobile' ? '8px' : deviceType === 'tablet' ? '12px' : '16px'
        };

        return baseStyles;
      };

      const ComponentContent = () => {
        const responsiveStyles = getResponsiveStyles();

        switch (component.type) {
          case 'text':
            return (
              <Text style={responsiveStyles}>
                {component.props?.content || 'Sample text'}
              </Text>
            );
          case 'button':
            return (
              <Button
                type={component.props?.type || 'default'}
                size={deviceType === 'mobile' ? 'small' : 'middle'}
                style={{ fontSize: responsiveStyles.fontSize }}
              >
                {component.props?.text || 'Button'}
              </Button>
            );
          case 'header':
            return (
              <Title
                level={component.props?.level || (deviceType === 'mobile' ? 4 : 2)}
                style={responsiveStyles}
              >
                {component.props?.text || 'Header'}
              </Title>
            );
          case 'card':
            return (
              <Card
                title={component.props?.title || 'Card Title'}
                size={deviceType === 'mobile' ? 'small' : 'default'}
                style={{ fontSize: responsiveStyles.fontSize }}
              >
                {component.props?.content || 'Card content'}
              </Card>
            );
          case 'image':
            return (
              <img
                src={component.props?.src || 'https://via.placeholder.com/150'}
                alt={component.props?.alt || 'Image'}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  borderRadius: deviceType === 'mobile' ? '4px' : '6px'
                }}
              />
            );
          case 'divider':
            return <Divider style={responsiveStyles}>{component.props?.text}</Divider>;
          case 'input':
            return (
              <Input
                placeholder={component.props?.placeholder || 'Enter text'}
                disabled={previewMode ? false : true}
                size={deviceType === 'mobile' ? 'small' : 'middle'}
                style={responsiveStyles}
              />
            );
          case 'form':
            return (
              <Form layout="vertical" size={deviceType === 'mobile' ? 'small' : 'middle'}>
                <Form.Item label="Sample Field">
                  <Input
                    placeholder="Sample input"
                    disabled={!previewMode}
                    style={responsiveStyles}
                  />
                </Form.Item>
              </Form>
            );
          case 'table':
            const columns = [
              { title: 'Name', dataIndex: 'name', key: 'name' },
              { title: 'Age', dataIndex: 'age', key: 'age' },
            ];
            const data = [
              { key: '1', name: 'John', age: 32 },
              { key: '2', name: 'Jane', age: 28 },
            ];
            return (
              <Table
                columns={columns}
                dataSource={data}
                size={deviceType === 'mobile' ? 'small' : 'middle'}
                scroll={deviceType === 'mobile' ? { x: true } : undefined}
              />
            );
          default:
            return (
              <div style={{
                padding: responsiveStyles.padding,
                border: '1px dashed #ccc',
                textAlign: 'center',
                fontSize: responsiveStyles.fontSize,
                borderRadius: deviceType === 'mobile' ? '4px' : '6px'
              }}>
                {component.type} Component
              </div>
            );
        }
      };

      return (
        <ComponentWrapper
          key={component.id}
          isSelected={isSelected}
          previewMode={previewMode}
          isDragOver={isDraggedOver}
          onClick={(e) => {
            e.stopPropagation();
            if (!previewMode && onSelectComponent) {
              onSelectComponent(component);
            }
          }}
          onMouseEnter={() => setHoveredComponent(component.id)}
          onMouseLeave={() => setHoveredComponent(null)}
          onDragOver={(e) => handleComponentDragOver(e, component.id)}
          onDragLeave={(e) => handleComponentDragLeave(e, component.id)}
          style={{
            padding: deviceType === 'mobile' ? '8px' : deviceType === 'tablet' ? '12px' : '16px',
            position: 'relative',
            margin: deviceType === 'mobile' ? '4px 0' : '8px 0'
          }}
        >
          <ComponentContent />

          {!previewMode && (isSelected || isHovered) && (
            <ComponentControls visible={isSelected || isHovered}>
              <Tooltip title="Edit">
                <ControlButton
                  icon={<EditOutlined />}
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Trigger real-time update for edit mode
                    if (realTimeUpdates) {
                      handleRealTimeUpdate(component.id, { editing: true });
                    }
                  }}
                />
              </Tooltip>
              <Tooltip title="Copy">
                <ControlButton
                  icon={<CopyOutlined />}
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle copy with real-time update
                    if (realTimeUpdates) {
                      const copiedComponent = { ...component, id: Date.now().toString() };
                      handleRealTimeUpdate(copiedComponent.id, copiedComponent);
                    }
                  }}
                />
              </Tooltip>
              {deviceType === 'desktop' && (
                <>
                  <Tooltip title="Move Up">
                    <ControlButton
                      icon={<ArrowUpOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onMoveComponent) onMoveComponent(component.id, 'up');
                        if (realTimeUpdates) {
                          handleRealTimeUpdate(component.id, { moved: 'up' });
                        }
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="Move Down">
                    <ControlButton
                      icon={<ArrowDownOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        if (onMoveComponent) onMoveComponent(component.id, 'down');
                        if (realTimeUpdates) {
                          handleRealTimeUpdate(component.id, { moved: 'down' });
                        }
                      }}
                    />
                  </Tooltip>
                </>
              )}
              <Tooltip title="Delete">
                <ControlButton
                  icon={<DeleteOutlined />}
                  size="small"
                  danger
                  onClick={(e) => {
                    e.stopPropagation();
                    if (onDeleteComponent) onDeleteComponent(component.id);
                    if (realTimeUpdates) {
                      handleRealTimeUpdate(component.id, { deleted: true });
                    }
                  }}
                />
              </Tooltip>
            </ComponentControls>
          )}
        </ComponentWrapper>
      );

      endRenderMeasurement();
      return result;
    });
  }, [
    selectedComponentId,
    hoveredComponent,
    dragOverComponent,
    deviceType,
    previewMode,
    realTimeUpdates,
    getCachedComponent,
    startRenderMeasurement,
    endRenderMeasurement,
    handleRealTimeUpdate,
    onMoveComponent,
    onDeleteComponent
  ]);

  return (
    <PreviewContainer>
      <PreviewToolbar>
        {/* Device Selector */}
        <Space>
          <Text strong>Preview</Text>
          <Divider type="vertical" />
          <DeviceSelector>
            {Object.entries(DEVICE_CONFIGS).map(([key, config]) => (
              <DeviceButton
                key={key}
                size="small"
                active={deviceType === key}
                onClick={() => handleDeviceChange(key)}
                icon={config.icon}
              >
                {!previewMode && config.name}
              </DeviceButton>
            ))}
          </DeviceSelector>
        </Space>

        {/* Real-time Status and Performance */}
        <Space>
          {realTimeUpdates && (
            <>
              <Badge
                status={realtimeWebsocketConnected ? "success" : "error"}
                text={realtimeWebsocketConnected ? "Live" : "Offline"}
              />
              {(isUpdating || realtimeUpdating) && <SyncOutlined spin />}
              {process.env.NODE_ENV === 'development' && (
                <Tooltip title={`Render: ${renderTime.toFixed(1)}ms | FPS: ${frameRate} | Memory: ${memoryUsage}MB`}>
                  <Badge
                    count={`${renderTime.toFixed(0)}ms`}
                    style={{ backgroundColor: renderTime > 16 ? '#ff4d4f' : '#52c41a' }}
                  />
                </Tooltip>
              )}
              <Divider type="vertical" />
            </>
          )}

          {!previewMode && (
            <>
              <Tooltip title="Zoom Out">
                <Button
                  icon={<ZoomOutOutlined />}
                  size="small"
                  onClick={handleZoomOut}
                  disabled={zoom <= 0.5}
                />
              </Tooltip>
              <Text style={{ minWidth: 40, textAlign: 'center' }}>
                {Math.round(zoom * 100)}%
              </Text>
              <Tooltip title="Zoom In">
                <Button
                  icon={<ZoomInOutlined />}
                  size="small"
                  onClick={handleZoomIn}
                  disabled={zoom >= 2}
                />
              </Tooltip>
              <Button size="small" onClick={handleZoomReset}>
                Reset
              </Button>
              <Divider type="vertical" />
            </>
          )}
        </Space>

        {/* Grid and Snap Controls */}
        {!previewMode && (
          <Space>
            <Tooltip title="Toggle Grid">
              <Switch
                checked={showGrid}
                onChange={setShowGrid}
                checkedChildren={<BorderOutlined />}
                unCheckedChildren={<BorderOutlined />}
                size="small"
              />
            </Tooltip>
            <Tooltip title="Toggle Rulers">
              <Switch
                checked={showRulers}
                onChange={setShowRulers}
                size="small"
              />
            </Tooltip>
            <Tooltip title="Snap to Grid">
              <Switch
                checked={snapToGrid}
                onChange={setSnapToGrid}
                size="small"
              />
            </Tooltip>
          </Space>
        )}

        {/* Last Update Time */}
        {realTimeUpdates && lastUpdateTime && (
          <Space>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Updated: {lastUpdateTime.toLocaleTimeString()}
            </Text>
          </Space>
        )}
      </PreviewToolbar>

      <CanvasContainer
        showGrid={showGrid && !previewMode && deviceType === 'desktop'}
        gridSize={gridSize}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {showRulers && !previewMode && deviceType === 'desktop' && (
          <>
            <Ruler orientation="horizontal" />
            <Ruler orientation="vertical" />
          </>
        )}

        {/* Device Frame for Mobile/Tablet */}
        <DeviceFrame
          deviceType={deviceType}
          frame={currentDevice.frame}
        >
          <ResponsiveCanvas
            {...getContainerProps()}
            ref={canvasRef}
            deviceWidth={deviceStyles.width}
            deviceHeight={deviceStyles.height}
            deviceType={deviceType}
            scale={zoom}
            onClick={() => onSelectComponent && onSelectComponent(null)}
          >
            {/* Performance metrics display in development */}
            {process.env.NODE_ENV === 'development' && realTimeUpdates && (
              <div style={{
                position: 'absolute',
                top: 10,
                right: 10,
                background: 'rgba(0,0,0,0.8)',
                color: 'white',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '12px',
                zIndex: 1000
              }}>
                <div>Render: {renderTime.toFixed(1)}ms</div>
                <div>FPS: {frameRate}</div>
                <div>Memory: {memoryUsage}MB</div>
                <div>Visible: {visibleComponents.length}/{components.length}</div>
              </div>
            )}

            {/* Virtual scrolling spacer - before */}
            <div {...getSpacerProps().before} />

            {visibleComponents.length > 0 ? (
              visibleComponents.map((componentData, index) =>
                renderComponent(componentData, index)
              )
            ) : components.length === 0 ? (
              <Empty
                description={
                  <span>
                    No components added yet.
                    <br />
                    {previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'}
                  </span>
                }
                style={{
                  margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
                  fontSize: deviceType === 'mobile' ? '14px' : '16px'
                }}
              />
            ) : null}

            {/* Virtual scrolling spacer - after */}
            <div {...getSpacerProps().after} />
          </ResponsiveCanvas>
        </DeviceFrame>

        <DropZone visible={isDragOver && !previewMode} isActive={isDragOver} />
      </CanvasContainer>
    </PreviewContainer>
  );
};

export default EnhancedPreviewArea;
