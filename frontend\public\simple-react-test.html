<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple React Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Simple React Test</h1>
    <p>This page tests if React globals are available by checking the main app.</p>
    
    <div id="results"></div>
    
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }
        
        function runTests() {
            addResult('Starting React global tests...', 'info');
            
            // Test 1: Check if React is available
            if (typeof window.React !== 'undefined') {
                addResult('✅ React is available globally', 'success');
                addResult(`React version: ${window.React.version || 'Unknown'}`, 'info');
            } else {
                addResult('❌ React is NOT available globally', 'error');
            }
            
            // Test 2: Check if ReactDOM is available
            if (typeof window.ReactDOM !== 'undefined') {
                addResult('✅ ReactDOM is available globally', 'success');
                addResult(`ReactDOM keys: ${Object.keys(window.ReactDOM).join(', ')}`, 'info');
            } else {
                addResult('❌ ReactDOM is NOT available globally', 'error');
            }
            
            // Test 3: Check if main app is loaded
            const rootElement = document.getElementById('root');
            if (rootElement) {
                addResult('✅ Root element found', 'success');
                if (rootElement.innerHTML.trim().length > 0) {
                    addResult('✅ Root element has content', 'success');
                } else {
                    addResult('❌ Root element is empty', 'error');
                }
            } else {
                addResult('❌ Root element not found', 'error');
            }
            
            // Test 4: Check scripts
            const scripts = Array.from(document.querySelectorAll('script[src]'));
            const mainScripts = scripts.filter(s => s.src.includes('main'));
            addResult(`Found ${scripts.length} total scripts, ${mainScripts.length} main scripts`, 'info');
            
            // Test 5: Try to access React from iframe (main app)
            setTimeout(() => {
                try {
                    // Try to access React from the main window
                    if (window.parent && window.parent !== window) {
                        if (typeof window.parent.React !== 'undefined') {
                            addResult('✅ React found in parent window', 'success');
                        } else {
                            addResult('❌ React not found in parent window', 'error');
                        }
                    }
                    
                    // Check if we can find React in any frame
                    let reactFound = false;
                    try {
                        if (window.frames) {
                            for (let i = 0; i < window.frames.length; i++) {
                                try {
                                    if (window.frames[i].React) {
                                        reactFound = true;
                                        addResult('✅ React found in frame ' + i, 'success');
                                        break;
                                    }
                                } catch (e) {
                                    // Cross-origin frame, skip
                                }
                            }
                        }
                    } catch (e) {
                        addResult('Frame access error: ' + e.message, 'error');
                    }
                    
                    if (!reactFound && window.parent === window) {
                        addResult('❌ React not found in any accessible context', 'error');
                    }
                } catch (e) {
                    addResult('Error during extended tests: ' + e.message, 'error');
                }
            }, 2000);
        }
        
        // Run tests when page loads
        window.addEventListener('load', runTests);
        
        // Also run tests after a delay to catch late-loading scripts
        setTimeout(runTests, 3000);
    </script>
</body>
</html>
