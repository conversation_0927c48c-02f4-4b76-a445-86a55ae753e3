import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Tabs, message, Spin } from 'antd';
import {
  DownloadOutlined,
  CopyOutlined,
  EyeOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

const { Text } = Typography;
const { TabPane } = Tabs;

/**
 * Export Preview
 * 
 * Shows a preview of the generated code with syntax highlighting
 * and provides options to copy or download the code.
 */

const ExportPreview = ({
  components = [],
  format = 'react',
  settings = {},
  onExport,
  onDownload,
  loading = false
}) => {
  const [generatedCode, setGeneratedCode] = useState('');
  const [activeFile, setActiveFile] = useState('main');
  const [files, setFiles] = useState({});
  const [generating, setGenerating] = useState(false);

  // Generate code when components or settings change
  useEffect(() => {
    if (components.length > 0) {
      generateCode();
    }
  }, [components, format, settings]);

  const generateCode = async () => {
    setGenerating(true);
    try {
      // Mock code generation - in real app this would call the export service
      const mockCode = generateMockCode(components, format, settings);
      setGeneratedCode(mockCode.main);
      setFiles(mockCode.files);
    } catch (error) {
      message.error('Failed to generate code');
    } finally {
      setGenerating(false);
    }
  };

  const generateMockCode = (components, format, settings) => {
    const { includeStyles, typescript, includeTests } = settings;
    const ext = typescript ? 'tsx' : 'jsx';

    let mainCode = '';
    let files = {};

    switch (format) {
      case 'react':
        mainCode = generateReactCode(components, settings);
        files = {
          main: mainCode,
          [`App.${ext}`]: mainCode,
          ...(includeStyles && { 'App.css': generateCSSCode(components) }),
          ...(includeTests && { [`App.test.${ext}`]: generateTestCode(components) })
        };
        break;

      case 'vue':
        mainCode = generateVueCode(components, settings);
        files = {
          main: mainCode,
          'App.vue': mainCode,
          ...(includeTests && { 'App.spec.js': generateVueTestCode(components) })
        };
        break;

      case 'html':
        mainCode = generateHTMLCode(components, settings);
        files = {
          main: mainCode,
          'index.html': mainCode,
          'styles.css': generateCSSCode(components),
          'script.js': generateJSCode(components)
        };
        break;

      default:
        mainCode = `// ${format} export coming soon!\n// Components: ${components.length}`;
        files = { main: mainCode };
    }

    return { main: mainCode, files };
  };

  const generateReactCode = (components, settings) => {
    const { typescript } = settings;
    const imports = typescript
      ? "import React from 'react';\nimport './App.css';\n\n"
      : "import React from 'react';\nimport './App.css';\n\n";

    const componentCode = components.map(comp => {
      return `  <${comp.type}${comp.props ? ` ${Object.entries(comp.props).map(([key, value]) =>
        `${key}="${value}"`).join(' ')}` : ''} />`;
    }).join('\n');

    return `${imports}function App() {
  return (
    <div className="app">
${componentCode}
    </div>
  );
}

export default App;`;
  };

  const generateVueCode = (components, settings) => {
    const componentCode = components.map(comp => {
      return `    <${comp.type}${comp.props ? ` ${Object.entries(comp.props).map(([key, value]) =>
        `:${key}="${value}"`).join(' ')}` : ''} />`;
    }).join('\n');

    return `<template>
  <div class="app">
${componentCode}
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      // Component data
    };
  }
};
</script>

<style scoped>
.app {
  padding: 20px;
}
</style>`;
  };

  const generateHTMLCode = (components, settings) => {
    const componentCode = components.map(comp => {
      return `    <div class="${comp.type.toLowerCase()}">${comp.props?.title || comp.type}</div>`;
    }).join('\n');

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
${componentCode}
    </div>
    <script src="script.js"></script>
</body>
</html>`;
  };

  const generateCSSCode = (components) => {
    return `.app {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

${components.map(comp => `.${comp.type.toLowerCase()} {
  margin: 10px 0;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}`).join('\n\n')}`;
  };

  const generateTestCode = (components) => {
    return `import { render, screen } from '@testing-library/react';
import App from './App';

test('renders app components', () => {
  render(<App />);
  
  // Test that components are rendered
${components.map(comp => `  expect(screen.getByText('${comp.type}')).toBeInTheDocument();`).join('\n')}
});`;
  };

  const generateVueTestCode = (components) => {
    return `import { mount } from '@vue/test-utils';
import App from './App.vue';

describe('App', () => {
  it('renders components', () => {
    const wrapper = mount(App);
    expect(wrapper.exists()).toBe(true);
  });
});`;
  };

  const generateJSCode = (components) => {
    return `// Generated JavaScript
document.addEventListener('DOMContentLoaded', function() {
  console.log('App loaded with ${components.length} components');
});`;
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode);
      message.success('Code copied to clipboard');
    } catch (error) {
      message.error('Failed to copy code');
    }
  };

  const handleDownload = async () => {
    try {
      if (onDownload) {
        await onDownload(format, settings);
      } else {
        // Create and download file
        const blob = new Blob([generatedCode], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `app.${format === 'react' ? 'jsx' : format === 'vue' ? 'vue' : 'html'}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
      message.success('Code downloaded successfully');
    } catch (error) {
      message.error('Failed to download code');
    }
  };

  const getLanguage = (format) => {
    switch (format) {
      case 'react': return 'jsx';
      case 'vue': return 'vue';
      case 'html': return 'html';
      case 'angular': return 'typescript';
      default: return 'javascript';
    }
  };

  if (generating || loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Generating {format} code...</Text>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space>
          <CodeOutlined />
          <Text strong>Generated {format.toUpperCase()} Code</Text>
          <Text type="secondary">({components.length} components)</Text>
        </Space>

        <Space>
          <Button
            icon={<CopyOutlined />}
            onClick={handleCopy}
          >
            Copy
          </Button>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleDownload}
          >
            Download
          </Button>
        </Space>
      </div>

      {Object.keys(files).length > 1 ? (
        <Tabs activeKey={activeFile} onChange={setActiveFile}>
          {Object.entries(files).map(([filename, code]) => (
            <TabPane tab={filename} key={filename}>
              <Card>
                <SyntaxHighlighter
                  language={getLanguage(format)}
                  style={tomorrow}
                  customStyle={{
                    margin: 0,
                    maxHeight: '400px',
                    fontSize: '13px'
                  }}
                >
                  {code}
                </SyntaxHighlighter>
              </Card>
            </TabPane>
          ))}
        </Tabs>
      ) : (
        <Card>
          <SyntaxHighlighter
            language={getLanguage(format)}
            style={tomorrow}
            customStyle={{
              margin: 0,
              maxHeight: '400px',
              fontSize: '13px'
            }}
          >
            {generatedCode}
          </SyntaxHighlighter>
        </Card>
      )}
    </div>
  );
};

export default ExportPreview;
