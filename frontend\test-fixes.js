/**
 * Test script to verify React loading fixes
 * This script tests the fixes for timing and consistency issues
 */

const puppeteer = require('puppeteer');

async function testReactFixes() {
  console.log('🔧 Testing React Loading Fixes...');
  console.log('=====================================');

  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false, // Show browser for visual verification
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture console output
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`[${msg.type()}] ${msg.text()}`);
    });

    // Capture errors
    const errors = [];
    page.on('error', error => {
      errors.push(error.message);
    });

    page.on('pageerror', error => {
      errors.push(error.message);
    });

    console.log('📱 Testing main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Test 1: Immediate React availability
    console.log('\n🧪 Test 1: Immediate React Availability');
    const immediateTest = await page.evaluate(() => {
      return {
        reactAvailable: typeof window.React !== 'undefined',
        reactDOMAvailable: typeof window.ReactDOM !== 'undefined',
        reactVersion: window.React?.version || null,
        globalsExposed: window.__REACT_GLOBALS_EXPOSED__ || false
      };
    });

    console.log(`   React Available: ${immediateTest.reactAvailable ? '✅' : '❌'}`);
    console.log(`   ReactDOM Available: ${immediateTest.reactDOMAvailable ? '✅' : '❌'}`);
    console.log(`   React Version: ${immediateTest.reactVersion || 'N/A'}`);
    console.log(`   Globals Exposed Flag: ${immediateTest.globalsExposed ? '✅' : '❌'}`);

    // Test 2: App loading states
    console.log('\n🧪 Test 2: App Loading States');
    await page.waitForFunction(
      () => window.__APP_LOADED__ === true,
      { timeout: 10000 }
    );

    const loadingTest = await page.evaluate(() => {
      return {
        appLoaded: window.__APP_LOADED__,
        appLoading: window.__APP_LOADING__,
        reactLoaded: window.__REACT_LOADED__
      };
    });

    console.log(`   App Loaded: ${loadingTest.appLoaded ? '✅' : '❌'}`);
    console.log(`   App Loading: ${loadingTest.appLoading ? '❌' : '✅'} (should be false)`);
    console.log(`   React Loaded: ${loadingTest.reactLoaded ? '✅' : '❌'}`);

    // Test 3: Root element content
    console.log('\n🧪 Test 3: Root Element Content');
    const rootTest = await page.evaluate(() => {
      const root = document.getElementById('root');
      return {
        rootExists: !!root,
        hasContent: root ? root.innerHTML.trim().length > 0 : false,
        contentLength: root ? root.innerHTML.length : 0
      };
    });

    console.log(`   Root Exists: ${rootTest.rootExists ? '✅' : '❌'}`);
    console.log(`   Has Content: ${rootTest.hasContent ? '✅' : '❌'}`);
    console.log(`   Content Length: ${rootTest.contentLength} characters`);

    // Test 4: CSS Bundle Loading
    console.log('\n🧪 Test 4: CSS Bundle Loading');
    const cssTest = await page.evaluate(() => {
      const cssLinks = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
      return {
        cssLinksCount: cssLinks.length,
        cssFiles: cssLinks.map(link => link.href.split('/').pop())
      };
    });

    console.log(`   CSS Links Found: ${cssTest.cssLinksCount} ${cssTest.cssLinksCount > 0 ? '✅' : '❌'}`);
    console.log(`   CSS Files: ${cssTest.cssFiles.join(', ')}`);

    // Test 5: Iframe consistency
    console.log('\n🧪 Test 5: Iframe Consistency Test');
    const iframeTest = await page.evaluate(() => {
      return new Promise((resolve) => {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = 'about:blank';
        document.body.appendChild(iframe);

        iframe.onload = () => {
          try {
            const iframeWindow = iframe.contentWindow;
            const result = {
              mainReact: typeof window.React !== 'undefined',
              mainReactDOM: typeof window.ReactDOM !== 'undefined',
              iframeReact: typeof iframeWindow.React !== 'undefined',
              iframeReactDOM: typeof iframeWindow.ReactDOM !== 'undefined'
            };
            
            document.body.removeChild(iframe);
            resolve(result);
          } catch (error) {
            document.body.removeChild(iframe);
            resolve({ error: error.message });
          }
        };
      });
    });

    if (iframeTest.error) {
      console.log(`   Iframe Test: ❌ Error - ${iframeTest.error}`);
    } else {
      console.log(`   Main React: ${iframeTest.mainReact ? '✅' : '❌'}`);
      console.log(`   Main ReactDOM: ${iframeTest.mainReactDOM ? '✅' : '❌'}`);
      console.log(`   Iframe React: ${iframeTest.iframeReact ? '✅' : '❌'}`);
      console.log(`   Iframe ReactDOM: ${iframeTest.iframeReactDOM ? '✅' : '❌'}`);
      
      const consistent = iframeTest.mainReact === iframeTest.iframeReact && 
                        iframeTest.mainReactDOM === iframeTest.iframeReactDOM;
      console.log(`   Consistency: ${consistent ? '✅' : '❌'}`);
    }

    // Test 6: Comprehensive diagnostic tool
    console.log('\n🧪 Test 6: Testing Comprehensive Diagnostic Tool');
    await page.goto('http://localhost:3000/comprehensive-react-diagnostics.html', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for diagnostics to complete
    await page.waitForTimeout(3000);

    const diagnosticResults = await page.evaluate(() => {
      const summary = document.getElementById('summary');
      return {
        summaryText: summary ? summary.textContent : 'No summary found',
        logEntries: window.diagnosticLog ? window.diagnosticLog.length : 0
      };
    });

    console.log(`   Diagnostic Summary: ${diagnosticResults.summaryText}`);
    console.log(`   Log Entries: ${diagnosticResults.logEntries}`);

    // Summary
    console.log('\n📊 Test Summary');
    console.log('================');
    
    const allTests = [
      immediateTest.reactAvailable && immediateTest.reactDOMAvailable,
      loadingTest.appLoaded && !loadingTest.appLoading,
      rootTest.rootExists && rootTest.hasContent,
      cssTest.cssLinksCount > 0,
      !iframeTest.error,
      diagnosticResults.logEntries > 0
    ];

    const passedTests = allTests.filter(test => test).length;
    const totalTests = allTests.length;

    console.log(`Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`Overall Status: ${passedTests === totalTests ? '✅ ALL TESTS PASSED' : '⚠️ SOME ISSUES REMAIN'}`);

    // Show console messages if there were any errors
    if (errors.length > 0) {
      console.log('\n❌ Errors Found:');
      errors.forEach(error => console.log(`   ${error}`));
    }

    // Show relevant console messages
    const relevantMessages = consoleMessages.filter(msg => 
      msg.includes('React') || msg.includes('App') || msg.includes('✅') || msg.includes('❌')
    );
    
    if (relevantMessages.length > 0) {
      console.log('\n📝 Relevant Console Messages:');
      relevantMessages.slice(0, 10).forEach(msg => console.log(`   ${msg}`));
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the tests
testReactFixes().catch(console.error);
