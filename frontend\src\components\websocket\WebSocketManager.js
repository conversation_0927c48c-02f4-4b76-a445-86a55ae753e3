import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Button,
  Typography,
  Space,
  Badge,
  Collapse,
  Table,
  Input,
  Form,
  Switch,
  Tooltip,
  Drawer,
  Divider,
  message,
  Tag,
  Spin
} from 'antd';
import {
  ApiOutlined,
  DisconnectOutlined,
  ReloadOutlined,
  SettingOutlined,
  SendOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import {
  wsConnect,
  wsDisconnect,
  wsSendMessage,
  wsClearMessages
} from '../../redux/actions/websocket.actions';
import { styled } from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;
const { TextArea } = Input;

// Styled components
const StatusBadge = styled(Badge)`
  .ant-badge-status-dot {
    width: 10px;
    height: 10px;
  }
`;

const MessageContainer = styled.div`
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: ${props => props.theme === 'dark' ? '#1f1f1f' : '#f5f5f5'};
`;

const MessageItem = styled.div`
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: ${props => props.type === 'sent'
    ? (props.theme === 'dark' ? '#177ddc' : '#e6f7ff')
    : (props.theme === 'dark' ? '#2b2b2b' : '#ffffff')};
  border-left: 4px solid ${props => props.type === 'sent'
    ? '#1890ff'
    : (props.status === 'error' ? '#ff4d4f' : props.status === 'warning' ? '#faad14' : '#52c41a')};
  color: ${props => props.theme === 'dark' ? '#ffffff' : '#000000'};
  word-break: break-word;
`;

const TimeStamp = styled(Text)`
  font-size: 12px;
  color: ${props => props.theme === 'dark' ? '#8c8c8c' : '#8c8c8c'};
  margin-left: 8px;
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 16px;
`;

/**
 * WebSocketManager component
 * 
 * This component provides a user interface for managing WebSocket connections
 * and viewing WebSocket messages.
 */
const WebSocketManager = ({
  visible = false,
  onClose = () => { },
  placement = 'right',
  width = 600
}) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [messageForm] = Form.useForm();
  const [autoScroll, setAutoScroll] = useState(true);
  const [filterText, setFilterText] = useState('');
  const [showSettings, setShowSettings] = useState(false);

  // Get WebSocket state from Redux store with error handling
  const connected = useSelector(state => state?.websocket?.connected || false);
  const connecting = useSelector(state => state?.websocket?.connecting || false);
  const error = useSelector(state => state?.websocket?.error || null);
  const messages = useSelector(state => state?.websocket?.messages || []);
  const url = useSelector(state => state?.websocket?.url || null);
  const reconnectAttempts = useSelector(state => state?.websocket?.reconnectAttempts || 0);
  const reconnectInterval = useSelector(state => state?.websocket?.reconnectInterval || 5000);

  // Get theme from Redux store
  const isDarkMode = useSelector(state => {
    const activeThemeId = state.themes?.activeTheme;
    const themes = state.themes?.themes || [];
    const activeTheme = themes.find(theme => theme.id === activeThemeId);
    return activeTheme?.isDark || false;
  });

  // Auto-scroll to bottom of message container
  useEffect(() => {
    if (autoScroll) {
      const container = document.getElementById('ws-message-container');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [messages, autoScroll]);

  // Connect to WebSocket
  const handleConnect = () => {
    form.validateFields().then(values => {
      const { url, reconnectAttempts, reconnectInterval, protocols } = values;

      dispatch(wsConnect(url, {
        reconnectAttempts,
        reconnectInterval,
        protocols: protocols ? protocols.split(',').map(p => p.trim()) : undefined
      }));
    });
  };

  // Disconnect from WebSocket
  const handleDisconnect = () => {
    dispatch(wsDisconnect());
  };

  // Send message
  const handleSendMessage = () => {
    messageForm.validateFields().then(values => {
      const { message } = values;

      try {
        // Try to parse as JSON
        const jsonMessage = JSON.parse(message);
        dispatch(wsSendMessage(jsonMessage));
      } catch (error) {
        // Send as plain text
        dispatch(wsSendMessage(message));
      }

      // Clear message input
      messageForm.resetFields();
    });
  };

  // Clear messages
  const handleClearMessages = () => {
    dispatch(wsClearMessages());
  };

  // Toggle settings drawer
  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };

  // Filter messages
  const filteredMessages = messages.filter(message => {
    if (!filterText) return true;

    const messageStr = typeof message.data === 'object'
      ? JSON.stringify(message.data)
      : String(message.data);

    return messageStr.toLowerCase().includes(filterText.toLowerCase());
  });

  // Format message for display
  const formatMessage = (message) => {
    if (typeof message === 'object') {
      try {
        return JSON.stringify(message, null, 2);
      } catch (error) {
        return String(message);
      }
    }

    return String(message);
  };

  // Get connection status
  const getConnectionStatus = () => {
    if (connected) {
      return {
        status: 'success',
        text: 'Connected',
        icon: <CheckCircleOutlined />
      };
    } else if (connecting) {
      return {
        status: 'processing',
        text: 'Connecting',
        icon: <Spin size="small" />
      };
    } else if (error) {
      return {
        status: 'error',
        text: 'Error',
        icon: <CloseCircleOutlined />
      };
    } else {
      return {
        status: 'default',
        text: 'Disconnected',
        icon: <DisconnectOutlined />
      };
    }
  };

  const connectionStatus = getConnectionStatus();

  // Initialize form with current values
  useEffect(() => {
    form.setFieldsValue({
      url: url || 'ws://localhost:8000/ws',
      reconnectAttempts: reconnectAttempts || 5,
      reconnectInterval: reconnectInterval || 3000,
      protocols: ''
    });
  }, [form, url, reconnectAttempts, reconnectInterval]);

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <ApiOutlined style={{ marginRight: 8 }} />
          <span>WebSocket Manager</span>
        </div>
      }
      placement={placement}
      width={width}
      onClose={onClose}
      open={visible}
      extra={
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={toggleSettings}
          />
        </Space>
      }
    >
      <ConnectionStatus>
        <StatusBadge status={connectionStatus.status} />
        <Text style={{ marginLeft: 8 }}>
          {connectionStatus.text}
        </Text>
        {error && (
          <Tooltip title={error.message}>
            <WarningOutlined style={{ marginLeft: 8, color: '#ff4d4f' }} />
          </Tooltip>
        )}
        <div style={{ marginLeft: 'auto' }}>
          {connected ? (
            <Button
              icon={<DisconnectOutlined />}
              onClick={handleDisconnect}
              danger
            >
              Disconnect
            </Button>
          ) : (
            <Button
              icon={<ApiOutlined />}
              onClick={handleConnect}
              type="primary"
              loading={connecting}
            >
              Connect
            </Button>
          )}
        </div>
      </ConnectionStatus>

      <Collapse defaultActiveKey={['1', '2']}>
        <Panel
          header="Connection Settings"
          key="1"
          extra={
            <Button
              icon={<ReloadOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                form.resetFields();
              }}
            />
          }
        >
          <Form
            form={form}
            layout="vertical"
          >
            <Form.Item
              name="url"
              label="WebSocket URL"
              rules={[{ required: true, message: 'Please enter WebSocket URL' }]}
            >
              <Input
                placeholder="ws://localhost:8000/ws"
                disabled={connected || connecting}
              />
            </Form.Item>

            <Form.Item
              name="protocols"
              label="Protocols (comma-separated)"
            >
              <Input
                placeholder="protocol1, protocol2"
                disabled={connected || connecting}
              />
            </Form.Item>

            <Form.Item
              name="reconnectAttempts"
              label="Reconnect Attempts"
              rules={[{ required: true, message: 'Please enter reconnect attempts' }]}
            >
              <Input
                type="number"
                min={0}
                max={10}
                disabled={connected || connecting}
              />
            </Form.Item>

            <Form.Item
              name="reconnectInterval"
              label="Reconnect Interval (ms)"
              rules={[{ required: true, message: 'Please enter reconnect interval' }]}
            >
              <Input
                type="number"
                min={1000}
                max={10000}
                disabled={connected || connecting}
              />
            </Form.Item>
          </Form>
        </Panel>

        <Panel
          header="Messages"
          key="2"
          extra={
            <Space>
              <Tooltip title="Auto-scroll to bottom">
                <Switch
                  checked={autoScroll}
                  onChange={setAutoScroll}
                  size="small"
                />
              </Tooltip>
              <Button
                icon={<ClearOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearMessages();
                }}
              />
            </Space>
          }
        >
          <Input
            placeholder="Filter messages"
            value={filterText}
            onChange={(e) => setFilterText(e.target.value)}
            allowClear
            style={{ marginBottom: 16 }}
            prefix={<InfoCircleOutlined />}
          />

          <MessageContainer
            id="ws-message-container"
            theme={isDarkMode ? 'dark' : 'light'}
          >
            {filteredMessages.length === 0 ? (
              <Text type="secondary">No messages</Text>
            ) : (
              filteredMessages.map((message, index) => (
                <MessageItem
                  key={index}
                  type={message.type}
                  status={message.status}
                  theme={isDarkMode ? 'dark' : 'light'}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                    <Tag color={message.type === 'sent' ? 'blue' : 'green'}>
                      {message.type === 'sent' ? 'Sent' : 'Received'}
                    </Tag>
                    <TimeStamp theme={isDarkMode ? 'dark' : 'light'}>
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </TimeStamp>
                  </div>
                  <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                    {formatMessage(message.data)}
                  </pre>
                </MessageItem>
              ))
            )}
          </MessageContainer>

          <Form
            form={messageForm}
            layout="inline"
            style={{ display: 'flex', marginTop: 16 }}
          >
            <Form.Item
              name="message"
              style={{ flex: 1, marginRight: 8 }}
              rules={[{ required: true, message: 'Please enter a message' }]}
            >
              <TextArea
                placeholder="Enter message (plain text or JSON)"
                autoSize={{ minRows: 1, maxRows: 6 }}
                disabled={!connected}
              />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                disabled={!connected}
              >
                Send
              </Button>
            </Form.Item>
          </Form>
        </Panel>
      </Collapse>

      <Drawer
        title="Advanced Settings"
        placement="right"
        closable={true}
        onClose={toggleSettings}
        open={showSettings}
        width={400}
      >
        <Paragraph>
          These settings allow you to configure the WebSocket connection behavior.
        </Paragraph>

        <Divider />

        <Form
          layout="vertical"
        >
          <Form.Item
            label="Ping Interval (ms)"
            name="pingInterval"
            initialValue={30000}
          >
            <Input type="number" min={1000} max={60000} />
          </Form.Item>

          <Form.Item
            label="Ping Message"
            name="pingMessage"
            initialValue='{"type":"ping"}'
          >
            <Input />
          </Form.Item>

          <Form.Item
            label="Auto Reconnect"
            name="autoReconnect"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="Debug Mode"
            name="debugMode"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Drawer>
    </Drawer>
  );
};

export default WebSocketManager;
