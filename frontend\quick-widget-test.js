/**
 * Quick test to check diagnostic widget
 */

const puppeteer = require('puppeteer');

async function quickWidgetTest() {
  console.log('🔍 Quick Widget Test...');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture all console messages
    page.on('console', msg => {
      console.log(`[BROWSER] ${msg.text()}`);
    });

    // Navigate to main app
    console.log('📱 Loading main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for app to load
    await page.waitForTimeout(5000);

    // Check environment and widget status
    const status = await page.evaluate(() => {
      return {
        hostname: window.location.hostname,
        widgetExists: !!document.getElementById('react-diagnostic-widget'),
        reactLoaded: typeof window.React !== 'undefined',
        appLoaded: window.__APP_LOADED__,
        bodyExists: !!document.body,
        bodyChildren: document.body ? document.body.children.length : 0
      };
    });

    console.log('Status:', status);

    // Try to manually add widget for testing
    console.log('🔧 Manually testing widget creation...');
    await page.evaluate(() => {
      if (!document.getElementById('test-widget')) {
        const testWidget = document.createElement('div');
        testWidget.id = 'test-widget';
        testWidget.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: red;
          color: white;
          padding: 10px;
          z-index: 10000;
        `;
        testWidget.textContent = 'TEST WIDGET';
        document.body.appendChild(testWidget);
        console.log('Test widget added');
      }
    });

    await page.waitForTimeout(2000);

    const finalStatus = await page.evaluate(() => {
      return {
        diagnosticWidget: !!document.getElementById('react-diagnostic-widget'),
        testWidget: !!document.getElementById('test-widget')
      };
    });

    console.log('Final Status:', finalStatus);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

quickWidgetTest().catch(console.error);
