/**
 * Template System Integration Tests
 * 
 * Tests the complete template system functionality including:
 * - Template loading and saving
 * - API integration
 * - Default template availability
 */

import { templateService } from '../../services/api';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('Template System Integration', () => {
  beforeEach(() => {
    fetch.mockClear();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Template Service API', () => {
    test('should fetch templates successfully', async () => {
      const mockTemplates = [
        {
          id: 1,
          name: '[Default] Hello World Starter',
          description: 'A simple starter template',
          app_category: 'other',
          is_public: true
        }
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockTemplates
      });

      const result = await templateService.getTemplates();

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/templates/',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );

      expect(result.data).toEqual(mockTemplates);
      expect(result.status).toBe(200);
    });

    test('should handle API errors gracefully', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Not found' })
      });

      await expect(templateService.getTemplates()).rejects.toThrow('HTTP error! status: 404');
    });

    test('should load a specific template', async () => {
      const mockTemplate = {
        id: 1,
        name: '[Default] Hello World Starter',
        components: {
          pages: [{
            name: 'home',
            components: [
              {
                id: 'header-1',
                type: 'header',
                props: { title: 'Hello World!' }
              }
            ]
          }]
        }
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockTemplate
      });

      const result = await templateService.getTemplate(1);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/templates/1/',
        expect.any(Object)
      );

      expect(result.data).toEqual(mockTemplate);
    });

    test('should save a new template', async () => {
      const newTemplate = {
        name: 'My Custom Template',
        description: 'A custom template',
        components: {
          pages: [{
            name: 'home',
            components: []
          }]
        }
      };

      const mockResponse = { id: 2, ...newTemplate };

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockResponse
      });

      const result = await templateService.createTemplate(newTemplate);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/templates/',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(newTemplate),
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );

      expect(result.data).toEqual(mockResponse);
    });
  });

  describe('Template Structure Validation', () => {
    test('should validate Hello World template structure', () => {
      const helloWorldTemplate = {
        name: '[Default] Hello World Starter',
        description: 'A simple starter template',
        app_category: 'other',
        components: {
          pages: [
            {
              name: 'home',
              title: 'Home',
              components: [
                {
                  id: 'header-1',
                  type: 'header',
                  props: {
                    title: 'Hello World!',
                    subtitle: 'Welcome to your new app built with App Builder',
                    backgroundColor: '#1890ff',
                    textColor: '#ffffff'
                  }
                },
                {
                  id: 'button-1',
                  type: 'button',
                  props: {
                    text: 'Get Started',
                    backgroundColor: '#52c41a',
                    color: '#ffffff'
                  }
                }
              ]
            }
          ],
          theme: {
            primaryColor: '#1890ff',
            secondaryColor: '#52c41a',
            backgroundColor: '#ffffff',
            textColor: '#333333'
          }
        },
        required_components: ['header', 'container', 'text', 'button', 'footer'],
        is_public: true
      };

      // Validate template structure
      expect(helloWorldTemplate.name).toBeDefined();
      expect(helloWorldTemplate.description).toBeDefined();
      expect(helloWorldTemplate.components).toBeDefined();
      expect(helloWorldTemplate.components.pages).toBeInstanceOf(Array);
      expect(helloWorldTemplate.components.pages.length).toBeGreaterThan(0);

      // Validate page structure
      const homePage = helloWorldTemplate.components.pages[0];
      expect(homePage.name).toBe('home');
      expect(homePage.components).toBeInstanceOf(Array);
      expect(homePage.components.length).toBeGreaterThan(0);

      // Validate component structure
      const headerComponent = homePage.components[0];
      expect(headerComponent.id).toBeDefined();
      expect(headerComponent.type).toBe('header');
      expect(headerComponent.props).toBeDefined();
      expect(headerComponent.props.title).toBe('Hello World!');

      // Validate theme structure
      expect(helloWorldTemplate.components.theme).toBeDefined();
      expect(helloWorldTemplate.components.theme.primaryColor).toBeDefined();
      expect(helloWorldTemplate.components.theme.secondaryColor).toBeDefined();

      // Validate required components
      expect(helloWorldTemplate.required_components).toBeInstanceOf(Array);
      expect(helloWorldTemplate.required_components).toContain('header');
      expect(helloWorldTemplate.required_components).toContain('button');
    });

    test('should validate component properties', () => {
      const validComponent = {
        id: 'test-component-1',
        type: 'button',
        props: {
          text: 'Click me',
          backgroundColor: '#1890ff',
          color: '#ffffff',
          padding: '12px 24px',
          borderRadius: '6px'
        }
      };

      // Validate component structure
      expect(validComponent.id).toMatch(/^[a-zA-Z0-9-_]+$/);
      expect(validComponent.type).toBeDefined();
      expect(validComponent.props).toBeDefined();

      // Validate button-specific properties
      expect(validComponent.props.text).toBeDefined();
      expect(validComponent.props.backgroundColor).toMatch(/^#[0-9a-fA-F]{6}$/);
      expect(validComponent.props.color).toMatch(/^#[0-9a-fA-F]{6}$/);
    });
  });

  describe('Template Loading and Application', () => {
    test('should simulate template loading process', async () => {
      const mockTemplate = {
        id: 1,
        name: '[Default] Hello World Starter',
        components: {
          pages: [{
            name: 'home',
            components: [
              {
                id: 'header-1',
                type: 'header',
                props: { title: 'Hello World!' }
              },
              {
                id: 'button-1',
                type: 'button',
                props: { text: 'Get Started' }
              }
            ]
          }]
        }
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ success: true })
      });

      // Simulate loading template
      const result = await templateService.loadTemplate(1);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/templates/1/load/',
        expect.objectContaining({
          method: 'POST'
        })
      );

      expect(result.data.success).toBe(true);
    });

    test('should handle template loading errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Internal server error' })
      });

      await expect(templateService.loadTemplate(999)).rejects.toThrow('HTTP error! status: 500');
    });
  });

  describe('Template Export and Code Generation', () => {
    test('should validate generated code structure', () => {
      const mockGeneratedCode = {
        format: 'react',
        code: `import React from 'react';

function App() {
  return (
    <div className="app">
      <header style={{
        backgroundColor: '#1890ff',
        color: '#ffffff',
        textAlign: 'center',
        padding: '40px 20px'
      }}>
        <h1>Hello World!</h1>
        <p>Welcome to your new app built with App Builder</p>
      </header>
      
      <div style={{
        padding: '40px 20px',
        textAlign: 'center'
      }}>
        <button style={{
          backgroundColor: '#52c41a',
          color: '#ffffff',
          padding: '12px 24px',
          borderRadius: '6px',
          border: 'none',
          cursor: 'pointer'
        }}>
          Get Started
        </button>
      </div>
    </div>
  );
}

export default App;`,
        files: {
          'App.jsx': 'import React from "react";...',
          'package.json': '{"name": "my-app", "version": "1.0.0"}'
        }
      };

      // Validate code structure
      expect(mockGeneratedCode.format).toBe('react');
      expect(mockGeneratedCode.code).toContain('import React');
      expect(mockGeneratedCode.code).toContain('function App()');
      expect(mockGeneratedCode.code).toContain('export default App');
      expect(mockGeneratedCode.code).toContain('Hello World!');
      expect(mockGeneratedCode.code).toContain('Get Started');

      // Validate files structure
      expect(mockGeneratedCode.files).toBeDefined();
      expect(mockGeneratedCode.files['App.jsx']).toBeDefined();
      expect(mockGeneratedCode.files['package.json']).toBeDefined();
    });

    test('should validate code syntax', () => {
      const reactCode = `import React from 'react';

function App() {
  return (
    <div className="app">
      <h1>Hello World!</h1>
    </div>
  );
}

export default App;`;

      // Basic syntax validation
      expect(reactCode).toContain('import React');
      expect(reactCode).toContain('function App()');
      expect(reactCode).toContain('return (');
      expect(reactCode).toContain('export default');

      // Check for balanced brackets
      const openBrackets = (reactCode.match(/\(/g) || []).length;
      const closeBrackets = (reactCode.match(/\)/g) || []).length;
      expect(openBrackets).toBe(closeBrackets);

      // Check for balanced JSX tags
      const openTags = (reactCode.match(/<[^/][^>]*>/g) || []).length;
      const closeTags = (reactCode.match(/<\/[^>]*>/g) || []).length;
      expect(openTags).toBe(closeTags);
    });
  });

  describe('Template System Performance', () => {
    test('should handle multiple template requests efficiently', async () => {
      const mockTemplates = Array.from({ length: 10 }, (_, i) => ({
        id: i + 1,
        name: `Template ${i + 1}`,
        description: `Description ${i + 1}`
      }));

      fetch.mockResolvedValue({
        ok: true,
        status: 200,
        json: async () => mockTemplates
      });

      const startTime = Date.now();
      
      // Simulate multiple concurrent requests
      const promises = Array.from({ length: 5 }, () => templateService.getTemplates());
      const results = await Promise.all(promises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all requests completed
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result.data).toEqual(mockTemplates);
      });

      // Performance check (should complete within reasonable time)
      expect(duration).toBeLessThan(1000); // 1 second
    });
  });
});
