# Feature Buttons Troubleshooting Guide

## Issue: Missing Feature Buttons in App Builder Interface

The Testing Tools, Performance Monitor, Data Management, and Enhanced Export buttons are not visible in the App Builder interface header.

## ✅ Fixed Issues

1. **Duplicate Variable Declaration**: Fixed `TestingTools` being declared twice in `IntegratedAppBuilder.js`
2. **Syntax Error**: Fixed syntax error in `TemplateSystemDemo.js` with duplicate export statements
3. **Compilation Errors**: Application now compiles successfully

## 🔍 Diagnostic Steps

### Step 1: Verify Application is Running
1. Navigate to `http://localhost:3000`
2. Click "Try Enhanced Builder" button
3. Or directly navigate to `http://localhost:3000/app-builder`

### Step 2: Check Browser Console
1. Open browser Developer Tools (F12)
2. Check Console tab for any JavaScript errors
3. Look for component import errors or React rendering errors

### Step 3: Run Debug Script
1. Open browser console on the app-builder page
2. Run: `debugFeatureButtons()` (script is auto-loaded)
3. Review the output for missing buttons and configuration issues

### Step 4: Verify Feature Configuration
Check that `enableFeatures` in `App.js` includes all features:
```javascript
enableFeatures={{
  websocket: true,
  tutorial: true,
  aiSuggestions: true,
  templates: true,
  codeExport: true,
  collaboration: true,
  testing: true,              // ✅ Should be true
  dataManagement: true,       // ✅ Should be true
  performanceMonitoring: true, // ✅ Should be true
  enhancedExport: true,       // ✅ Should be true
  tutorialAssistant: true,
}}
```

### Step 5: Check Component Imports
Verify these components exist and are properly imported:
- `frontend/src/components/testing/TestingTools.js`
- `frontend/src/components/data/DataManagementTools.js`
- `frontend/src/components/performance/PerformanceTools.js`
- `frontend/src/components/export/EnhancedCodeExporter.js`

### Step 6: Inspect DOM Elements
1. Right-click in the header area where buttons should appear
2. Select "Inspect Element"
3. Look for buttons with these data attributes:
   - `data-tutorial="testing-tools"`
   - `data-tutorial="performance-monitor"`
   - `data-tutorial="data-management"`
   - `data-tutorial="code-export"`

## 🔧 Manual Testing

### Test Button Visibility
Run these commands in browser console:
```javascript
// Check if buttons exist in DOM
document.querySelector('button[data-tutorial="testing-tools"]')
document.querySelector('button[data-tutorial="performance-monitor"]')
document.querySelector('button[data-tutorial="data-management"]')
document.querySelector('button[data-tutorial="code-export"]')

// Test button clicks
document.querySelector('button[data-tutorial="testing-tools"]')?.click()
```

### Check CSS Styling
```javascript
// Check if buttons are hidden by CSS
const button = document.querySelector('button[data-tutorial="testing-tools"]');
if (button) {
  const styles = window.getComputedStyle(button);
  console.log('Display:', styles.display);
  console.log('Visibility:', styles.visibility);
  console.log('Opacity:', styles.opacity);
  console.log('Z-index:', styles.zIndex);
}
```

## 🚨 Common Issues & Solutions

### Issue 1: Buttons Not Rendering
**Cause**: Component import errors or enableFeatures configuration
**Solution**: 
1. Check browser console for import errors
2. Verify enableFeatures prop in App.js
3. Ensure component files exist in correct directories

### Issue 2: Buttons Hidden by CSS
**Cause**: CSS styling issues (display: none, visibility: hidden, etc.)
**Solution**:
1. Inspect element styles in browser DevTools
2. Check for conflicting CSS rules
3. Verify z-index and positioning

### Issue 3: React Component Errors
**Cause**: Component rendering errors or prop validation issues
**Solution**:
1. Check React DevTools for component errors
2. Verify component props are correctly passed
3. Check for missing dependencies in component imports

### Issue 4: Feature Components Not Loading
**Cause**: Missing component files or import path errors
**Solution**:
1. Verify component files exist in expected directories
2. Check import paths in IntegratedAppBuilder.js
3. Ensure fallback components are working

## 📍 Expected Button Locations

The feature buttons should appear in the header section of the IntegratedAppBuilder component, specifically in the `FeatureToggles` styled component container.

**Expected buttons:**
1. 🧪 Testing (orange gradient)
2. ⚡ Performance (purple gradient)
3. 📊 Data (teal gradient)
4. 📤 Export (pink gradient)

## 🔄 Next Steps

1. **Immediate**: Run the debug script to identify specific issues
2. **If buttons still missing**: Check enableFeatures configuration
3. **If buttons present but not working**: Test modal opening functionality
4. **If styling issues**: Inspect CSS and responsive design
5. **If component errors**: Check individual feature component implementations

## 📞 Additional Help

If issues persist:
1. Check the browser's Network tab for failed resource loads
2. Verify all npm dependencies are installed
3. Try clearing browser cache and reloading
4. Check if the issue occurs in different browsers
5. Review the webpack compilation output for warnings/errors
