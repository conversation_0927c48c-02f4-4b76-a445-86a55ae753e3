// Script to temporarily switch to minimal React app for testing
const fs = require('fs');
const path = require('path');

const indexPath = path.join(__dirname, 'src', 'index.js');
const backupPath = path.join(__dirname, 'src', 'index.backup.js');
const minimalPath = path.join(__dirname, 'src', 'index.minimal.js');

try {
  // Backup current index.js if backup doesn't exist
  if (!fs.existsSync(backupPath)) {
    console.log('📦 Backing up current index.js...');
    fs.copyFileSync(indexPath, backupPath);
    console.log('✅ Backup created at index.backup.js');
  }

  // Copy minimal version to index.js
  console.log('🔄 Switching to minimal React app...');
  fs.copyFileSync(minimalPath, indexPath);
  console.log('✅ Switched to minimal React app');
  
  console.log('\n📋 Next steps:');
  console.log('1. The webpack dev server should automatically reload');
  console.log('2. Check http://localhost:3000 to see if React renders');
  console.log('3. Run "node restore-from-minimal.js" to restore the original app');
  
} catch (error) {
  console.error('❌ Error switching to minimal app:', error.message);
  process.exit(1);
}
