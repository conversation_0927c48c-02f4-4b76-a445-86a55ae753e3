# App Builder Enhanced - Integration Guide

## Overview

The App Builder Enhanced provides a comprehensive, integrated development environment that combines four core features:

1. **Component Builder** - Create and manage UI components
2. **Layout Designer** - Design responsive layouts and arrangements
3. **Theme Manager** - Customize colors, typography, and styling
4. **WebSocket Manager** - Enable real-time collaboration

## Accessing the App Builder

### Routes Available:
- **`/app-builder`** - Main integrated App Builder (recommended)
- **`/app-builder-advanced`** - Advanced IntegratedAppBuilder component
- **`/app-builder-enhanced`** - Enhanced tabbed interface

## Core Features Integration

### 1. Component Builder
**Location**: Component Builder tab or integrated grid view

**Features**:
- **Component Palette**: Pre-built components (buttons, text, inputs, cards)
- **Drag & Drop**: Visual component placement
- **Property Editor**: Real-time property modification
- **Component Library**: Reusable component templates

**Usage**:
```javascript
// Adding a component programmatically
const newComponent = {
  id: 'btn1',
  type: 'button',
  props: { text: 'Click Me', type: 'primary' },
  position: { x: 100, y: 50 }
};
```

### 2. Layout Designer
**Location**: Layout Designer tab or integrated grid view

**Features**:
- **Grid Layouts**: Responsive grid systems
- **Flex Layouts**: Flexible box layouts
- **Responsive Design**: Breakpoint management
- **Layout Templates**: Pre-designed layout patterns

**Usage**:
```javascript
// Applying a grid layout
const gridLayout = {
  type: 'grid',
  columns: 3,
  gap: '16px',
  responsive: true,
  breakpoints: {
    mobile: { columns: 1 },
    tablet: { columns: 2 },
    desktop: { columns: 3 }
  }
};
```

### 3. Theme Manager
**Location**: Theme Manager tab or integrated grid view

**Features**:
- **Color Schemes**: Primary, secondary, accent colors
- **Typography**: Font families, sizes, weights
- **Spacing**: Margins, padding, gaps
- **Border Radius**: Corner styling
- **Shadows**: Elevation and depth

**Usage**:
```javascript
// Applying a custom theme
const customTheme = {
  primaryColor: '#52c41a',
  fontFamily: 'Inter, sans-serif',
  borderRadius: '8px',
  spacing: {
    small: '8px',
    medium: '16px',
    large: '24px'
  }
};
```

### 4. WebSocket Manager
**Location**: WebSocket Manager tab or integrated grid view

**Features**:
- **Real-time Collaboration**: Multi-user editing
- **Live Cursors**: See other users' actions
- **Change Synchronization**: Instant updates
- **Connection Management**: Robust reconnection

**Usage**:
```javascript
// WebSocket connection status
const websocketStatus = {
  connected: true,
  collaborators: ['user1', 'user2'],
  lastSync: new Date()
};
```

## Step-by-Step Sample App Creation

### Step 1: Access the App Builder
1. Navigate to `http://localhost:3000/app-builder`
2. You'll see the integrated view with all four features

### Step 2: Create Components
1. **Go to Component Builder section**
2. **Add a Button**:
   - Click "Add Button" 
   - Set text to "Get Started"
   - Set type to "primary"
3. **Add Text**:
   - Click "Add Text"
   - Set text to "Welcome to App Builder"
   - Set size to "large"
4. **Add a Card**:
   - Click "Add Card"
   - Set title to "Feature Card"
   - Set content to "This showcases our features"
5. **Add an Input**:
   - Click "Add Input"
   - Set placeholder to "Enter your name"

### Step 3: Design Layout
1. **Go to Layout Designer section**
2. **Select Grid Layout**:
   - Choose 2-column grid
   - Set gap to "16px"
   - Enable responsive design
3. **Arrange Components**:
   - Drag components to desired positions
   - Adjust grid areas as needed

### Step 4: Apply Theme
1. **Go to Theme Manager section**
2. **Set Primary Color**: Choose green (#52c41a)
3. **Select Typography**: Choose "Inter" font family
4. **Set Border Radius**: 8px for modern look
5. **Configure Spacing**: 16px standard spacing

### Step 5: Enable Collaboration
1. **Go to WebSocket Manager section**
2. **Connect to WebSocket**: Click "Connect"
3. **Invite Collaborators**: Share project link
4. **Monitor Real-time Changes**: See live updates

### Step 6: Preview and Test
1. **Click "Build Sample App"** in the integrated view
2. **Review the Preview**: See all features working together
3. **Test Responsiveness**: Resize browser window
4. **Verify Real-time Updates**: Make changes and see instant sync

## Sample App Demonstration

### Automated Sample Creation
The integrated App Builder includes a "Build Sample App" feature that demonstrates the complete workflow:

```javascript
// Sample app configuration
const sampleApp = {
  components: [
    { id: 'btn1', type: 'button', props: { text: 'Get Started', type: 'primary' } },
    { id: 'txt1', type: 'text', props: { text: 'Welcome to App Builder', size: 'large' } },
    { id: 'card1', type: 'card', props: { title: 'Feature Card', content: 'Sample content' } },
    { id: 'input1', type: 'input', props: { placeholder: 'Enter your name' } }
  ],
  layout: {
    type: 'grid',
    columns: 2,
    gap: '16px',
    responsive: true
  },
  theme: {
    primaryColor: '#52c41a',
    fontFamily: 'Inter, sans-serif',
    borderRadius: '8px'
  },
  collaboration: {
    enabled: true,
    realTimeSync: true
  }
};
```

## Integration Benefits

### 1. Unified Workflow
- All features accessible from single interface
- Seamless transitions between design phases
- Consistent data flow between components

### 2. Real-time Collaboration
- Multiple users can work simultaneously
- Changes sync instantly across all features
- Live cursor tracking and user presence

### 3. Responsive Design
- Layouts automatically adapt to screen sizes
- Components maintain functionality across devices
- Theme applies consistently at all breakpoints

### 4. Data Persistence
- Projects save automatically
- State maintained across sessions
- Export capabilities for production use

## Troubleshooting

### Common Issues:

1. **Components Not Loading**:
   - Check browser console for errors
   - Verify all dependencies are installed
   - Try refreshing the page

2. **WebSocket Connection Failed**:
   - Check network connectivity
   - Verify WebSocket server is running
   - Try reconnecting manually

3. **Layout Not Responsive**:
   - Verify responsive settings are enabled
   - Check breakpoint configurations
   - Test in different screen sizes

4. **Theme Not Applying**:
   - Ensure theme is saved
   - Check CSS specificity issues
   - Verify theme format is correct

## Advanced Usage

### Custom Component Creation
```javascript
// Register custom component
const customComponent = {
  type: 'custom-widget',
  props: { title: 'My Widget', data: [] },
  render: (props) => <CustomWidget {...props} />
};
```

### Layout Templates
```javascript
// Create layout template
const dashboardLayout = {
  name: 'Dashboard',
  type: 'grid',
  areas: [
    'header header header',
    'sidebar main main',
    'footer footer footer'
  ]
};
```

### Theme Presets
```javascript
// Define theme preset
const darkTheme = {
  name: 'Dark Mode',
  primaryColor: '#1890ff',
  backgroundColor: '#141414',
  textColor: '#ffffff'
};
```

## Conclusion

The App Builder Enhanced provides a complete, integrated development environment that streamlines the entire application creation process. By combining component building, layout design, theme management, and real-time collaboration in a single interface, developers can create sophisticated applications efficiently and collaboratively.
