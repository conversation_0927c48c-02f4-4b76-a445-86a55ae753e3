services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: app-builder-backend-prod
    restart: unless-stopped
    env_file:
      - ./backend/.env.production
    volumes:
      - static_volume:/usr/src/app/staticfiles
      - media_volume:/usr/src/app/media
    depends_on:
      - db
      - redis
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8000/health/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: app-builder-frontend-prod
    restart: unless-stopped
    env_file:
      - ./frontend/.env.production
    depends_on:
      - backend
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: app-builder-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - static_volume:/usr/src/app/staticfiles:ro
      - media_volume:/usr/src/app/media:ro
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    container_name: app-builder-db-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: app_builder_201_prod
      POSTGRES_USER: app_builder_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - app-network
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U app_builder_user -d app_builder_201_prod" ]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: app-builder-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - app-network
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 10s
      retries: 3

  certbot:
    image: certbot/certbot
    container_name: app-builder-certbot
    volumes:
      - ./nginx/ssl:/etc/letsencrypt
      - ./nginx/certbot-webroot:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d your-domain.com -d www.your-domain.com

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local

networks:
  app-network:
    driver: bridge
