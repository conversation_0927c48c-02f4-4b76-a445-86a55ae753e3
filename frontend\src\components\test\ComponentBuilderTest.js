import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Alert } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * ComponentBuilderTest - A test component to verify component builder functionality
 */
const ComponentBuilderTest = () => {
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    const results = {};

    try {
      // Test 1: Check if IntegratedAppBuilder is available
      results.integratedBuilderAvailable = document.body.textContent.includes('Enhanced App Builder') ||
                                          document.body.textContent.includes('App Builder');

      // Test 2: Check for component palette
      results.componentPaletteExists = document.body.textContent.includes('Components') ||
                                      document.querySelectorAll('button').length > 0;

      // Test 3: Check for canvas/preview area
      results.canvasAreaExists = document.body.textContent.includes('Canvas') ||
                                document.body.textContent.includes('Preview') ||
                                document.body.textContent.includes('Drag');

      // Test 4: Check for property editor
      results.propertyEditorExists = document.body.textContent.includes('Properties') ||
                                    document.body.textContent.includes('Select a component');

      // Test 5: Check for interactive buttons
      const buttons = document.querySelectorAll('button');
      results.hasInteractiveElements = buttons.length > 0;

      // Test 6: Check for error messages
      const errorElements = document.querySelectorAll('[class*="error"], [class*="Error"]');
      results.noErrors = errorElements.length === 0;

      // Test 7: Check if React is working
      results.reactWorking = typeof window.React !== 'undefined' && window.__REACT_LOADED__;

      setTestResults(results);
    } catch (error) {
      console.error('Test execution error:', error);
      setTestResults({ error: error.message });
    }

    setIsRunning(false);
  };

  const getTestStatus = (result) => {
    if (result === true) return { icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />, text: 'PASS' };
    if (result === false) return { icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />, text: 'FAIL' };
    return { icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />, text: 'UNKNOWN' };
  };

  const overallSuccess = Object.values(testResults).filter(Boolean).length >= 5;

  return (
    <Card 
      title="Component Builder Test Suite" 
      style={{ margin: '20px', maxWidth: '600px' }}
      extra={
        <Button 
          type="primary" 
          icon={<PlayCircleOutlined />} 
          onClick={runTests}
          loading={isRunning}
        >
          Run Tests
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {testResults.error ? (
          <Alert
            message="Test Error"
            description={testResults.error}
            type="error"
            showIcon
          />
        ) : (
          <>
            {Object.keys(testResults).length > 0 && (
              <Alert
                message={`Overall Status: ${overallSuccess ? 'PASS' : 'FAIL'}`}
                type={overallSuccess ? 'success' : 'error'}
                showIcon
              />
            )}

            {Object.keys(testResults).map(testName => {
              const result = testResults[testName];
              const status = getTestStatus(result);
              
              return (
                <div key={testName} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <Text>{testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</Text>
                  <Space>
                    {status.icon}
                    <Text strong style={{ 
                      color: result ? '#52c41a' : '#ff4d4f' 
                    }}>
                      {status.text}
                    </Text>
                  </Space>
                </div>
              );
            })}
          </>
        )}

        {Object.keys(testResults).length === 0 && (
          <Text type="secondary">Click "Run Tests" to verify component builder functionality</Text>
        )}
      </Space>
    </Card>
  );
};

export default ComponentBuilderTest;
