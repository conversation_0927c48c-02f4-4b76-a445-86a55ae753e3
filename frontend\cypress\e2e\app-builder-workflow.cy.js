/**
 * Cypress End-to-End Tests for App Builder Workflow
 * 
 * Tests the complete user journey in a real browser environment
 */

describe('App Builder Complete Workflow', () => {
  beforeEach(() => {
    // Visit the App Builder page
    cy.visit('/app-builder');
    
    // Wait for the application to load
    cy.get('[data-testid="app-builder"]', { timeout: 10000 }).should('be.visible');
    
    // Mock API responses
    cy.intercept('GET', '/api/templates/', { fixture: 'templates.json' }).as('getTemplates');
    cy.intercept('POST', '/api/export/', { fixture: 'export-response.json' }).as('exportApp');
  });

  describe('Template Selection and Loading', () => {
    it('should load and apply the Hello World template', () => {
      // Open template manager
      cy.get('[data-testid="template-manager-button"]').click();
      
      // Wait for templates to load
      cy.wait('@getTemplates');
      
      // Find and select Hello World template
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .should('be.visible');
      
      // Click on the template
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      
      // Click load button
      cy.get('[data-testid="load-template-button"]').click();
      
      // Verify template is loaded
      cy.get('[data-testid="app-canvas"]')
        .should('contain', 'Hello World!');
      
      // Verify components are added to the canvas
      cy.get('[data-testid="component-header"]').should('exist');
      cy.get('[data-testid="component-button"]').should('exist');
    });

    it('should preview template before loading', () => {
      // Open template manager
      cy.get('[data-testid="template-manager-button"]').click();
      
      // Click preview button on Hello World template
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .parent()
        .find('[data-testid="preview-button"]')
        .click();
      
      // Verify preview modal opens
      cy.get('[data-testid="template-preview-modal"]').should('be.visible');
      
      // Verify preview content
      cy.get('[data-testid="template-preview-content"]')
        .should('contain', 'Hello World!');
      
      // Close preview
      cy.get('[data-testid="close-preview-button"]').click();
      cy.get('[data-testid="template-preview-modal"]').should('not.exist');
    });
  });

  describe('Component Management', () => {
    beforeEach(() => {
      // Load Hello World template first
      cy.get('[data-testid="template-manager-button"]').click();
      cy.wait('@getTemplates');
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      cy.get('[data-testid="load-template-button"]').click();
      cy.get('[data-testid="template-manager-modal"]').should('not.exist');
    });

    it('should add new components to the canvas', () => {
      // Open component palette
      cy.get('[data-testid="component-palette"]').should('be.visible');
      
      // Add a text component
      cy.get('[data-testid="add-text-component"]').click();
      
      // Verify text component is added
      cy.get('[data-testid="component-text"]').should('have.length.at.least', 2);
      
      // Add a button component
      cy.get('[data-testid="add-button-component"]').click();
      
      // Verify button component is added
      cy.get('[data-testid="component-button"]').should('have.length.at.least', 2);
    });

    it('should select and highlight components', () => {
      // Click on a component to select it
      cy.get('[data-testid="component-header"]').first().click();
      
      // Verify component is selected (highlighted)
      cy.get('[data-testid="component-header"]')
        .first()
        .should('have.class', 'selected');
      
      // Verify property panel shows component properties
      cy.get('[data-testid="property-panel"]').should('be.visible');
      cy.get('[data-testid="property-panel"]')
        .should('contain', 'Header Properties');
    });

    it('should delete components', () => {
      // Select a component
      cy.get('[data-testid="component-button"]').first().click();
      
      // Delete the component
      cy.get('[data-testid="delete-component-button"]').click();
      
      // Confirm deletion
      cy.get('[data-testid="confirm-delete-button"]').click();
      
      // Verify component is removed
      cy.get('[data-testid="component-button"]').should('have.length', 1);
    });
  });

  describe('Property Editing', () => {
    beforeEach(() => {
      // Load template and select a component
      cy.get('[data-testid="template-manager-button"]').click();
      cy.wait('@getTemplates');
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      cy.get('[data-testid="load-template-button"]').click();
      cy.get('[data-testid="component-button"]').first().click();
    });

    it('should edit text properties', () => {
      // Change button text
      cy.get('[data-testid="property-text-input"]')
        .clear()
        .type('Updated Button Text');
      
      // Verify text is updated in the component
      cy.get('[data-testid="component-button"]')
        .first()
        .should('contain', 'Updated Button Text');
    });

    it('should edit color properties', () => {
      // Change button background color
      cy.get('[data-testid="property-background-color"]').click();
      cy.get('[data-testid="color-picker"]').should('be.visible');
      cy.get('[data-testid="color-option-green"]').click();
      
      // Verify color is applied
      cy.get('[data-testid="component-button"]')
        .first()
        .should('have.css', 'background-color', 'rgb(82, 196, 26)'); // #52c41a
    });

    it('should edit size properties', () => {
      // Change button padding
      cy.get('[data-testid="property-padding-input"]')
        .clear()
        .type('20px 40px');
      
      // Verify padding is applied
      cy.get('[data-testid="component-button"]')
        .first()
        .should('have.css', 'padding', '20px 40px');
    });
  });

  describe('Layout Management', () => {
    beforeEach(() => {
      // Load template
      cy.get('[data-testid="template-manager-button"]').click();
      cy.wait('@getTemplates');
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      cy.get('[data-testid="load-template-button"]').click();
    });

    it('should drag and drop components to reposition them', () => {
      // Get initial position of a component
      cy.get('[data-testid="component-button"]').first().then(($el) => {
        const initialRect = $el[0].getBoundingClientRect();
        
        // Drag the component to a new position
        cy.get('[data-testid="component-button"]')
          .first()
          .trigger('dragstart');
        
        cy.get('[data-testid="drop-zone"]')
          .trigger('dragover')
          .trigger('drop');
        
        // Verify component moved
        cy.get('[data-testid="component-button"]').first().then(($newEl) => {
          const newRect = $newEl[0].getBoundingClientRect();
          expect(newRect.top).to.not.equal(initialRect.top);
        });
      });
    });

    it('should resize components', () => {
      // Select a component
      cy.get('[data-testid="component-container"]').first().click();
      
      // Verify resize handles appear
      cy.get('[data-testid="resize-handle"]').should('be.visible');
      
      // Drag resize handle
      cy.get('[data-testid="resize-handle-se"]')
        .trigger('mousedown', { which: 1 })
        .trigger('mousemove', { clientX: 100, clientY: 100 })
        .trigger('mouseup');
      
      // Verify component size changed
      cy.get('[data-testid="component-container"]')
        .first()
        .should('have.css', 'width')
        .and('not.equal', '100%');
    });
  });

  describe('Theme Management', () => {
    beforeEach(() => {
      // Load template
      cy.get('[data-testid="template-manager-button"]').click();
      cy.wait('@getTemplates');
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      cy.get('[data-testid="load-template-button"]').click();
    });

    it('should switch between light and dark themes', () => {
      // Open theme manager
      cy.get('[data-testid="theme-manager-button"]').click();
      
      // Switch to dark theme
      cy.get('[data-testid="dark-theme-option"]').click();
      
      // Verify dark theme is applied
      cy.get('[data-testid="app-canvas"]')
        .should('have.class', 'dark-theme');
      
      // Switch back to light theme
      cy.get('[data-testid="light-theme-option"]').click();
      
      // Verify light theme is applied
      cy.get('[data-testid="app-canvas"]')
        .should('not.have.class', 'dark-theme');
    });

    it('should customize theme colors', () => {
      // Open theme customization
      cy.get('[data-testid="theme-manager-button"]').click();
      cy.get('[data-testid="customize-theme-button"]').click();
      
      // Change primary color
      cy.get('[data-testid="primary-color-picker"]').click();
      cy.get('[data-testid="color-option-purple"]').click();
      
      // Apply theme
      cy.get('[data-testid="apply-theme-button"]').click();
      
      // Verify primary color is applied to components
      cy.get('[data-testid="component-header"]')
        .first()
        .should('have.css', 'background-color')
        .and('include', '114, 46, 209'); // #722ed1
    });
  });

  describe('Code Export', () => {
    beforeEach(() => {
      // Load template and make some modifications
      cy.get('[data-testid="template-manager-button"]').click();
      cy.wait('@getTemplates');
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      cy.get('[data-testid="load-template-button"]').click();
      
      // Modify a component
      cy.get('[data-testid="component-button"]').first().click();
      cy.get('[data-testid="property-text-input"]')
        .clear()
        .type('Export Test Button');
    });

    it('should export React code', () => {
      // Open export panel
      cy.get('[data-testid="export-button"]').click();
      
      // Select React format
      cy.get('[data-testid="export-format-select"]').select('react');
      
      // Generate code
      cy.get('[data-testid="generate-code-button"]').click();
      
      // Wait for export
      cy.wait('@exportApp');
      
      // Verify code is generated
      cy.get('[data-testid="code-preview"]').should('be.visible');
      cy.get('[data-testid="code-preview"]')
        .should('contain', 'import React')
        .and('contain', 'Export Test Button')
        .and('contain', 'export default App');
    });

    it('should export Vue code', () => {
      // Open export panel
      cy.get('[data-testid="export-button"]').click();
      
      // Select Vue format
      cy.get('[data-testid="export-format-select"]').select('vue');
      
      // Generate code
      cy.get('[data-testid="generate-code-button"]').click();
      
      // Wait for export
      cy.wait('@exportApp');
      
      // Verify Vue code structure
      cy.get('[data-testid="code-preview"]')
        .should('contain', '<template>')
        .and('contain', '<script>')
        .and('contain', '<style>')
        .and('contain', 'Export Test Button');
    });

    it('should copy code to clipboard', () => {
      // Open export panel and generate code
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="generate-code-button"]').click();
      cy.wait('@exportApp');
      
      // Copy code
      cy.get('[data-testid="copy-code-button"]').click();
      
      // Verify success message
      cy.get('[data-testid="copy-success-message"]')
        .should('be.visible')
        .and('contain', 'Code copied to clipboard');
    });

    it('should download code files', () => {
      // Open export panel and generate code
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="generate-code-button"]').click();
      cy.wait('@exportApp');
      
      // Download files
      cy.get('[data-testid="download-code-button"]').click();
      
      // Verify download was initiated (check for download attribute)
      cy.get('[data-testid="download-link"]')
        .should('have.attr', 'download');
    });
  });

  describe('Error Handling', () => {
    it('should handle template loading errors gracefully', () => {
      // Mock API error
      cy.intercept('GET', '/api/templates/', { statusCode: 500 }).as('getTemplatesError');
      
      // Try to open template manager
      cy.get('[data-testid="template-manager-button"]').click();
      
      // Wait for error
      cy.wait('@getTemplatesError');
      
      // Verify error message is shown
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'Failed to load templates');
    });

    it('should handle export errors gracefully', () => {
      // Load template first
      cy.get('[data-testid="template-manager-button"]').click();
      cy.wait('@getTemplates');
      cy.get('[data-testid="template-card"]')
        .contains('[Default] Hello World Starter')
        .click();
      cy.get('[data-testid="load-template-button"]').click();
      
      // Mock export error
      cy.intercept('POST', '/api/export/', { statusCode: 500 }).as('exportError');
      
      // Try to export
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="generate-code-button"]').click();
      
      // Wait for error
      cy.wait('@exportError');
      
      // Verify error message
      cy.get('[data-testid="export-error-message"]')
        .should('be.visible')
        .and('contain', 'Export failed');
    });
  });

  describe('Accessibility', () => {
    it('should be keyboard navigable', () => {
      // Test keyboard navigation through main interface
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'template-manager-button');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'component-palette');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'app-canvas');
    });

    it('should have proper ARIA labels', () => {
      // Check important elements have ARIA labels
      cy.get('[data-testid="template-manager-button"]')
        .should('have.attr', 'aria-label');
      
      cy.get('[data-testid="component-palette"]')
        .should('have.attr', 'aria-label');
      
      cy.get('[data-testid="app-canvas"]')
        .should('have.attr', 'aria-label');
    });

    it('should meet color contrast requirements', () => {
      // This would typically use a specialized accessibility testing tool
      // For now, we'll check that text is visible against backgrounds
      cy.get('[data-testid="component-button"]')
        .should('be.visible')
        .and('have.css', 'color')
        .and('not.equal', 'rgba(0, 0, 0, 0)');
    });
  });
});
