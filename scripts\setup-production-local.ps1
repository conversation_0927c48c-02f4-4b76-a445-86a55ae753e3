# PowerShell script to set up production environment locally
# This script configures and starts the production environment for local testing

param(
    [switch]$Clean,
    [switch]$SkipBuild,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

function Write-Status {
    param($Message, $Color = "Yellow")
    Write-Host "🔧 $Message" -ForegroundColor $Color
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Cyan
}

Write-Status "Setting up App Builder 201 Production Environment (Local)" "Magenta"
Write-Info "This script will configure and start the production environment for local testing"

# Check if Docker is running
Write-Status "Checking Docker availability..."
try {
    docker version | Out-Null
    Write-Success "Docker is running"
}
catch {
    Write-Error "Docker is not running. Please start Docker Desktop and try again."
    exit 1
}

# Check if docker-compose is available
try {
    docker-compose version | Out-Null
    Write-Success "Docker Compose is available"
}
catch {
    Write-Error "Docker Compose is not available. Please install Docker Compose."
    exit 1
}

# Clean up if requested
if ($Clean) {
    Write-Status "Cleaning up existing containers and volumes..."
    docker-compose -f docker-compose.prod.yml down -v 2>$null
    docker system prune -f | Out-Null
    Write-Success "Cleanup completed"
}

# Create root .env file if it doesn't exist
Write-Status "Setting up environment variables..."
$rootEnvFile = ".env"
if (-not (Test-Path $rootEnvFile)) {
    @"
# Production Environment Variables
DB_PASSWORD=secure_local_prod_password_123
REDIS_PASSWORD=secure_redis_password_123
"@ | Out-File -FilePath $rootEnvFile -Encoding UTF8
    Write-Success "Created root .env file"
}
else {
    Write-Info "Root .env file already exists"
}

# Update backend production environment for local testing
Write-Status "Configuring backend production environment for local testing..."
$backendEnvFile = "backend/.env.production"
if (Test-Path $backendEnvFile) {
    $content = Get-Content $backendEnvFile
    $content = $content -replace "ALLOWED_HOSTS=.*", "ALLOWED_HOSTS=localhost,127.0.0.1,backend,frontend"
    $content = $content -replace "DB_PASSWORD=.*", "DB_PASSWORD=secure_local_prod_password_123"
    $content = $content -replace "SECURE_SSL_REDIRECT=True", "SECURE_SSL_REDIRECT=False"
    $content = $content -replace "SESSION_COOKIE_SECURE=True", "SESSION_COOKIE_SECURE=False"
    $content = $content -replace "CSRF_COOKIE_SECURE=True", "CSRF_COOKIE_SECURE=False"
    $content = $content -replace "CORS_ALLOWED_ORIGINS=.*", "CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1,http://localhost:80"
    
    # Add Redis password if not present
    if ($content -notcontains "REDIS_PASSWORD=") {
        $content += "REDIS_PASSWORD=secure_redis_password_123"
    }
    else {
        $content = $content -replace "REDIS_PASSWORD=.*", "REDIS_PASSWORD=secure_redis_password_123"
    }
    
    $content | Out-File -FilePath $backendEnvFile -Encoding UTF8
    Write-Success "Updated backend production environment"
}
else {
    Write-Error "Backend .env.production file not found"
    exit 1
}

# Update frontend production environment for local testing
Write-Status "Configuring frontend production environment for local testing..."
$frontendEnvFile = "frontend/.env.production"
if (Test-Path $frontendEnvFile) {
    $content = Get-Content $frontendEnvFile
    $content = $content -replace "REACT_APP_API_URL=.*", "REACT_APP_API_URL=http://localhost/api"
    $content = $content -replace "REACT_APP_WS_URL=.*", "REACT_APP_WS_URL=ws://localhost/ws"
    $content | Out-File -FilePath $frontendEnvFile -Encoding UTF8
    Write-Success "Updated frontend production environment"
}
else {
    Write-Error "Frontend .env.production file not found"
    exit 1
}

# Build production images
if (-not $SkipBuild) {
    Write-Status "Building production images..."
    Write-Info "This may take several minutes..."
    
    if ($Verbose) {
        docker-compose -f docker-compose.prod.yml build
    }
    else {
        docker-compose -f docker-compose.prod.yml build 2>&1 | Out-Null
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Production images built successfully"
    }
    else {
        Write-Error "Failed to build production images"
        exit 1
    }
}
else {
    Write-Info "Skipping build step"
}

# Start production services
Write-Status "Starting production services..."
docker-compose -f docker-compose.prod.yml up -d

if ($LASTEXITCODE -eq 0) {
    Write-Success "Production services started"
}
else {
    Write-Error "Failed to start production services"
    exit 1
}

# Wait for services to be ready
Write-Status "Waiting for services to be ready..."
$maxAttempts = 30
$attempt = 0

while ($attempt -lt $maxAttempts) {
    $attempt++
    Write-Host "." -NoNewline
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/health" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host ""
            Write-Success "Services are ready!"
            break
        }
    }
    catch {
        # Service not ready yet
    }
    
    if ($attempt -eq $maxAttempts) {
        Write-Host ""
        Write-Error "Services did not become ready within expected time"
        Write-Info "Check logs with: docker-compose -f docker-compose.prod.yml logs"
        exit 1
    }
    
    Start-Sleep -Seconds 2
}

# Run database migrations
Write-Status "Running database migrations..."
docker-compose -f docker-compose.prod.yml exec -T backend python manage.py migrate

if ($LASTEXITCODE -eq 0) {
    Write-Success "Database migrations completed"
}
else {
    Write-Error "Database migrations failed"
}

# Collect static files
Write-Status "Collecting static files..."
docker-compose -f docker-compose.prod.yml exec -T backend python manage.py collectstatic --noinput

if ($LASTEXITCODE -eq 0) {
    Write-Success "Static files collected"
}
else {
    Write-Error "Static files collection failed"
}

# Display status
Write-Status "Checking container status..."
docker-compose -f docker-compose.prod.yml ps

Write-Success "Production environment setup completed!" "Green"
Write-Info "Access the application at:"
Write-Host "  🌐 Frontend: http://localhost" -ForegroundColor Cyan
Write-Host "  🔧 API: http://localhost/api/" -ForegroundColor Cyan
Write-Host "  👤 Admin: http://localhost/admin/" -ForegroundColor Cyan
Write-Host "  ❤️  Health: http://localhost/health" -ForegroundColor Cyan

Write-Info "Useful commands:"
Write-Host "  View logs: docker-compose -f docker-compose.prod.yml logs -f" -ForegroundColor Yellow
Write-Host "  Stop: docker-compose -f docker-compose.prod.yml down" -ForegroundColor Yellow
Write-Host "  Restart: docker-compose -f docker-compose.prod.yml restart" -ForegroundColor Yellow

Write-Info "Monitor the logs for any issues:"
Write-Host "docker-compose -f docker-compose.prod.yml logs -f" -ForegroundColor Yellow
