# Production Environment Variables for Django Backend
# Generated on 2025-06-17 13:31:52

# Django Core Settings
DEBUG=False
SECRET_KEY=bKmXZrzxgZimpBXqIeG1ecsnysYFuGT1oNQpI2AWqkE_phVpgzRqvyOs6JWmMUJOlqw
ALLOWED_HOSTS=localhost,127.0.0.1,backend,frontend,your-domain.com,www.your-domain.com

# Database Configuration (PostgreSQL)
DB_ENGINE=django.db.backends.postgresql
DB_NAME=app_builder_201_prod
DB_USER=app_builder_user
DB_PASSWORD=secure_local_prod_password_123
DB_HOST=db
DB_PORT=5432

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://:secure_redis_password_123@redis:6379/0
CACHE_URL=redis://:secure_redis_password_123@redis:6379/1
REDIS_PASSWORD=secure_redis_password_123

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password_here

# Security Settings (Modified for local production testing)
SECURE_SSL_REDIRECT=False
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# CORS Settings (Modified for local production testing)
CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1,http://localhost:80,https://your-domain.com,https://www.your-domain.com
CORS_ALLOW_CREDENTIALS=True

# Static Files (AWS S3 or similar)
USE_S3=True
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_STORAGE_BUCKET_NAME=your-app-static-files
AWS_S3_REGION_NAME=us-east-1

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=INFO

# Application Settings
ENVIRONMENT=production
APP_VERSION=1.0.0
