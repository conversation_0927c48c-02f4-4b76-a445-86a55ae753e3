// Debug script to check feature buttons visibility
// Run this in browser console: copy and paste this entire script

console.log('🔍 Debugging Feature Buttons...');

// Wait for React to render
setTimeout(() => {
  console.log('\n=== FEATURE BUTTON DEBUG REPORT ===\n');
  
  // Check if IntegratedAppBuilder is loaded
  const appBuilder = document.querySelector('[data-testid="integrated-app-builder"]') || 
                    document.querySelector('.integrated-container') ||
                    document.querySelector('div[class*="IntegratedContainer"]');
  
  if (!appBuilder) {
    console.error('❌ IntegratedAppBuilder component not found');
    return;
  }
  
  console.log('✅ IntegratedAppBuilder component found');
  
  // Look for feature buttons
  const featureButtons = {
    testing: {
      selectors: [
        'button[data-tutorial="testing-tools"]',
        'button:contains("Testing")',
        'button:contains("🧪")'
      ],
      name: 'Testing Tools'
    },
    performance: {
      selectors: [
        'button[data-tutorial="performance-monitor"]',
        'button:contains("Performance")',
        'button:contains("⚡")'
      ],
      name: 'Performance Monitor'
    },
    dataManagement: {
      selectors: [
        'button[data-tutorial="data-management"]',
        'button:contains("Data")',
        'button:contains("📊")'
      ],
      name: 'Data Management'
    },
    enhancedExport: {
      selectors: [
        'button[data-tutorial="code-export"]',
        'button:contains("Export")',
        'button:contains("💻")'
      ],
      name: 'Enhanced Export'
    }
  };
  
  // Check each feature button
  Object.entries(featureButtons).forEach(([key, config]) => {
    console.log(`\n--- ${config.name} ---`);
    
    let found = false;
    config.selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        console.log(`✅ Found ${elements.length} element(s) with selector: ${selector}`);
        elements.forEach((el, index) => {
          console.log(`   ${index + 1}. Text: "${el.textContent.trim()}" | Visible: ${el.offsetParent !== null}`);
        });
        found = true;
      }
    });
    
    if (!found) {
      console.warn(`❌ ${config.name} button not found`);
    }
  });
  
  // Check for FeatureToggles container
  const featureToggles = document.querySelector('div[class*="FeatureToggles"]') ||
                        document.querySelector('[data-testid="feature-toggles"]');
  
  if (featureToggles) {
    console.log('\n✅ FeatureToggles container found');
    console.log('   - Children count:', featureToggles.children.length);
    console.log('   - All buttons in container:');
    
    const buttons = featureToggles.querySelectorAll('button');
    buttons.forEach((btn, index) => {
      console.log(`     ${index + 1}. "${btn.textContent.trim()}" (visible: ${btn.offsetParent !== null})`);
    });
  } else {
    console.warn('❌ FeatureToggles container not found');
  }
  
  // Check for any buttons with feature-related text
  console.log('\n--- All Feature-Related Buttons ---');
  const allButtons = document.querySelectorAll('button');
  const featureKeywords = ['testing', 'performance', 'data', 'export', 'tutorial'];
  
  allButtons.forEach((btn, index) => {
    const text = btn.textContent.toLowerCase();
    if (featureKeywords.some(keyword => text.includes(keyword))) {
      console.log(`${index + 1}. "${btn.textContent.trim()}" (visible: ${btn.offsetParent !== null})`);
    }
  });
  
  // Check enableFeatures prop
  console.log('\n--- React Component Props Debug ---');
  const reactFiber = appBuilder._reactInternalFiber || appBuilder._reactInternalInstance;
  if (reactFiber) {
    console.log('React fiber found, checking props...');
    // This is a simplified check - in real debugging you'd need to traverse the fiber tree
  }
  
  console.log('\n=== END DEBUG REPORT ===');
  
}, 2000);

// Also check immediately
console.log('Checking immediately...');
const buttons = document.querySelectorAll('button');
console.log(`Found ${buttons.length} total buttons on page`);
