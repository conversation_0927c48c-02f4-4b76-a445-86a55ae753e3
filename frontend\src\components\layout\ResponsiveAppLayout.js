/**
 * Responsive App Layout
 * 
 * Enhanced layout component with optimized panel sizing, collapsible sections,
 * improved workspace efficiency, mobile responsiveness, and consistent navigation patterns.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
// Optimized Ant Design imports for better tree-shaking
import { Layout, Button, Drawer, Affix, FloatButton, Tooltip, Badge } from '../../utils/optimizedAntdImports';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  ExpandOutlined,
  CompressOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  DragOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, responsiveUtils } from '../../design-system';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useLocalStorage } from '../../hooks/useLocalStorage';

const { Head<PERSON>, Sider, Content } = Layout;

// Enhanced responsive styled components
const ResponsiveLayout = styled(Layout)`
  height: 100vh;
  background: ${theme.colors.background.default};
  overflow: hidden;
  
  /* Smooth transitions for layout changes */
  transition: ${theme.transitions.default};
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }
`;

const AdaptiveHeader = styled(Header)`
  background: ${theme.colors.background.paper};
  border-bottom: 1px solid ${theme.colors.border.light};
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: ${theme.shadows.sm};
  z-index: ${theme.zIndex.appHeader};
  height: ${props => props.compact ? '48px' : '64px'};
  transition: ${theme.transitions.default};
  
  /* Mobile optimizations */
  ${theme.mediaQueries.maxMd} {
    height: 48px;
    padding: 0 ${theme.spacing[2]};
  }
`;

const FlexibleSider = styled(Sider)`
  background: ${theme.colors.background.paper};
  border-right: 1px solid ${theme.colors.border.light};
  box-shadow: ${theme.shadows.sm};
  z-index: ${theme.zIndex.appSidebar};
  transition: ${theme.transitions.default};
  
  /* Custom trigger styling */
  .ant-layout-sider-trigger {
    background: ${theme.colors.primary.main};
    color: ${theme.colors.primary.contrastText};
    border: none;
    height: 40px;
    line-height: 40px;
    transition: ${theme.transitions.default};
    
    &:hover {
      background: ${theme.colors.primary.dark};
    }
  }
  
  /* Responsive behavior */
  ${theme.mediaQueries.maxLg} {
    position: fixed !important;
    height: 100vh;
    z-index: ${theme.zIndex.modal};
  }
  
  /* Smooth width transitions */
  &.ant-layout-sider-collapsed {
    .sider-content {
      opacity: 0;
      pointer-events: none;
    }
  }
  
  .sider-content {
    opacity: 1;
    transition: opacity 0.2s ease;
    height: 100%;
    overflow: hidden;
  }
`;

const AdaptiveContent = styled(Content)`
  background: ${theme.colors.background.secondary};
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  
  /* Responsive margins for mobile */
  ${theme.mediaQueries.maxLg} {
    margin-left: 0 !important;
  }
`;

const PanelResizer = styled.div`
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  cursor: col-resize;
  z-index: ${theme.zIndex.docked};
  transition: ${theme.transitions.default};
  
  ${props => props.position === 'left' && `
    right: -2px;
  `}
  
  ${props => props.position === 'right' && `
    left: -2px;
  `}
  
  &:hover {
    background: ${theme.colors.primary.main};
  }
  
  &:active {
    background: ${theme.colors.primary.dark};
  }
  
  /* Hide on mobile */
  ${theme.mediaQueries.maxLg} {
    display: none;
  }
`;

const WorkspaceContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
`;

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  padding: 0 ${theme.spacing[4]};
  
  ${theme.mediaQueries.maxMd} {
    padding: 0 ${theme.spacing[2]};
    gap: ${theme.spacing[1]};
  }
`;

const MobileDrawerOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: ${theme.zIndex.overlay};
  opacity: ${props => props.visible ? 1 : 0};
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  transition: ${theme.transitions.default};
`;

const FloatingActionButton = styled(FloatButton.Group)`
  /* Custom positioning for mobile */
  ${theme.mediaQueries.maxMd} {
    .ant-float-btn-group {
      bottom: ${theme.spacing[4]} !important;
      right: ${theme.spacing[4]} !important;
    }
  }
`;

const BreakpointIndicator = styled.div`
  position: fixed;
  top: ${theme.spacing[2]};
  left: 50%;
  transform: translateX(-50%);
  background: ${theme.colors.primary.main};
  color: ${theme.colors.primary.contrastText};
  padding: ${theme.spacing[1]} ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.typography.fontSize.xs};
  font-weight: ${theme.typography.fontWeight.medium};
  z-index: ${theme.zIndex.tooltip};
  opacity: ${props => props.visible ? 1 : 0};
  transition: ${theme.transitions.default};
  pointer-events: none;
`;

// Default panel sizes for different breakpoints
const PANEL_SIZES = {
  desktop: {
    left: 320,
    right: 320,
    minLeft: 240,
    maxLeft: 480,
    minRight: 240,
    maxRight: 480
  },
  tablet: {
    left: 280,
    right: 280,
    minLeft: 200,
    maxLeft: 400,
    minRight: 200,
    maxRight: 400
  },
  mobile: {
    left: '100vw',
    right: '100vw'
  }
};

export default function ResponsiveAppLayout({
  leftPanel,
  rightPanel,
  children,
  headerContent,
  showBreakpointIndicator = false,
  enablePanelResize = true,
  persistLayout = true,
  compactMode = false
}) {
  // Responsive breakpoint detection
  const isMobile = useMediaQuery(theme.mediaQueries.maxMd);
  const isTablet = useMediaQuery(theme.mediaQueries.maxLg);
  const isDesktop = !isTablet;

  // Layout state with persistence
  const [layoutState, setLayoutState] = useLocalStorage('app-builder-layout', {
    leftSiderCollapsed: false,
    rightSiderCollapsed: false,
    leftSiderWidth: PANEL_SIZES.desktop.left,
    rightSiderWidth: PANEL_SIZES.desktop.right,
    isFullscreen: false
  });

  // Mobile-specific state
  const [mobileLeftDrawerVisible, setMobileLeftDrawerVisible] = useState(false);
  const [mobileRightDrawerVisible, setMobileRightDrawerVisible] = useState(false);

  // Panel resizing state
  const [isResizing, setIsResizing] = useState(false);
  const [resizingPanel, setResizingPanel] = useState(null);

  // Get current breakpoint
  const currentBreakpoint = useMemo(() => {
    if (isMobile) return 'mobile';
    if (isTablet) return 'tablet';
    return 'desktop';
  }, [isMobile, isTablet]);

  // Get panel sizes for current breakpoint
  const panelSizes = useMemo(() => {
    return PANEL_SIZES[currentBreakpoint];
  }, [currentBreakpoint]);

  // Update layout state
  const updateLayoutState = useCallback((updates) => {
    setLayoutState(prev => ({ ...prev, ...updates }));
  }, [setLayoutState]);

  // Toggle left panel
  const toggleLeftPanel = useCallback(() => {
    if (isMobile) {
      setMobileLeftDrawerVisible(prev => !prev);
    } else {
      updateLayoutState({ leftSiderCollapsed: !layoutState.leftSiderCollapsed });
    }
  }, [isMobile, layoutState.leftSiderCollapsed, updateLayoutState]);

  // Toggle right panel
  const toggleRightPanel = useCallback(() => {
    if (isMobile) {
      setMobileRightDrawerVisible(prev => !prev);
    } else {
      updateLayoutState({ rightSiderCollapsed: !layoutState.rightSiderCollapsed });
    }
  }, [isMobile, layoutState.rightSiderCollapsed, updateLayoutState]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      updateLayoutState({ isFullscreen: true });
    } else {
      document.exitFullscreen();
      updateLayoutState({ isFullscreen: false });
    }
  }, [updateLayoutState]);

  // Handle panel resizing
  const handleResizeStart = useCallback((panel, e) => {
    if (!enablePanelResize || isMobile) return;

    setIsResizing(true);
    setResizingPanel(panel);

    const startX = e.clientX;
    const startWidth = panel === 'left' ? layoutState.leftSiderWidth : layoutState.rightSiderWidth;

    const handleMouseMove = (e) => {
      const deltaX = panel === 'left' ? e.clientX - startX : startX - e.clientX;
      const newWidth = Math.max(
        panelSizes.minLeft || panelSizes.minRight,
        Math.min(panelSizes.maxLeft || panelSizes.maxRight, startWidth + deltaX)
      );

      updateLayoutState({
        [panel === 'left' ? 'leftSiderWidth' : 'rightSiderWidth']: newWidth
      });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      setResizingPanel(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [enablePanelResize, isMobile, layoutState, panelSizes, updateLayoutState]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      updateLayoutState({ isFullscreen: !!document.fullscreenElement });
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, [updateLayoutState]);

  // Close mobile drawers when switching to desktop
  useEffect(() => {
    if (!isMobile) {
      setMobileLeftDrawerVisible(false);
      setMobileRightDrawerVisible(false);
    }
  }, [isMobile]);

  // Adjust panel sizes for different breakpoints
  useEffect(() => {
    if (currentBreakpoint !== 'mobile') {
      const sizes = PANEL_SIZES[currentBreakpoint];
      updateLayoutState({
        leftSiderWidth: Math.min(layoutState.leftSiderWidth, sizes.maxLeft),
        rightSiderWidth: Math.min(layoutState.rightSiderWidth, sizes.maxRight)
      });
    }
  }, [currentBreakpoint, layoutState.leftSiderWidth, layoutState.rightSiderWidth, updateLayoutState]);

  return (
    <ResponsiveLayout>
      {/* Breakpoint indicator for development */}
      {showBreakpointIndicator && process.env.NODE_ENV === 'development' && (
        <BreakpointIndicator visible={true}>
          {currentBreakpoint.toUpperCase()} • {window.innerWidth}×{window.innerHeight}
        </BreakpointIndicator>
      )}

      {/* Header */}
      <AdaptiveHeader compact={compactMode}>
        <ToolbarContainer>
          <Button
            type="text"
            icon={layoutState.leftSiderCollapsed || mobileLeftDrawerVisible ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggleLeftPanel}
            size={compactMode ? 'small' : 'middle'}
          />

          {headerContent}
        </ToolbarContainer>

        <ToolbarContainer>
          <Tooltip title={`Current: ${currentBreakpoint}`}>
            <Button
              type="text"
              icon={
                currentBreakpoint === 'mobile' ? <MobileOutlined /> :
                  currentBreakpoint === 'tablet' ? <TabletOutlined /> :
                    <DesktopOutlined />
              }
              size={compactMode ? 'small' : 'middle'}
            />
          </Tooltip>

          <Button
            type="text"
            icon={layoutState.isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={toggleFullscreen}
            size={compactMode ? 'small' : 'middle'}
          />

          {!isMobile && (
            <Button
              type="text"
              icon={layoutState.rightSiderCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={toggleRightPanel}
              size={compactMode ? 'small' : 'middle'}
            />
          )}
        </ToolbarContainer>
      </AdaptiveHeader>

      <Layout>
        {/* Desktop Left Panel */}
        {!isMobile && (
          <FlexibleSider
            width={layoutState.leftSiderWidth}
            collapsed={layoutState.leftSiderCollapsed}
            collapsible
            trigger={null}
            theme="light"
          >
            <div className="sider-content">
              {leftPanel}
            </div>

            {enablePanelResize && !layoutState.leftSiderCollapsed && (
              <PanelResizer
                position="left"
                onMouseDown={(e) => handleResizeStart('left', e)}
              />
            )}
          </FlexibleSider>
        )}

        {/* Main Content */}
        <AdaptiveContent>
          <WorkspaceContainer>
            {children}
          </WorkspaceContainer>
        </AdaptiveContent>

        {/* Desktop Right Panel */}
        {!isMobile && (
          <FlexibleSider
            width={layoutState.rightSiderWidth}
            collapsed={layoutState.rightSiderCollapsed}
            collapsible
            trigger={null}
            theme="light"
            reverseArrow
          >
            <div className="sider-content">
              {rightPanel}
            </div>

            {enablePanelResize && !layoutState.rightSiderCollapsed && (
              <PanelResizer
                position="right"
                onMouseDown={(e) => handleResizeStart('right', e)}
              />
            )}
          </FlexibleSider>
        )}
      </Layout>

      {/* Mobile Drawers */}
      {isMobile && (
        <>
          <Drawer
            title="Components"
            placement="left"
            open={mobileLeftDrawerVisible}
            onClose={() => setMobileLeftDrawerVisible(false)}
            width="100vw"
            styles={{ body: { padding: 0 } }}
          >
            {leftPanel}
          </Drawer>

          <Drawer
            title="Properties"
            placement="right"
            open={mobileRightDrawerVisible}
            onClose={() => setMobileRightDrawerVisible(false)}
            width="100vw"
            styles={{ body: { padding: 0 } }}
          >
            {rightPanel}
          </Drawer>
        </>
      )}

      {/* Mobile Floating Action Buttons */}
      {isMobile && (
        <FloatingActionButton
          trigger="click"
          type="primary"
          style={{ right: theme.spacing[4], bottom: theme.spacing[4] }}
          icon={<SettingOutlined />}
        >
          <FloatButton
            icon={<MenuFoldOutlined />}
            tooltip="Components"
            onClick={() => setMobileLeftDrawerVisible(true)}
          />
          <FloatButton
            icon={<SettingOutlined />}
            tooltip="Properties"
            onClick={() => setMobileRightDrawerVisible(true)}
          />
          <FloatButton
            icon={layoutState.isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            tooltip="Fullscreen"
            onClick={toggleFullscreen}
          />
        </FloatingActionButton>
      )}

      {/* Resize cursor overlay */}
      {isResizing && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            cursor: 'col-resize',
            zIndex: theme.zIndex.modal,
            pointerEvents: 'none'
          }}
        />
      )}
    </ResponsiveLayout>
  );
}
