"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1470],{

/***/ 17177:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConnectionState: () => (/* binding */ ConnectionState),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(23029);
/* harmony import */ var _babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(92901);




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Enhanced WebSocket Client
 *
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Message batching for performance
 * - Offline message queue
 * - Heartbeat mechanism
 * - Comprehensive error handling
 * - Event-based API
 * - TypeScript-like documentation
 */

// Connection states
var ConnectionState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
  RECONNECTING: 4
};
var EnhancedWebSocketClient = /*#__PURE__*/function () {
  /**
   * Create a new EnhancedWebSocketClient
   * @param {Object} options - Configuration options
   * @param {string} options.url - WebSocket server URL
   * @param {boolean} [options.autoConnect=true] - Whether to connect automatically
   * @param {boolean} [options.autoReconnect=true] - Whether to reconnect automatically on disconnect
   * @param {number} [options.reconnectInterval=1000] - Initial reconnect interval in ms
   * @param {number} [options.maxReconnectInterval=30000] - Maximum reconnect interval in ms
   * @param {number} [options.reconnectDecay=1.5] - Reconnect interval decay factor
   * @param {number} [options.maxReconnectAttempts=Infinity] - Maximum reconnect attempts
   * @param {number} [options.connectionTimeout=5000] - Connection timeout in ms
   * @param {number} [options.heartbeatInterval=30000] - Heartbeat interval in ms
   * @param {boolean} [options.debug=false] - Enable debug logging
   * @param {number} [options.batchInterval=50] - Message batching interval in ms
   * @param {number} [options.maxBatchSize=100] - Maximum batch size
   * @param {boolean} [options.enableCompression=true] - Enable message compression for large messages
   * @param {number} [options.compressionThreshold=1024] - Minimum message size in bytes for compression
   * @param {boolean} [options.prioritizeUrgentMessages=true] - Prioritize urgent messages in the queue
   * @param {number} [options.maxOfflineQueueSize=1000] - Maximum size of the offline message queue
   * @param {boolean} [options.persistOfflineMessages=false] - Persist offline messages in localStorage
   * @param {string} [options.persistenceKey='enhanced_ws_offline_queue'] - Key for localStorage persistence
   * @param {Object} [options.protocols=null] - WebSocket protocols
   */
  function EnhancedWebSocketClient(options) {
    (0,_babel_runtime_helpers_classCallCheck__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(this, EnhancedWebSocketClient);
    this.options = _objectSpread({
      url: '',
      autoConnect: true,
      autoReconnect: true,
      reconnectInterval: 1000,
      maxReconnectInterval: 30000,
      reconnectDecay: 1.5,
      maxReconnectAttempts: Infinity,
      connectionTimeout: 5000,
      heartbeatInterval: 30000,
      debug: false,
      batchInterval: 50,
      maxBatchSize: 100,
      enableCompression: true,
      compressionThreshold: 1024,
      prioritizeUrgentMessages: true,
      maxOfflineQueueSize: 1000,
      persistOfflineMessages: false,
      persistenceKey: 'enhanced_ws_offline_queue',
      protocols: null
    }, options);

    // WebSocket instance
    this.ws = null;

    // Connection state
    this.connectionState = ConnectionState.CLOSED;

    // Reconnection state
    this.reconnectAttempts = 0;
    this.currentReconnectInterval = this.options.reconnectInterval;
    this.reconnectTimer = null;
    this.connectionTimeoutTimer = null;

    // Heartbeat state
    this.heartbeatTimer = null;
    this.missedHeartbeats = 0;
    this.maxMissedHeartbeats = 3;

    // Message batching
    this.messageQueue = [];
    this.batchTimer = null;

    // Offline queue
    this.offlineQueue = [];
    this.isOnline = navigator.onLine;

    // Event listeners
    this.eventListeners = {
      open: [],
      close: [],
      error: [],
      message: [],
      reconnect: [],
      reconnect_attempt: [],
      reconnect_failed: []
    };

    // Bind methods
    this._handleOpen = this._handleOpen.bind(this);
    this._handleClose = this._handleClose.bind(this);
    this._handleError = this._handleError.bind(this);
    this._handleMessage = this._handleMessage.bind(this);
    this._handleOnline = this._handleOnline.bind(this);
    this._handleOffline = this._handleOffline.bind(this);

    // Add online/offline event listeners
    window.addEventListener('online', this._handleOnline);
    window.addEventListener('offline', this._handleOffline);

    // Load persisted offline messages if enabled
    if (this.options.persistOfflineMessages) {
      this._loadPersistedMessages();
    }

    // Auto-connect if enabled
    if (this.options.autoConnect) {
      this.open();
    }
  }

  /**
   * Open the WebSocket connection
   * @returns {Promise<void>} Promise that resolves when connected or rejects on error
   */
  return (0,_babel_runtime_helpers_createClass__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(EnhancedWebSocketClient, [{
    key: "open",
    value: function open() {
      var _this = this;
      return new Promise(function (resolve, reject) {
        // Don't open if already connecting or open
        if (_this.connectionState === ConnectionState.CONNECTING || _this.connectionState === ConnectionState.OPEN) {
          _this._debug('WebSocket already connecting or open');
          if (_this.connectionState === ConnectionState.OPEN) {
            resolve(); // Already connected
          }
          return;
        }

        // Clear any existing connection
        _this.close();

        // Update connection state
        _this.connectionState = ConnectionState.CONNECTING;

        // Create new WebSocket
        try {
          _this._debug("Opening WebSocket connection to ".concat(_this.options.url));

          // Create WebSocket with optional protocols
          if (_this.options.protocols) {
            _this.ws = new WebSocket(_this.options.url, _this.options.protocols);
          } else {
            _this.ws = new WebSocket(_this.options.url);
          }

          // Create one-time event handlers for this connection attempt
          var onOpenOnce = function onOpenOnce(event) {
            _this._debug('Connection successful');
            _this._handleOpen(event);
            resolve();
          };
          var onErrorOnce = function onErrorOnce(event) {
            var error = new Error('WebSocket connection error');
            error.originalEvent = event;
            _this._handleError(event);
            reject(error);
          };
          var onCloseOnce = function onCloseOnce(event) {
            if (_this.connectionState === ConnectionState.CONNECTING) {
              var error = new Error("WebSocket connection closed during connection attempt: ".concat(event.code, " ").concat(event.reason));
              error.closeEvent = event;
              reject(error);
            }
          };

          // Set up one-time event listeners for this connection attempt
          _this.ws.addEventListener('open', onOpenOnce, {
            once: true
          });
          _this.ws.addEventListener('error', onErrorOnce, {
            once: true
          });
          _this.ws.addEventListener('close', onCloseOnce, {
            once: true
          });

          // Set regular event handlers
          _this.ws.onopen = _this._handleOpen;
          _this.ws.onclose = _this._handleClose;
          _this.ws.onerror = _this._handleError;
          _this.ws.onmessage = _this._handleMessage;

          // Set connection timeout
          _this.connectionTimeoutTimer = setTimeout(function () {
            if (_this.connectionState === ConnectionState.CONNECTING) {
              _this._debug('Connection timeout');
              var timeoutError = new Error("WebSocket connection timeout after ".concat(_this.options.connectionTimeout, "ms"));
              _this.ws.close();
              reject(timeoutError);
            }
          }, _this.options.connectionTimeout);
        } catch (error) {
          _this._debug('Error creating WebSocket:', error);
          _this._handleError(error);
          reject(error);
        }
      });
    }

    /**
     * Close the WebSocket connection
     * @param {number} [code=1000] - Close code
     * @param {string} [reason=''] - Close reason
     */
  }, {
    key: "close",
    value: function close() {
      var code = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;
      var reason = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      // Clear timers
      this._clearTimers();

      // Close WebSocket if it exists
      if (this.ws) {
        this.connectionState = ConnectionState.CLOSING;
        try {
          this.ws.close(code, reason);
        } catch (error) {
          this._debug('Error closing WebSocket:', error);
        }
      }

      // Update connection state
      this.connectionState = ConnectionState.CLOSED;
    }

    /**
     * Send a message through the WebSocket
     * @param {Object|string} data - Message data
     * @param {boolean} [batch=true] - Whether to batch the message
     * @param {Object} [options] - Additional options
     * @param {boolean} [options.compress=true] - Whether to compress the message if it's large
     * @param {boolean} [options.urgent=false] - Whether to send the message immediately, bypassing the queue
     * @returns {boolean} - Whether the message was sent or queued
     */
  }, {
    key: "send",
    value: function send(data) {
      var _this2 = this;
      var batch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var _options$compress = options.compress,
        compress = _options$compress === void 0 ? true : _options$compress,
        _options$urgent = options.urgent,
        urgent = _options$urgent === void 0 ? false : _options$urgent;

      // If urgent and connected, send immediately
      if (urgent && this.connectionState === ConnectionState.OPEN) {
        return this._sendImmediate(data, compress);
      }

      // If not connected, queue the message
      if (this.connectionState !== ConnectionState.OPEN) {
        this._debug('WebSocket not open, queueing message');
        this.offlineQueue.push({
          data: data,
          options: options
        });
        return false;
      }

      // If batching is enabled, add to batch queue
      if (batch && this.options.batchInterval > 0) {
        this.messageQueue.push({
          data: data,
          options: options
        });

        // Start batch timer if not already running
        if (!this.batchTimer) {
          this.batchTimer = setTimeout(function () {
            return _this2._processBatch();
          }, this.options.batchInterval);
        }

        // Process batch immediately if it reaches max size
        if (this.messageQueue.length >= this.options.maxBatchSize) {
          this._processBatch();
        }
        return true;
      }

      // Send immediately if not batching
      return this._sendImmediate(data, compress);
    }

    /**
     * Send a message immediately
     * @param {Object|string} data - Message data
     * @param {boolean} compress - Whether to compress the message
     * @returns {boolean} - Whether the message was sent successfully
     * @private
     */
  }, {
    key: "_sendImmediate",
    value: function _sendImmediate(data, compress) {
      try {
        // Prepare the message
        var message;
        if (typeof data === 'string') {
          message = data;
        } else {
          try {
            message = JSON.stringify(data);
          } catch (jsonError) {
            this._debug('Error stringifying message:', jsonError);
            this._handleError(jsonError);
            return false;
          }
        }

        // Compress large messages if enabled
        if (compress && message.length > 1024) {
          this._debug("Compressing large message (".concat(message.length, " bytes)"));
          // In a real implementation, we would compress the message here
          // For now, we'll just add a header indicating it should be compressed
          message = JSON.stringify({
            type: 'compressed',
            data: message,
            originalSize: message.length
          });
        }

        // Send the message
        this.ws.send(message);
        return true;
      } catch (error) {
        this._debug('Error sending message:', error);
        this._handleError(error);
        return false;
      }
    }

    /**
     * Add an event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     * @param {Object} [options] - Event options
     */
  }, {
    key: "addEventListener",
    value: function addEventListener(event, callback) {
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      if (!this.eventListeners[event]) {
        this.eventListeners[event] = [];
      }

      // Add callback with options
      this.eventListeners[event].push({
        callback: callback,
        options: options
      });
      this._debug("Added event listener for ".concat(event));
    }

    /**
     * Remove an event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
  }, {
    key: "removeEventListener",
    value: function removeEventListener(event, callback) {
      if (!this.eventListeners[event]) {
        return;
      }

      // Filter out the callback
      this.eventListeners[event] = this.eventListeners[event].filter(function (listener) {
        return listener.callback !== callback;
      });
      this._debug("Removed event listener for ".concat(event));
    }

    /**
     * Dispatch an event
     * @param {string} event - Event name
     * @param {Object} data - Event data
     * @private
     */
  }, {
    key: "_dispatchEvent",
    value: function _dispatchEvent(event, data) {
      var _this3 = this;
      if (!this.eventListeners[event]) {
        return;
      }

      // Call each listener
      this.eventListeners[event].forEach(function (listener) {
        try {
          listener.callback(data);

          // Remove if once option is set
          if (listener.options.once) {
            _this3.removeEventListener(event, listener.callback);
          }
        } catch (error) {
          _this3._debug("Error in ".concat(event, " listener:"), error);
        }
      });
    }

    /**
     * Handle WebSocket open event
     * @param {Event} event - WebSocket event
     * @private
     */
  }, {
    key: "_handleOpen",
    value: function _handleOpen(event) {
      this._debug('WebSocket connection opened');

      // Clear connection timeout
      clearTimeout(this.connectionTimeoutTimer);

      // Update connection state
      this.connectionState = ConnectionState.OPEN;

      // Reset reconnection state
      this.reconnectAttempts = 0;
      this.currentReconnectInterval = this.options.reconnectInterval;

      // Start heartbeat
      this._startHeartbeat();

      // Process offline queue
      this._processOfflineQueue();

      // Dispatch event
      this._dispatchEvent('open', event);
    }

    /**
     * Handle WebSocket close event
     * @param {CloseEvent} event - WebSocket close event
     * @private
     */
  }, {
    key: "_handleClose",
    value: function _handleClose(event) {
      var _this4 = this;
      this._debug("WebSocket connection closed: ".concat(event.code, " ").concat(event.reason));

      // Clear timers
      this._clearTimers();

      // Create detailed close info
      var closeInfo = {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        timestamp: new Date().toISOString()
      };

      // Update connection state
      this.connectionState = ConnectionState.CLOSED;

      // Handle specific close codes
      var shouldReconnect = this._handleCloseCode(event.code, event.reason);

      // Attempt to reconnect if enabled and should reconnect based on close code
      if (this.options.autoReconnect && shouldReconnect) {
        // Add a small delay before reconnecting to prevent rapid reconnection attempts
        setTimeout(function () {
          _this4._reconnect()["catch"](function (error) {
            _this4._debug('Reconnection chain failed:', error);
          });
        }, 100);
      }

      // Dispatch event with detailed close info
      this._dispatchEvent('close', {
        originalEvent: event,
        closeInfo: closeInfo,
        willReconnect: this.options.autoReconnect && shouldReconnect
      });
    }

    /**
     * Handle specific WebSocket close codes
     * @param {number} code - Close code
     * @param {string} reason - Close reason
     * @returns {boolean} - Whether to attempt reconnection
     * @private
     */
  }, {
    key: "_handleCloseCode",
    value: function _handleCloseCode(code, reason) {
      // Normal closure (1000) - don't reconnect
      if (code === 1000) {
        this._debug('Normal closure, not reconnecting');
        return false;
      }

      // Going away (1001) - server is shutting down or browser page is navigating away
      if (code === 1001) {
        this._debug('Server going away, will attempt reconnect');
        return true;
      }

      // Protocol error (1002) - protocol violation
      if (code === 1002) {
        this._debug('Protocol error, will attempt reconnect with delay');
        // Add extra delay for protocol errors
        this.currentReconnectInterval = Math.max(this.currentReconnectInterval * 2, this.options.reconnectInterval);
        return true;
      }

      // Unsupported data (1003) - server rejected data format
      if (code === 1003) {
        this._debug('Unsupported data format, will attempt reconnect');
        return true;
      }

      // No status (1005) - no status code was provided
      if (code === 1005) {
        this._debug('No status code provided, will attempt reconnect');
        return true;
      }

      // Abnormal closure (1006) - connection was closed abnormally
      if (code === 1006) {
        this._debug('Abnormal closure, will attempt reconnect');
        // This is often a network issue, so we should reconnect
        return true;
      }

      // Invalid frame payload data (1007) - message contained invalid data
      if (code === 1007) {
        this._debug('Invalid frame payload data, will attempt reconnect');
        return true;
      }

      // Policy violation (1008) - message violated server policy
      if (code === 1008) {
        this._debug('Policy violation, will attempt reconnect with increased delay');
        // Add extra delay for policy violations
        this.currentReconnectInterval = Math.max(this.currentReconnectInterval * 3, this.options.reconnectInterval);
        return true;
      }

      // Message too big (1009) - message was too large
      if (code === 1009) {
        this._debug('Message too big, will attempt reconnect');
        return true;
      }

      // Missing extension (1010) - server didn't negotiate required extensions
      if (code === 1010) {
        this._debug('Missing extension, will attempt reconnect');
        return true;
      }

      // Internal error (1011) - server encountered an error
      if (code === 1011) {
        this._debug('Server internal error, will attempt reconnect with delay');
        // Add extra delay for server errors
        this.currentReconnectInterval = Math.max(this.currentReconnectInterval * 2, this.options.reconnectInterval);
        return true;
      }

      // Service restart (1012) - server is restarting
      if (code === 1012) {
        this._debug('Service restart, will attempt reconnect');
        return true;
      }

      // Try again later (1013) - server is temporarily unavailable
      if (code === 1013) {
        this._debug('Service unavailable, will attempt reconnect with increased delay');
        // Add extra delay for service unavailable
        this.currentReconnectInterval = Math.max(this.currentReconnectInterval * 4, this.options.reconnectInterval);
        return true;
      }

      // Heartbeat timeout (4000) - custom code for heartbeat timeout
      if (code === 4000) {
        this._debug('Heartbeat timeout, will attempt reconnect');
        return true;
      }

      // Default: attempt to reconnect for unknown codes
      this._debug("Unknown close code ".concat(code, ", will attempt reconnect"));
      return true;
    }

    /**
     * Handle WebSocket error event
     * @param {Event} event - WebSocket error event
     * @private
     */
  }, {
    key: "_handleError",
    value: function _handleError(event) {
      this._debug('WebSocket error:', event);

      // Create a more detailed error object
      var errorDetails = {
        originalEvent: event,
        message: 'WebSocket connection error',
        timestamp: new Date().toISOString(),
        connectionState: this.connectionState,
        reconnectAttempts: this.reconnectAttempts,
        url: this.options.url
      };

      // Dispatch event with detailed error information
      this._dispatchEvent('error', errorDetails);
    }

    /**
     * Handle WebSocket message event
     * @param {MessageEvent} event - WebSocket message event
     * @private
     */
  }, {
    key: "_handleMessage",
    value: function _handleMessage(event) {
      var _this5 = this;
      // Parse message data
      var data = event.data;
      try {
        // Try to parse as JSON
        if (typeof data === 'string' && (data.startsWith('{') || data.startsWith('['))) {
          data = JSON.parse(data);
        }

        // Check if it's a heartbeat response
        if (data === 'pong' || data && data.type === 'pong') {
          this._handleHeartbeatResponse();
          return;
        }

        // Handle compressed message
        if (data && data.type === 'compressed') {
          this._debug("Received compressed message (".concat(data.originalSize, " bytes)"));
          // In a real implementation, we would decompress the message here
          // For now, we'll just use the data as is
          data = data.data;

          // Try to parse the decompressed data as JSON
          if (typeof data === 'string' && (data.startsWith('{') || data.startsWith('['))) {
            try {
              data = JSON.parse(data);
            } catch (parseError) {
              this._debug('Error parsing decompressed data:', parseError);
            }
          }
        }

        // Handle batch message
        if (data && data.type === 'batch') {
          this._debug("Received batch message with ".concat(data.count, " messages"));

          // Process each message in the batch
          var processMessages = function processMessages(messages) {
            if (!Array.isArray(messages)) return;
            messages.forEach(function (message) {
              try {
                // Parse message if it's a string
                var messageData = message;
                if (typeof message === 'string' && (message.startsWith('{') || message.startsWith('['))) {
                  messageData = JSON.parse(message);
                }

                // Dispatch event for each message
                _this5._dispatchEvent('message', {
                  data: messageData,
                  originalEvent: event,
                  isBatchItem: true
                });
              } catch (batchError) {
                _this5._debug('Error processing batch item:', batchError);
              }
            });
          };

          // Process both compressed and uncompressed messages
          if (data.messages) {
            if (data.messages.uncompressed) {
              processMessages(data.messages.uncompressed);
            }
            if (data.messages.compressed) {
              processMessages(data.messages.compressed);
            }
          } else if (Array.isArray(data.messages)) {
            // Handle old format for backward compatibility
            processMessages(data.messages);
          }
          return;
        }

        // Dispatch event for regular messages
        this._dispatchEvent('message', {
          data: data,
          originalEvent: event
        });
      } catch (error) {
        this._debug('Error parsing message:', error);

        // Dispatch event with raw data
        this._dispatchEvent('message', {
          data: event.data,
          originalEvent: event
        });
      }
    }

    /**
     * Handle online event
     * @private
     */
  }, {
    key: "_handleOnline",
    value: function _handleOnline() {
      this._debug('Browser went online');
      this.isOnline = true;

      // Reconnect if not connected
      if (this.connectionState !== ConnectionState.OPEN && this.options.autoReconnect) {
        this._reconnect();
      }
    }

    /**
     * Handle offline event
     * @private
     */
  }, {
    key: "_handleOffline",
    value: function _handleOffline() {
      this._debug('Browser went offline');
      this.isOnline = false;

      // Persist offline messages if enabled
      if (this.options.persistOfflineMessages && this.offlineQueue.length > 0) {
        this._persistOfflineMessages();
      }
    }

    /**
     * Attempt to reconnect
     * @returns {Promise<void>} Promise that resolves when reconnected or rejects on failure
     * @private
     */
  }, {
    key: "_reconnect",
    value: function _reconnect() {
      var _this6 = this;
      return new Promise(function (resolve, reject) {
        // Don't reconnect if already reconnecting
        if (_this6.connectionState === ConnectionState.RECONNECTING) {
          _this6._debug('Already attempting to reconnect');
          return;
        }

        // Check if max reconnect attempts reached
        if (_this6.reconnectAttempts >= _this6.options.maxReconnectAttempts) {
          _this6._debug('Max reconnect attempts reached');
          var error = new Error("Maximum reconnect attempts (".concat(_this6.options.maxReconnectAttempts, ") reached"));
          _this6._dispatchEvent('reconnect_failed', {
            attempts: _this6.reconnectAttempts,
            error: error
          });
          reject(error);
          return;
        }

        // Check if browser is offline
        if (!navigator.onLine) {
          _this6._debug('Browser is offline, waiting for online event');
          var _error = new Error('Cannot reconnect while offline');
          reject(_error);
          return;
        }

        // Update connection state
        _this6.connectionState = ConnectionState.RECONNECTING;

        // Increment reconnect attempts
        _this6.reconnectAttempts++;

        // Calculate reconnect interval with exponential backoff and jitter
        // Add jitter to prevent all clients from reconnecting simultaneously
        var jitter = Math.random() * 0.3 + 0.85; // Random value between 0.85 and 1.15
        var reconnectInterval = Math.min(_this6.currentReconnectInterval * _this6.options.reconnectDecay * jitter, _this6.options.maxReconnectInterval);
        _this6.currentReconnectInterval = reconnectInterval;
        _this6._debug("Reconnecting in ".concat(Math.round(reconnectInterval), "ms (attempt ").concat(_this6.reconnectAttempts, "/").concat(_this6.options.maxReconnectAttempts, ")"));

        // Dispatch reconnect attempt event
        _this6._dispatchEvent('reconnect_attempt', {
          attempt: _this6.reconnectAttempts,
          interval: reconnectInterval,
          maxAttempts: _this6.options.maxReconnectAttempts
        });

        // Set reconnect timer
        _this6.reconnectTimer = setTimeout(function () {
          _this6._debug("Attempting reconnect (".concat(_this6.reconnectAttempts, "/").concat(_this6.options.maxReconnectAttempts, ")"));

          // Try to open connection
          _this6.open().then(function () {
            _this6._debug('Reconnection successful');

            // Dispatch reconnect event
            _this6._dispatchEvent('reconnect', {
              attempt: _this6.reconnectAttempts,
              success: true
            });
            resolve();
          })["catch"](function (error) {
            _this6._debug('Reconnection failed:', error);

            // Dispatch reconnect event with error
            _this6._dispatchEvent('reconnect', {
              attempt: _this6.reconnectAttempts,
              success: false,
              error: error
            });

            // Try again if we haven't reached max attempts
            if (_this6.reconnectAttempts < _this6.options.maxReconnectAttempts) {
              _this6._reconnect().then(resolve)["catch"](reject);
            } else {
              reject(error);
            }
          });
        }, reconnectInterval);
      });
    }

    /**
     * Start heartbeat mechanism
     * @private
     */
  }, {
    key: "_startHeartbeat",
    value: function _startHeartbeat() {
      var _this7 = this;
      // Clear existing heartbeat
      this._clearHeartbeat();

      // Don't start if interval is 0
      if (this.options.heartbeatInterval <= 0) {
        return;
      }
      this._debug("Starting heartbeat (interval: ".concat(this.options.heartbeatInterval, "ms)"));

      // Reset missed heartbeats
      this.missedHeartbeats = 0;

      // Set heartbeat timer
      this.heartbeatTimer = setInterval(function () {
        _this7._sendHeartbeat();
      }, this.options.heartbeatInterval);
    }

    /**
     * Send heartbeat message
     * @private
     */
  }, {
    key: "_sendHeartbeat",
    value: function _sendHeartbeat() {
      // Check if connection is open
      if (this.connectionState !== ConnectionState.OPEN) {
        return;
      }
      this._debug('Sending heartbeat');

      // Increment missed heartbeats
      this.missedHeartbeats++;

      // Check if too many missed heartbeats
      if (this.missedHeartbeats >= this.maxMissedHeartbeats) {
        this._debug("Too many missed heartbeats (".concat(this.missedHeartbeats, "), closing connection"));
        this.close(4000, 'Heartbeat timeout');
        return;
      }

      // Send heartbeat
      try {
        this.ws.send(JSON.stringify({
          type: 'ping',
          timestamp: Date.now()
        }));
      } catch (error) {
        this._debug('Error sending heartbeat:', error);
      }
    }

    /**
     * Handle heartbeat response
     * @private
     */
  }, {
    key: "_handleHeartbeatResponse",
    value: function _handleHeartbeatResponse() {
      this._debug('Received heartbeat response');

      // Reset missed heartbeats
      this.missedHeartbeats = 0;
    }

    /**
     * Clear heartbeat timer
     * @private
     */
  }, {
    key: "_clearHeartbeat",
    value: function _clearHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    }

    /**
     * Process message batch
     * @private
     */
  }, {
    key: "_processBatch",
    value: function _processBatch() {
      // Clear batch timer
      clearTimeout(this.batchTimer);
      this.batchTimer = null;

      // Check if there are messages to send
      if (this.messageQueue.length === 0) {
        return;
      }

      // Check if connection is open
      if (this.connectionState !== ConnectionState.OPEN) {
        var _this$offlineQueue;
        // Move messages to offline queue
        (_this$offlineQueue = this.offlineQueue).push.apply(_this$offlineQueue, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this.messageQueue));
        this.messageQueue = [];
        return;
      }
      this._debug("Processing batch of ".concat(this.messageQueue.length, " messages"));

      // Group messages by compression option
      var compressedMessages = [];
      var uncompressedMessages = [];
      this.messageQueue.forEach(function (item) {
        var data = item.data,
          _item$options = item.options,
          options = _item$options === void 0 ? {} : _item$options;
        var _options$compress2 = options.compress,
          compress = _options$compress2 === void 0 ? true : _options$compress2;

        // Prepare the message
        var message = typeof data === 'string' ? data : JSON.stringify(data);
        if (compress) {
          compressedMessages.push(message);
        } else {
          uncompressedMessages.push(message);
        }
      });

      // Create batch message
      var batch = {
        type: 'batch',
        compressed: compressedMessages.length > 0,
        messages: {
          compressed: compressedMessages,
          uncompressed: uncompressedMessages
        },
        count: compressedMessages.length + uncompressedMessages.length,
        timestamp: Date.now()
      };

      // Clear message queue
      var originalQueue = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this.messageQueue);
      this.messageQueue = [];

      // Send batch
      try {
        this.ws.send(JSON.stringify(batch));
      } catch (error) {
        var _this$offlineQueue2;
        this._debug('Error sending batch:', error);
        this._handleError(error);

        // Move messages to offline queue
        (_this$offlineQueue2 = this.offlineQueue).push.apply(_this$offlineQueue2, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(originalQueue));
      }
    }

    /**
     * Process offline queue
     * @private
     */
  }, {
    key: "_processOfflineQueue",
    value: function _processOfflineQueue() {
      var _this8 = this;
      // Check if there are messages in the offline queue
      if (this.offlineQueue.length === 0) {
        return;
      }
      this._debug("Processing offline queue of ".concat(this.offlineQueue.length, " messages"));

      // Sort messages by priority (urgent first)
      var sortedQueue = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(this.offlineQueue).sort(function (a, b) {
        var _a$options, _b$options;
        var aUrgent = ((_a$options = a.options) === null || _a$options === void 0 ? void 0 : _a$options.urgent) || false;
        var bUrgent = ((_b$options = b.options) === null || _b$options === void 0 ? void 0 : _b$options.urgent) || false;
        return bUrgent - aUrgent; // true (1) comes before false (0)
      });

      // Process urgent messages immediately
      var urgentMessages = sortedQueue.filter(function (item) {
        var _item$options2;
        return (_item$options2 = item.options) === null || _item$options2 === void 0 ? void 0 : _item$options2.urgent;
      });
      var normalMessages = sortedQueue.filter(function (item) {
        var _item$options3;
        return !((_item$options3 = item.options) !== null && _item$options3 !== void 0 && _item$options3.urgent);
      });

      // Clear offline queue
      this.offlineQueue = [];

      // Send urgent messages immediately
      urgentMessages.forEach(function (item) {
        var data = item.data,
          _item$options4 = item.options,
          options = _item$options4 === void 0 ? {} : _item$options4;
        var _options$compress3 = options.compress,
          compress = _options$compress3 === void 0 ? true : _options$compress3;
        _this8._sendImmediate(data, compress);
      });

      // Move normal messages to batch queue
      if (normalMessages.length > 0) {
        var _this$messageQueue;
        (_this$messageQueue = this.messageQueue).push.apply(_this$messageQueue, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(normalMessages));
        // Process batch
        this._processBatch();
      }
    }

    /**
     * Clear all timers
     * @private
     */
  }, {
    key: "_clearTimers",
    value: function _clearTimers() {
      // Clear connection timeout
      if (this.connectionTimeoutTimer) {
        clearTimeout(this.connectionTimeoutTimer);
        this.connectionTimeoutTimer = null;
      }

      // Clear reconnect timer
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      // Clear heartbeat timer
      this._clearHeartbeat();

      // Clear batch timer
      if (this.batchTimer) {
        clearTimeout(this.batchTimer);
        this.batchTimer = null;
      }
    }

    /**
     * Debug log with proper timestamp formatting
     * @param {...any} args - Log arguments
     * @private
     */
  }, {
    key: "_debug",
    value: function _debug() {
      if (this.options.debug) {
        var _console;
        // Format timestamp as YYYY-MM-DD HH:MM:SS.mmm
        var now = new Date();
        var year = now.getFullYear();
        var month = String(now.getMonth() + 1).padStart(2, '0');
        var day = String(now.getDate()).padStart(2, '0');
        var hours = String(now.getHours()).padStart(2, '0');
        var minutes = String(now.getMinutes()).padStart(2, '0');
        var seconds = String(now.getSeconds()).padStart(2, '0');
        var milliseconds = String(now.getMilliseconds()).padStart(3, '0');
        var timestamp = "".concat(year, "-").concat(month, "-").concat(day, " ").concat(hours, ":").concat(minutes, ":").concat(seconds, ".").concat(milliseconds);
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        (_console = console).log.apply(_console, ["[".concat(timestamp, "][EnhancedWebSocketClient]")].concat(args));
      }
    }

    /**
     * Persist offline messages to localStorage
     * @private
     */
  }, {
    key: "_persistOfflineMessages",
    value: function _persistOfflineMessages() {
      if (!this.options.persistOfflineMessages || this.offlineQueue.length === 0) {
        return;
      }
      try {
        // Serialize offline queue
        var serializedQueue = JSON.stringify(this.offlineQueue);

        // Store in localStorage
        localStorage.setItem(this.options.persistenceKey, serializedQueue);
        this._debug("Persisted ".concat(this.offlineQueue.length, " offline messages to localStorage"));
      } catch (error) {
        this._debug('Error persisting offline messages:', error);
      }
    }

    /**
     * Load persisted offline messages from localStorage
     * @private
     */
  }, {
    key: "_loadPersistedMessages",
    value: function _loadPersistedMessages() {
      if (!this.options.persistOfflineMessages) {
        return;
      }
      try {
        // Get serialized queue from localStorage
        var serializedQueue = localStorage.getItem(this.options.persistenceKey);
        if (!serializedQueue) {
          return;
        }

        // Parse serialized queue
        var persistedQueue = JSON.parse(serializedQueue);
        if (Array.isArray(persistedQueue) && persistedQueue.length > 0) {
          var _this$offlineQueue3;
          // Add persisted messages to offline queue
          (_this$offlineQueue3 = this.offlineQueue).push.apply(_this$offlineQueue3, (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(persistedQueue));

          // Enforce max queue size
          if (this.offlineQueue.length > this.options.maxOfflineQueueSize) {
            this.offlineQueue = this.offlineQueue.slice(-this.options.maxOfflineQueueSize);
          }
          this._debug("Loaded ".concat(persistedQueue.length, " persisted offline messages"));

          // Clear persisted messages
          localStorage.removeItem(this.options.persistenceKey);
        }
      } catch (error) {
        this._debug('Error loading persisted offline messages:', error);
      }
    }

    /**
     * Clean up resources
     */
  }, {
    key: "destroy",
    value: function destroy() {
      this._debug('Destroying WebSocket client');

      // Persist offline messages if enabled
      if (this.options.persistOfflineMessages && this.offlineQueue.length > 0) {
        this._persistOfflineMessages();
      }

      // Close connection
      this.close();

      // Clear timers
      this._clearTimers();

      // Remove online/offline event listeners
      window.removeEventListener('online', this._handleOnline);
      window.removeEventListener('offline', this._handleOffline);

      // Clear event listeners
      this.eventListeners = {};

      // Clear queues
      this.messageQueue = [];
      this.offlineQueue = [];
    }
  }]);
}();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedWebSocketClient);

/***/ }),

/***/ 26390:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $0: () => (/* binding */ getWebSocketUrl),
/* harmony export */   e9: () => (/* binding */ getApiUrl)
/* harmony export */ });
/* unused harmony exports API_URL, WS_URL, WS_HOST, WS_ENDPOINT, BACKEND_HOST, ENV, DEBUG, WS_CONFIG, getAppConfig, getDefaultUserSettings, getBrowserInfo, getDeviceCapabilities */
/**
 * Environment Variables Configuration
 *
 * This file centralizes access to environment variables and provides
 * sensible defaults when environment variables are not available.
 */

// API URL for HTTP requests
var API_URL = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_API_URL || 'http://localhost:8000';

// WebSocket base URL for direct reference if needed
var WS_URL = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_URL || 'ws://localhost:8000/ws';

// WebSocket host for connection
var WS_HOST = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_HOST || 'localhost:8000';

// WebSocket endpoint
var WS_ENDPOINT = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_ENDPOINT || 'app_builder';

// Log environment variables for debugging
console.log('Environment Variables:');
console.log('API_URL:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_API_URL || 'http://localhost:8000');
console.log('WS_URL:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_URL || 'ws://localhost:8000/ws');
console.log('WS_HOST:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_HOST || 'localhost:8000');
console.log('WS_ENDPOINT:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_ENDPOINT || 'app_builder');
console.log('BACKEND_HOST:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_BACKEND_HOST || 'backend');
console.log('ENV:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_ENV || 'development');
console.log('DEBUG:', {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_DEBUG === 'true' || false);

// Backend host for internal container communication
var BACKEND_HOST = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_BACKEND_HOST || 'backend';

// Environment (development, production, test)
var ENV = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_ENV || 'development';

// Debug mode
var DEBUG = {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_DEBUG === 'true' || false;

/**
 * Get WebSocket URL for a specific endpoint
 * @param {string} endpoint - WebSocket endpoint (e.g., 'app_builder', 'test')
 * @param {Object} options - Additional options
 * @param {boolean} options.forceSecure - Force secure connection (wss)
 * @returns {string} Full WebSocket URL
 */
var getWebSocketUrl = function getWebSocketUrl(endpoint) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  // Normalize the endpoint
  var normalizedEndpoint = endpoint.replace(/^\/+|\/+$/g, '');

  // Determine the protocol based on the current page protocol or options
  var protocol;
  if (options.forceSecure) {
    protocol = 'wss:';
  } else if (typeof window !== 'undefined') {
    // In browser environment
    protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  } else {
    // Default to ws in non-browser environment
    protocol = 'ws:';
  }

  // Check if we're in development mode
  var isDevelopment = typeof window !== 'undefined' && ( false || window.location.port === '3000' || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

  // Determine the host
  var host;
  if (typeof window !== 'undefined') {
    if (isDevelopment) {
      // For development, connect directly to backend to avoid proxy issues
      console.log('🔌 Development mode: Connecting directly to backend WebSocket server');
      host = "".concat(window.location.hostname, ":8000");
    } else {
      // In production, use the configured host or fallback to current host
      host = WS_HOST || window.location.host;
    }
  } else {
    // In non-browser environment, use the configured host
    host = WS_HOST;
  }

  // Construct the URL with the correct format
  var url;

  // If WS_URL is provided in environment variables, use it directly (but not in development)
  if ({"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_WS_URL && !isDevelopment) {
    var baseUrl = WS_URL.endsWith('/') ? WS_URL : "".concat(WS_URL, "/");
    url = "".concat(baseUrl).concat(normalizedEndpoint, "/");
  } else {
    // Otherwise, construct the URL from protocol and host
    url = "".concat(protocol, "//").concat(host, "/ws/").concat(normalizedEndpoint, "/");
  }
  console.log('🔌 Generated WebSocket URL:', url);
  console.log('🔌 Development mode:', isDevelopment);
  console.log('🔌 Host:', host);
  console.log('🔌 Protocol:', protocol);
  console.log('🔌 Endpoint:', normalizedEndpoint);

  // Clean up any double slashes (except in protocol)
  var result = url.replace(/([^:]\/)\/+/g, '$1');

  // Debug log the URL
  console.log("\uD83D\uDD0C Final WebSocket URL for endpoint '".concat(normalizedEndpoint, "': ").concat(result));
  return result;
};

/**
 * Get API URL for a specific endpoint
 * @param {string} endpoint - API endpoint (e.g., 'users', 'components')
 * @returns {string} Full API URL
 */
var getApiUrl = function getApiUrl(endpoint) {
  // Normalize the endpoint
  var normalizedEndpoint = endpoint.replace(/^\/+|\/+$/g, '');

  // Ensure API_URL doesn't end with a slash
  var baseUrl = API_URL.endsWith('/') ? API_URL.slice(0, -1) : API_URL;
  return "".concat(baseUrl, "/api/").concat(normalizedEndpoint, "/");
};

// WebSocket configuration object
var WS_CONFIG = {
  BASE_URL: WS_URL,
  HOST: WS_HOST,
  ENDPOINT: WS_ENDPOINT,
  getUrl: getWebSocketUrl
};

// Get app configuration
var getAppConfig = function getAppConfig() {
  return {
    appName: {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_APP_NAME || 'App Builder 201',
    appVersion: {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_VERSION || '1.0.0',
    debug: DEBUG,
    features: {
      websockets: {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_FEATURE_WEBSOCKETS !== 'false',
      collaboration: {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_FEATURE_COLLABORATION !== 'false',
      themes: {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_FEATURE_THEMES !== 'false',
      dataManagement: {"ACLOCAL_PATH":"C:\\Program Files\\Git\\mingw64\\share\\aclocal;C:\\Program Files\\Git\\usr\\share\\aclocal","ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_6272_KMLXZXNOKXUMKOHQ","COLOR":"1","COLORTERM":"truecolor","COMMONPROGRAMFILES":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","CONFIG_SITE":"C:/Program Files/Git/etc/config.site","DISABLE_ESLINT_PLUGIN":"false","DISPLAY":"needs-to-be-defined","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","EXEPATH":"C:\\Program Files\\Git\\bin","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"cat","HOME":"C:\\Users\\<USER>\\Users\\danie","HOSTNAME":"LAPTOP-E9FOD0GS","INFOPATH":"C:\\Program Files\\Git\\mingw64\\local\\info;C:\\Program Files\\Git\\mingw64\\share\\info;C:\\Program Files\\Git\\usr\\local\\info;C:\\Program Files\\Git\\usr\\share\\info;C:\\Program Files\\Git\\usr\\info;C:\\Program Files\\Git\\share\\info","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LESS":"-FX","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","MANPATH":"C:\\Program Files\\Git\\mingw64\\local\\man;C:\\Program Files\\Git\\mingw64\\share\\man;C:\\Program Files\\Git\\usr\\local\\man;C:\\Program Files\\Git\\usr\\share\\man;C:\\Program Files\\Git\\usr\\man;C:\\Program Files\\Git\\share\\man","MINGW_CHOST":"x86_64-w64-mingw32","MINGW_PACKAGE_PREFIX":"mingw-w64-x86_64","MINGW_PREFIX":"C:/Program Files/Git/mingw64","MSYSTEM":"MINGW64","MSYSTEM_CARCH":"x86_64","MSYSTEM_CHOST":"x86_64-w64-mingw32","MSYSTEM_PREFIX":"C:/Program Files/Git/mingw64","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OLDPWD":"C:/Users/<USER>/New folder (2)/app-builder-201","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_PATH":"C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","ORIGINAL_TEMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_TMP":"C:/Users/<USER>/AppData/Local/Temp","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","PAGER":"cat","PATH":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Users\\<USER>\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\local\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Program Files\\Git\\mingw64\\bin;C:\\Program Files\\Git\\usr\\bin;C:\\Users\\<USER>\\bin;C:\\Python313\\Scripts;C:\\Python313;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0;C:\\Windows\\System32\\OpenSSH;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Program Files\\nodejs;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Program Files\\Git\\usr\\bin\\vendor_perl;C:\\Program Files\\Git\\usr\\bin\\core_perl","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW","PKG_CONFIG_PATH":"C:\\Program Files\\Git\\mingw64\\lib\\pkgconfig;C:\\Program Files\\Git\\mingw64\\share\\pkgconfig","PKG_CONFIG_SYSTEM_INCLUDE_PATH":"C:/Program Files/Git/mingw64/include","PKG_CONFIG_SYSTEM_LIBRARY_PATH":"C:/Program Files/Git/mingw64/lib","PLINK_PROTOCOL":"ssh","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","PROGRAMFILES":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\Program Files\\Git\\usr\\bin\\bash.exe","SHLVL":"2","SKIP_PREFLIGHT_CHECK":"true","SSH_ASKPASS":"C:/Program Files/Git/mingw64/bin/git-askpass.exe","SYSTEMDRIVE":"C:","SYSTEMROOT":"C:\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM":"xterm-256color","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TMPDIR":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","WINDIR":"C:\\WINDOWS","_":"C:/Program Files/nodejs/node.exe"}.REACT_APP_FEATURE_DATA_MANAGEMENT !== 'false'
    }
  };
};

// Default user settings
var getDefaultUserSettings = function getDefaultUserSettings() {
  return {
    theme: 'light',
    language: 'en',
    notifications: true,
    autoSave: true,
    autoSaveInterval: 60000,
    // 1 minute
    fontSize: 14,
    showLineNumbers: true,
    tabSize: 2,
    wordWrap: true
  };
};

// Get browser information
var getBrowserInfo = function getBrowserInfo() {
  var userAgent = navigator.userAgent;
  var browserName = 'Unknown';
  var browserVersion = 'Unknown';

  // Detect browser
  if (userAgent.indexOf('Firefox') > -1) {
    browserName = 'Firefox';
    browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf('Chrome') > -1) {
    browserName = 'Chrome';
    browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf('Safari') > -1) {
    browserName = 'Safari';
    browserVersion = userAgent.match(/Version\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf('MSIE') > -1 || userAgent.indexOf('Trident') > -1) {
    browserName = 'Internet Explorer';
    browserVersion = userAgent.match(/(?:MSIE |rv:)([0-9.]+)/)[1];
  } else if (userAgent.indexOf('Edge') > -1) {
    browserName = 'Edge';
    browserVersion = userAgent.match(/Edge\/([0-9.]+)/)[1];
  }
  return {
    browserName: browserName,
    browserVersion: browserVersion,
    userAgent: userAgent,
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
    isTablet: /iPad|Android(?!.*Mobile)/i.test(userAgent)
  };
};

// Get device capabilities
var getDeviceCapabilities = function getDeviceCapabilities() {
  return {
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    pixelRatio: window.devicePixelRatio || 1,
    touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    webSocketSupport: 'WebSocket' in window,
    storageSupport: 'localStorage' in window && 'sessionStorage' in window,
    webGLSupport: function () {
      try {
        var canvas = document.createElement('canvas');
        return !!(window.WebGLRenderingContext && (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
      } catch (e) {
        return false;
      }
    }()
  };
};

// Export all environment variables as a single object
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ({
  API_URL: API_URL,
  WS_URL: WS_URL,
  WS_HOST: WS_HOST,
  WS_ENDPOINT: WS_ENDPOINT,
  BACKEND_HOST: BACKEND_HOST,
  ENV: ENV,
  DEBUG: DEBUG,
  WS_CONFIG: WS_CONFIG,
  getWebSocketUrl: getWebSocketUrl,
  getApiUrl: getApiUrl,
  getAppConfig: getAppConfig,
  getDefaultUserSettings: getDefaultUserSettings,
  getBrowserInfo: getBrowserInfo,
  getDeviceCapabilities: getDeviceCapabilities
});

/***/ })

}]);