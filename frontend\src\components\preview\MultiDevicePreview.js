import React, { useState, useRef, useEffect } from 'react';
import { Card, Select, Button, Space, Typography, Tooltip, Slider, Switch } from 'antd';
import {
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  RotateRightOutlined,
  FullscreenOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  EyeOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Text } = Typography;
const { Option } = Select;

const PreviewContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ControlBar = styled.div`
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 8px;
`;

const DeviceFrame = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: auto;
`;

const DeviceContainer = styled.div`
  background: ${props => props.deviceType === 'mobile' ? '#333' : '#666'};
  border-radius: ${props => props.deviceType === 'mobile' ? '25px' : '8px'};
  padding: ${props => props.deviceType === 'mobile' ? '20px 10px' : '15px'};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  transform: scale(${props => props.scale}) ${props => props.rotated ? 'rotate(90deg)' : ''};
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: ${props => props.deviceType === 'mobile' ? '8px' : '6px'};
    left: 50%;
    transform: translateX(-50%);
    width: ${props => props.deviceType === 'mobile' ? '60px' : '80px'};
    height: ${props => props.deviceType === 'mobile' ? '4px' : '6px'};
    background: #999;
    border-radius: 2px;
    display: ${props => props.deviceType === 'desktop' ? 'none' : 'block'};
  }
`;

const Screen = styled.div`
  width: ${props => props.width}px;
  height: ${props => props.height}px;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 0 0 1px #e8e8e8;
`;

const PreviewContent = styled.div`
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
`;

const DeviceInfo = styled.div`
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  text-align: center;
  color: #666;
  font-size: 12px;
  font-weight: 500;
`;

// Device presets
const DEVICE_PRESETS = {
  mobile: {
    'iPhone 14': { width: 390, height: 844 },
    'iPhone 14 Plus': { width: 428, height: 926 },
    'iPhone SE': { width: 375, height: 667 },
    'Samsung Galaxy S23': { width: 360, height: 780 },
    'Google Pixel 7': { width: 412, height: 915 }
  },
  tablet: {
    'iPad': { width: 768, height: 1024 },
    'iPad Pro 11"': { width: 834, height: 1194 },
    'iPad Pro 12.9"': { width: 1024, height: 1366 },
    'Samsung Galaxy Tab': { width: 800, height: 1280 },
    'Surface Pro': { width: 912, height: 1368 }
  },
  desktop: {
    'MacBook Air': { width: 1280, height: 800 },
    'MacBook Pro 14"': { width: 1512, height: 982 },
    'iMac 24"': { width: 1920, height: 1080 },
    'Full HD': { width: 1920, height: 1080 },
    'Ultra Wide': { width: 2560, height: 1080 }
  }
};

const MultiDevicePreview = ({
  children,
  onDeviceChange = () => {},
  showControls = true,
  defaultDevice = 'desktop',
  defaultPreset = 'Full HD'
}) => {
  const [deviceType, setDeviceType] = useState(defaultDevice);
  const [devicePreset, setDevicePreset] = useState(defaultPreset);
  const [customSize, setCustomSize] = useState({ width: 1920, height: 1080 });
  const [scale, setScale] = useState(0.5);
  const [rotated, setRotated] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [showRuler, setShowRuler] = useState(false);
  const containerRef = useRef(null);

  // Get current device dimensions
  const getCurrentDimensions = () => {
    if (devicePreset === 'Custom') {
      return customSize;
    }
    return DEVICE_PRESETS[deviceType][devicePreset] || { width: 1920, height: 1080 };
  };

  const dimensions = getCurrentDimensions();
  const actualDimensions = rotated ? 
    { width: dimensions.height, height: dimensions.width } : 
    dimensions;

  // Handle device type change
  const handleDeviceTypeChange = (newType) => {
    setDeviceType(newType);
    setRotated(false);
    
    // Set default preset for device type
    const presets = Object.keys(DEVICE_PRESETS[newType]);
    const defaultPreset = presets[0];
    setDevicePreset(defaultPreset);
    
    onDeviceChange(newType, defaultPreset);
  };

  // Handle preset change
  const handlePresetChange = (preset) => {
    setDevicePreset(preset);
    onDeviceChange(deviceType, preset);
  };

  // Auto-adjust scale based on container size
  useEffect(() => {
    const adjustScale = () => {
      if (!containerRef.current) return;
      
      const container = containerRef.current;
      const containerWidth = container.clientWidth - 40; // padding
      const containerHeight = container.clientHeight - 40;
      
      const scaleX = containerWidth / actualDimensions.width;
      const scaleY = containerHeight / actualDimensions.height;
      const autoScale = Math.min(scaleX, scaleY, 1);
      
      setScale(autoScale);
    };

    adjustScale();
    window.addEventListener('resize', adjustScale);
    return () => window.removeEventListener('resize', adjustScale);
  }, [actualDimensions]);

  const renderControls = () => (
    <ControlBar>
      <Space wrap>
        <Space>
          <Text strong>Device:</Text>
          <Select
            value={deviceType}
            onChange={handleDeviceTypeChange}
            style={{ width: 120 }}
          >
            <Option value="mobile">
              <MobileOutlined /> Mobile
            </Option>
            <Option value="tablet">
              <TabletOutlined /> Tablet
            </Option>
            <Option value="desktop">
              <DesktopOutlined /> Desktop
            </Option>
          </Select>
        </Space>

        <Space>
          <Text strong>Preset:</Text>
          <Select
            value={devicePreset}
            onChange={handlePresetChange}
            style={{ width: 150 }}
          >
            {Object.keys(DEVICE_PRESETS[deviceType]).map(preset => (
              <Option key={preset} value={preset}>
                {preset}
              </Option>
            ))}
            <Option value="Custom">Custom Size</Option>
          </Select>
        </Space>

        {devicePreset === 'Custom' && (
          <Space>
            <Text>W:</Text>
            <input
              type="number"
              value={customSize.width}
              onChange={(e) => setCustomSize(prev => ({ ...prev, width: parseInt(e.target.value) || 0 }))}
              style={{ width: 80, padding: '4px 8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
            />
            <Text>H:</Text>
            <input
              type="number"
              value={customSize.height}
              onChange={(e) => setCustomSize(prev => ({ ...prev, height: parseInt(e.target.value) || 0 }))}
              style={{ width: 80, padding: '4px 8px', border: '1px solid #d9d9d9', borderRadius: '4px' }}
            />
          </Space>
        )}
      </Space>

      <Space wrap>
        <Space>
          <Text>Scale:</Text>
          <Slider
            min={0.1}
            max={1}
            step={0.1}
            value={scale}
            onChange={setScale}
            style={{ width: 100 }}
          />
          <Text>{Math.round(scale * 100)}%</Text>
        </Space>

        <Tooltip title="Rotate Device">
          <Button
            type={rotated ? 'primary' : 'default'}
            icon={<RotateRightOutlined />}
            onClick={() => setRotated(!rotated)}
            disabled={deviceType === 'desktop'}
          />
        </Tooltip>

        <Tooltip title="Toggle Ruler">
          <Button
            type={showRuler ? 'primary' : 'default'}
            icon={<EyeOutlined />}
            onClick={() => setShowRuler(!showRuler)}
          />
        </Tooltip>

        <Tooltip title="Fullscreen">
          <Button
            icon={<FullscreenOutlined />}
            onClick={() => setFullscreen(!fullscreen)}
          />
        </Tooltip>
      </Space>
    </ControlBar>
  );

  return (
    <PreviewContainer>
      {showControls && renderControls()}
      
      <DeviceFrame ref={containerRef}>
        <div style={{ position: 'relative' }}>
          <DeviceInfo>
            {devicePreset} - {actualDimensions.width} × {actualDimensions.height}
            {rotated && ' (Rotated)'}
          </DeviceInfo>
          
          <DeviceContainer
            deviceType={deviceType}
            scale={scale}
            rotated={rotated}
          >
            <Screen
              width={actualDimensions.width}
              height={actualDimensions.height}
            >
              <PreviewContent>
                {children}
              </PreviewContent>
            </Screen>
          </DeviceContainer>
        </div>
      </DeviceFrame>
    </PreviewContainer>
  );
};

export default MultiDevicePreview;
