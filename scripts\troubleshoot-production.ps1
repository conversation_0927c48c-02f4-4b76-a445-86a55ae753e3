# PowerShell script to troubleshoot production environment issues
# This script performs comprehensive diagnostics on the production environment

param(
    [switch]$Detailed,
    [switch]$FixIssues,
    [string]$Service = ""
)

$ErrorActionPreference = "Continue"

function Write-Status {
    param($Message, $Color = "Yellow")
    Write-Host "🔧 $Message" -ForegroundColor $Color
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Cyan
}

function Test-ContainerHealth {
    param($ContainerName)
    
    Write-Status "Checking $ContainerName health..."
    
    try {
        $status = docker inspect --format='{{.State.Health.Status}}' $ContainerName 2>$null
        if ($status -eq "healthy") {
            Write-Success "$ContainerName is healthy"
            return $true
        } elseif ($status -eq "unhealthy") {
            Write-Error "$ContainerName is unhealthy"
            return $false
        } else {
            Write-Warning "$ContainerName health status: $status"
            return $false
        }
    } catch {
        Write-Error "$ContainerName health check failed"
        return $false
    }
}

function Test-ServiceConnectivity {
    param($Url, $ServiceName)
    
    Write-Status "Testing $ServiceName connectivity..."
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "$ServiceName is accessible"
            return $true
        } else {
            Write-Warning "$ServiceName returned status code: $($response.StatusCode)"
            return $false
        }
    } catch {
        Write-Error "$ServiceName is not accessible: $($_.Exception.Message)"
        return $false
    }
}

function Get-ContainerLogs {
    param($ContainerName, $Lines = 50)
    
    Write-Status "Getting last $Lines lines of logs for $ContainerName..."
    try {
        docker-compose -f docker-compose.prod.yml logs --tail=$Lines $ContainerName
    } catch {
        Write-Error "Failed to get logs for $ContainerName"
    }
}

function Test-DatabaseConnection {
    Write-Status "Testing database connection..."
    
    try {
        $result = docker-compose -f docker-compose.prod.yml exec -T db pg_isready -U app_builder_user -d app_builder_201_prod
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database is ready"
            return $true
        } else {
            Write-Error "Database is not ready"
            return $false
        }
    } catch {
        Write-Error "Database connection test failed"
        return $false
    }
}

function Test-RedisConnection {
    Write-Status "Testing Redis connection..."
    
    try {
        $result = docker-compose -f docker-compose.prod.yml exec -T redis redis-cli ping
        if ($result -match "PONG") {
            Write-Success "Redis is responding"
            return $true
        } else {
            Write-Error "Redis is not responding"
            return $false
        }
    } catch {
        Write-Error "Redis connection test failed"
        return $false
    }
}

function Test-StaticFiles {
    Write-Status "Testing static files..."
    
    try {
        $result = docker-compose -f docker-compose.prod.yml exec -T backend ls -la /usr/src/app/staticfiles/
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Static files directory exists"
            return $true
        } else {
            Write-Error "Static files directory not found"
            return $false
        }
    } catch {
        Write-Error "Static files test failed"
        return $false
    }
}

function Fix-CommonIssues {
    Write-Status "Attempting to fix common issues..." "Magenta"
    
    # Restart unhealthy containers
    Write-Status "Restarting services..."
    docker-compose -f docker-compose.prod.yml restart
    
    # Run migrations
    Write-Status "Running database migrations..."
    docker-compose -f docker-compose.prod.yml exec -T backend python manage.py migrate
    
    # Collect static files
    Write-Status "Collecting static files..."
    docker-compose -f docker-compose.prod.yml exec -T backend python manage.py collectstatic --noinput
    
    # Clear cache
    Write-Status "Clearing cache..."
    docker-compose -f docker-compose.prod.yml exec -T redis redis-cli FLUSHALL
    
    Write-Success "Common fixes applied"
}

Write-Status "App Builder 201 Production Troubleshooting" "Magenta"
Write-Info "Performing comprehensive diagnostics..."

# Check if Docker is running
Write-Status "Checking Docker availability..."
try {
    docker version | Out-Null
    Write-Success "Docker is running"
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop."
    exit 1
}

# Check container status
Write-Status "Checking container status..."
$containers = docker-compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.State}}\t{{.Status}}"
Write-Host $containers

# Get running containers
$runningContainers = docker-compose -f docker-compose.prod.yml ps -q

if ($runningContainers.Count -eq 0) {
    Write-Error "No containers are running. Start with: docker-compose -f docker-compose.prod.yml up -d"
    exit 1
}

# Test individual container health
$healthyContainers = 0
$totalContainers = 0

$containerNames = @("app-builder-backend-prod", "app-builder-frontend-prod", "app-builder-nginx-prod", "app-builder-db-prod", "app-builder-redis-prod")

foreach ($container in $containerNames) {
    $totalContainers++
    if (Test-ContainerHealth $container) {
        $healthyContainers++
    }
}

Write-Info "Container Health: $healthyContainers/$totalContainers healthy"

# Test service connectivity
Write-Status "Testing service connectivity..."
$connectivityTests = @{
    "Frontend" = "http://localhost"
    "Health Check" = "http://localhost/health"
    "API" = "http://localhost/api/"
}

$passedTests = 0
foreach ($test in $connectivityTests.GetEnumerator()) {
    if (Test-ServiceConnectivity $test.Value $test.Key) {
        $passedTests++
    }
}

Write-Info "Connectivity Tests: $passedTests/$($connectivityTests.Count) passed"

# Test database and Redis
$dbHealthy = Test-DatabaseConnection
$redisHealthy = Test-RedisConnection
$staticFilesOk = Test-StaticFiles

# Resource usage check
Write-Status "Checking resource usage..."
try {
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
} catch {
    Write-Warning "Could not get resource usage statistics"
}

# Network connectivity between containers
Write-Status "Testing inter-container connectivity..."
try {
    $backendToDb = docker-compose -f docker-compose.prod.yml exec -T backend ping -c 1 db
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Backend can reach database"
    } else {
        Write-Error "Backend cannot reach database"
    }
    
    $frontendToBackend = docker-compose -f docker-compose.prod.yml exec -T frontend ping -c 1 backend
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Frontend can reach backend"
    } else {
        Write-Error "Frontend cannot reach backend"
    }
} catch {
    Write-Warning "Inter-container connectivity test failed"
}

# Show recent logs if requested or if issues found
if ($Detailed -or $healthyContainers -lt $totalContainers -or $passedTests -lt $connectivityTests.Count) {
    Write-Status "Showing recent logs for troubleshooting..."
    
    if ($Service) {
        Get-ContainerLogs $Service 100
    } else {
        foreach ($container in @("backend", "frontend", "nginx")) {
            Write-Info "=== $container logs ==="
            Get-ContainerLogs $container 20
            Write-Host ""
        }
    }
}

# Volume inspection
Write-Status "Checking volumes..."
try {
    $volumes = docker volume ls --filter name=app-builder-201 --format "table {{.Name}}\t{{.Driver}}"
    Write-Host $volumes
} catch {
    Write-Warning "Could not list volumes"
}

# Port bindings
Write-Status "Checking port bindings..."
try {
    $ports = docker-compose -f docker-compose.prod.yml port nginx 80 2>$null
    if ($ports) {
        Write-Success "Nginx port 80 is bound to: $ports"
    } else {
        Write-Warning "Nginx port 80 is not bound"
    }
} catch {
    Write-Warning "Could not check port bindings"
}

# Summary
Write-Status "=== TROUBLESHOOTING SUMMARY ===" "Magenta"

if ($healthyContainers -eq $totalContainers -and $passedTests -eq $connectivityTests.Count -and $dbHealthy -and $redisHealthy) {
    Write-Success "All systems appear to be working correctly!"
} else {
    Write-Warning "Issues detected:"
    
    if ($healthyContainers -lt $totalContainers) {
        Write-Error "Some containers are unhealthy"
    }
    
    if ($passedTests -lt $connectivityTests.Count) {
        Write-Error "Some connectivity tests failed"
    }
    
    if (-not $dbHealthy) {
        Write-Error "Database connection issues"
    }
    
    if (-not $redisHealthy) {
        Write-Error "Redis connection issues"
    }
    
    if (-not $staticFilesOk) {
        Write-Error "Static files issues"
    }
    
    if ($FixIssues) {
        Fix-CommonIssues
        Write-Info "Re-run this script to verify fixes"
    } else {
        Write-Info "Run with -FixIssues to attempt automatic fixes"
    }
}

Write-Info "Useful commands for further troubleshooting:"
Write-Host "  📋 All logs: docker-compose -f docker-compose.prod.yml logs -f" -ForegroundColor Yellow
Write-Host "  🔍 Specific service: docker-compose -f docker-compose.prod.yml logs -f [service]" -ForegroundColor Yellow
Write-Host "  🔄 Restart all: docker-compose -f docker-compose.prod.yml restart" -ForegroundColor Yellow
Write-Host "  🛠️  Shell access: docker-compose -f docker-compose.prod.yml exec [service] bash" -ForegroundColor Yellow
Write-Host "  🧹 Clean restart: docker-compose -f docker-compose.prod.yml down && docker-compose -f docker-compose.prod.yml up -d" -ForegroundColor Yellow
