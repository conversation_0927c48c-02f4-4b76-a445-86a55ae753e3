/**
 * Test script to verify the diagnostic widget is working correctly
 */

const puppeteer = require('puppeteer');

async function testDiagnosticWidget() {
  console.log('🔍 Testing Diagnostic Widget...');
  console.log('================================');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false, // Show browser for visual verification
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Navigate to main app
    console.log('📱 Loading main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for app to load
    await page.waitForFunction(
      () => window.__APP_LOADED__ === true,
      { timeout: 10000 }
    );

    // Check if diagnostic widget exists
    console.log('🔍 Checking for diagnostic widget...');
    const widgetExists = await page.evaluate(() => {
      return !!document.getElementById('react-diagnostic-widget');
    });

    console.log(`Diagnostic Widget Present: ${widgetExists ? '✅' : '❌'}`);

    if (widgetExists) {
      // Test widget functionality
      console.log('🧪 Testing widget functionality...');
      
      const widgetInfo = await page.evaluate(() => {
        const widget = document.getElementById('react-diagnostic-widget');
        return {
          text: widget.textContent,
          title: widget.title,
          backgroundColor: window.getComputedStyle(widget).backgroundColor,
          visible: widget.offsetWidth > 0 && widget.offsetHeight > 0
        };
      });

      console.log(`Widget Text: ${widgetInfo.text}`);
      console.log(`Widget Title: ${widgetInfo.title}`);
      console.log(`Widget Color: ${widgetInfo.backgroundColor}`);
      console.log(`Widget Visible: ${widgetInfo.visible ? '✅' : '❌'}`);

      // Click the widget to test console output
      console.log('🖱️ Clicking diagnostic widget...');
      
      // Capture console messages
      const consoleMessages = [];
      page.on('console', msg => {
        if (msg.text().includes('React Diagnostics')) {
          consoleMessages.push(msg.text());
        }
      });

      // Handle alert dialog
      page.on('dialog', async dialog => {
        console.log(`Alert Message: ${dialog.message()}`);
        await dialog.accept();
      });

      await page.click('#react-diagnostic-widget');
      
      // Wait a moment for console output
      await page.waitForTimeout(1000);

      console.log(`Console Messages Captured: ${consoleMessages.length}`);
      if (consoleMessages.length > 0) {
        console.log('Console Output:', consoleMessages[0]);
      }
    }

    // Test comprehensive diagnostics page
    console.log('\n🧪 Testing comprehensive diagnostics page...');
    await page.goto('http://localhost:3000/comprehensive-react-diagnostics.html', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for diagnostics to run
    await page.waitForTimeout(5000);

    const diagnosticResults = await page.evaluate(() => {
      const summary = document.getElementById('summary');
      const logContainer = document.getElementById('diagnostic-log');
      
      return {
        summaryText: summary ? summary.textContent.trim() : 'No summary found',
        logText: logContainer ? logContainer.textContent : 'No log found',
        logLength: logContainer ? logContainer.textContent.length : 0
      };
    });

    console.log(`Diagnostic Summary: ${diagnosticResults.summaryText}`);
    console.log(`Log Length: ${diagnosticResults.logLength} characters`);
    
    // Check for specific success indicators
    const hasSuccessIndicators = diagnosticResults.logText.includes('✅') || 
                                 diagnosticResults.summaryText.includes('PASSED');
    
    console.log(`Success Indicators Found: ${hasSuccessIndicators ? '✅' : '❌'}`);

    // Overall assessment
    console.log('\n📊 Test Summary');
    console.log('================');
    
    const tests = [
      widgetExists,
      widgetInfo?.visible || false,
      diagnosticResults.logLength > 0,
      hasSuccessIndicators
    ];

    const passedTests = tests.filter(test => test).length;
    const totalTests = tests.length;

    console.log(`Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`Overall Status: ${passedTests === totalTests ? '✅ ALL TESTS PASSED' : '⚠️ SOME ISSUES FOUND'}`);

    if (passedTests === totalTests) {
      console.log('\n🎉 Diagnostic tools are working correctly!');
      console.log('✅ Widget is visible and functional');
      console.log('✅ Comprehensive diagnostics are running');
      console.log('✅ Success indicators are present');
    } else {
      console.log('\n⚠️ Some diagnostic issues found:');
      if (!widgetExists) console.log('   ❌ Diagnostic widget not found');
      if (!widgetInfo?.visible) console.log('   ❌ Widget not visible');
      if (diagnosticResults.logLength === 0) console.log('   ❌ No diagnostic log generated');
      if (!hasSuccessIndicators) console.log('   ❌ No success indicators found');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testDiagnosticWidget().catch(console.error);
