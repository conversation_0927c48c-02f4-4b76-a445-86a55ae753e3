/**
 * Test script to verify diagnostic improvements work correctly
 */

const puppeteer = require('puppeteer');

async function testDiagnosticImprovements() {
  console.log('🔍 Testing Diagnostic Improvements...');
  console.log('====================================');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false, // Show browser for visual verification
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture console messages
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`[${msg.type()}] ${msg.text()}`);
    });

    // Test 1: Main application first
    console.log('📱 Testing main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for app to load
    await page.waitForFunction(
      () => window.__APP_LOADED__ === true,
      { timeout: 10000 }
    );

    // Get main app status
    const mainAppStatus = await page.evaluate(() => {
      return {
        reactAvailable: typeof window.React !== 'undefined',
        reactVersion: window.React?.version || null,
        appLoaded: window.__APP_LOADED__,
        rootExists: !!document.getElementById('root'),
        rootHasContent: document.getElementById('root')?.innerHTML?.length > 0,
        rootContentLength: document.getElementById('root')?.innerHTML?.length || 0,
        cssLinksCount: document.querySelectorAll('link[rel="stylesheet"]').length,
        cssFiles: Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => link.href.split('/').pop())
      };
    });

    console.log('\n📊 Main App Status:');
    console.log(`  React Available: ${mainAppStatus.reactAvailable ? '✅' : '❌'} (${mainAppStatus.reactVersion})`);
    console.log(`  App Loaded: ${mainAppStatus.appLoaded ? '✅' : '❌'}`);
    console.log(`  Root Element: ${mainAppStatus.rootExists ? '✅' : '❌'}`);
    console.log(`  Root Content: ${mainAppStatus.rootHasContent ? '✅' : '❌'} (${mainAppStatus.rootContentLength} chars)`);
    console.log(`  CSS Links: ${mainAppStatus.cssLinksCount} ${mainAppStatus.cssLinksCount > 0 ? '✅' : '❌'}`);
    console.log(`  CSS Files: ${mainAppStatus.cssFiles.join(', ')}`);

    // Test 2: Comprehensive diagnostics page
    console.log('\n🧪 Testing comprehensive diagnostics page...');
    await page.goto('http://localhost:3000/comprehensive-react-diagnostics.html', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for diagnostics to complete
    await page.waitForTimeout(8000); // Give more time for iframe connection

    const diagnosticResults = await page.evaluate(() => {
      const summary = document.getElementById('summary');
      const timingResults = document.getElementById('timing-results');
      const reactStatus = document.getElementById('react-status');
      const cssStatus = document.getElementById('css-status');
      const logContainer = document.getElementById('diagnostic-log');
      
      return {
        summaryText: summary ? summary.textContent.trim() : 'No summary found',
        timingText: timingResults ? timingResults.textContent : 'No timing results',
        reactText: reactStatus ? reactStatus.textContent : 'No react status',
        cssText: cssStatus ? cssStatus.textContent : 'No CSS status',
        logText: logContainer ? logContainer.textContent : 'No log found',
        logLength: logContainer ? logContainer.textContent.length : 0,
        hasRootElementPass: timingResults ? timingResults.textContent.includes('Root Element Content') && timingResults.textContent.includes('✅') : false,
        hasCSSLinksPass: cssStatus ? cssStatus.textContent.includes('CSS Links') && cssStatus.textContent.includes('✅') : false
      };
    });

    console.log('\n📋 Diagnostic Results:');
    console.log(`  Summary: ${diagnosticResults.summaryText}`);
    console.log(`  Log Length: ${diagnosticResults.logLength} characters`);
    console.log(`  Root Element Test: ${diagnosticResults.hasRootElementPass ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  CSS Links Test: ${diagnosticResults.hasCSSLinksPass ? '✅ PASS' : '❌ FAIL'}`);

    // Check for specific improvements
    const improvements = {
      rootElementDetected: diagnosticResults.hasRootElementPass,
      cssLinksDetected: diagnosticResults.hasCSSLinksPass,
      logGenerated: diagnosticResults.logLength > 0,
      contextConnection: diagnosticResults.logText.includes('Successfully connected to main app') || 
                        diagnosticResults.logText.includes('Using opener window context') ||
                        diagnosticResults.logText.includes('Using parent window context')
    };

    console.log('\n🔧 Improvement Analysis:');
    console.log(`  Root Element Detection: ${improvements.rootElementDetected ? '✅ FIXED' : '❌ STILL FAILING'}`);
    console.log(`  CSS Links Detection: ${improvements.cssLinksDetected ? '✅ FIXED' : '❌ STILL FAILING'}`);
    console.log(`  Context Connection: ${improvements.contextConnection ? '✅ WORKING' : '❌ NOT WORKING'}`);
    console.log(`  Log Generation: ${improvements.logGenerated ? '✅ WORKING' : '❌ NOT WORKING'}`);

    // Show relevant log messages
    const relevantLogs = consoleMessages.filter(msg => 
      msg.includes('Successfully connected') || 
      msg.includes('main app') ||
      msg.includes('Root Element') ||
      msg.includes('CSS Links') ||
      msg.includes('✅') || 
      msg.includes('❌')
    );

    if (relevantLogs.length > 0) {
      console.log('\n📝 Relevant Log Messages:');
      relevantLogs.slice(0, 10).forEach(msg => console.log(`   ${msg}`));
    }

    // Overall assessment
    console.log('\n🎯 Overall Assessment:');
    const fixedIssues = Object.values(improvements).filter(fixed => fixed).length;
    const totalIssues = Object.keys(improvements).length;

    console.log(`Issues Fixed: ${fixedIssues}/${totalIssues}`);
    
    if (fixedIssues === totalIssues) {
      console.log('🎉 ALL DIAGNOSTIC ISSUES FIXED!');
      console.log('✅ Root element is now properly detected');
      console.log('✅ CSS links are now properly detected');
      console.log('✅ Context connection is working');
      console.log('✅ Diagnostic logging is functional');
    } else {
      console.log('⚠️ Some diagnostic issues remain:');
      if (!improvements.rootElementDetected) console.log('   ❌ Root element detection still failing');
      if (!improvements.cssLinksDetected) console.log('   ❌ CSS links detection still failing');
      if (!improvements.contextConnection) console.log('   ❌ Context connection not working');
      if (!improvements.logGenerated) console.log('   ❌ Log generation not working');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testDiagnosticImprovements().catch(console.error);
