"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1937],{

/***/ 11937:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(70572);





var _templateObject, _templateObject2, _templateObject3, _templateObject4;
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Testing Tools Component
 * 
 * Comprehensive testing capabilities including component testing, layout validation,
 * and accessibility compliance checks integrated into the App Builder.
 */





var Title = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_7__/* .Select */ .l6.Option;

// Styled Components
var TestingContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  padding: 16px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"])));
var TestResultCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  \n  .ant-card-head {\n    background: ", ";\n  }\n"])), function (props) {
  switch (props.status) {
    case 'passed':
      return '#f6ffed';
    case 'failed':
      return '#fff2f0';
    case 'warning':
      return '#fffbe6';
    default:
      return '#fafafa';
  }
});
var TestMetrics = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 24px;\n"])));
var TestProgress = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  margin: 16px 0;\n"])));

// Test Types Configuration
var TEST_TYPES = {
  COMPONENT: {
    id: 'component',
    name: 'Component Tests',
    description: 'Test individual component functionality and props',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null),
    color: '#1890ff'
  },
  LAYOUT: {
    id: 'layout',
    name: 'Layout Validation',
    description: 'Validate responsive layouts and positioning',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DesktopOutlined */ .zlw, null),
    color: '#52c41a'
  },
  ACCESSIBILITY: {
    id: 'accessibility',
    name: 'Accessibility Tests',
    description: 'WCAG 2.1 AA compliance and screen reader support',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SafetyOutlined */ .lMf, null),
    color: '#722ed1'
  },
  PERFORMANCE: {
    id: 'performance',
    name: 'Performance Tests',
    description: 'Render performance and optimization checks',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ThunderboltOutlined */ .CwG, null),
    color: '#fa8c16'
  },
  RESPONSIVE: {
    id: 'responsive',
    name: 'Responsive Tests',
    description: 'Cross-device and viewport testing',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .MobileOutlined */ .jHj, null),
    color: '#eb2f96'
  }
};

// Mock test results for demonstration
var generateMockTestResults = function generateMockTestResults(testType, components) {
  var results = [];
  var testCount = Math.floor(Math.random() * 10) + 5;
  for (var i = 0; i < testCount; i++) {
    var _TEST_TYPES$testType$;
    var status = Math.random() > 0.8 ? 'failed' : Math.random() > 0.9 ? 'warning' : 'passed';
    results.push({
      id: "".concat(testType, "-").concat(i),
      name: "".concat(((_TEST_TYPES$testType$ = TEST_TYPES[testType.toUpperCase()]) === null || _TEST_TYPES$testType$ === void 0 ? void 0 : _TEST_TYPES$testType$.name) || testType, " Test ").concat(i + 1),
      status: status,
      duration: Math.floor(Math.random() * 1000) + 100,
      message: status === 'failed' ? 'Test assertion failed' : status === 'warning' ? 'Performance threshold exceeded' : 'Test passed successfully',
      details: {
        assertions: Math.floor(Math.random() * 10) + 1,
        coverage: Math.floor(Math.random() * 30) + 70
      }
    });
  }
  return results;
};

/**
 * TestingTools Component
 */
var TestingTools = function TestingTools(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onTestComplete = _ref.onTestComplete,
    onTestStart = _ref.onTestStart,
    _ref$enabledTests = _ref.enabledTests,
    enabledTests = _ref$enabledTests === void 0 ? Object.keys(TEST_TYPES) : _ref$enabledTests,
    _ref$autoRun = _ref.autoRun,
    autoRun = _ref$autoRun === void 0 ? false : _ref$autoRun,
    _ref$showMetrics = _ref.showMetrics,
    showMetrics = _ref$showMetrics === void 0 ? true : _ref$showMetrics,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({}),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    testResults = _useState4[0],
    setTestResults = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(new Set()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    runningTests = _useState6[0],
    setRunningTests = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({}),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    testProgress = _useState8[0],
    setTestProgress = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      includePerformance: true,
      includeAccessibility: true,
      includeResponsive: true,
      strictMode: false,
      generateReport: true
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    testSettings = _useState0[0],
    setTestSettings = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState1, 2),
    showSettings = _useState10[0],
    setShowSettings = _useState10[1];

  // Computed metrics
  var testMetrics = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var allResults = Object.values(testResults).flat();
    var total = allResults.length;
    var passed = allResults.filter(function (r) {
      return r.status === 'passed';
    }).length;
    var failed = allResults.filter(function (r) {
      return r.status === 'failed';
    }).length;
    var warnings = allResults.filter(function (r) {
      return r.status === 'warning';
    }).length;
    return {
      total: total,
      passed: passed,
      failed: failed,
      warnings: warnings,
      passRate: total > 0 ? Math.round(passed / total * 100) : 0,
      avgDuration: total > 0 ? Math.round(allResults.reduce(function (sum, r) {
        return sum + r.duration;
      }, 0) / total) : 0
    };
  }, [testResults]);

  // Run tests for a specific type
  var runTests = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee(testType) {
      var _TEST_TYPES$testType$2, progressInterval, results, metrics, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!runningTests.has(testType)) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            setRunningTests(function (prev) {
              return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [testType]));
            });
            setTestProgress(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, 0));
            });
            if (onTestStart) {
              onTestStart(testType);
            }
            _context.prev = 2;
            // Simulate test execution with progress updates
            progressInterval = setInterval(function () {
              setTestProgress(function (prev) {
                return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, Math.min((prev[testType] || 0) + Math.random() * 20, 95)));
              });
            }, 200); // Simulate async test execution
            _context.next = 3;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 2000 + Math.random() * 3000);
            });
          case 3:
            clearInterval(progressInterval);
            setTestProgress(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, 100));
            });

            // Generate mock results
            results = generateMockTestResults(testType, components);
            setTestResults(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, results));
            });
            if (onTestComplete) {
              onTestComplete(testType, results);
            }

            // Show notification
            metrics = {
              total: results.length,
              passed: results.filter(function (r) {
                return r.status === 'passed';
              }).length,
              failed: results.filter(function (r) {
                return r.status === 'failed';
              }).length
            };
            antd__WEBPACK_IMPORTED_MODULE_7__/* .notification */ .Ew.success({
              message: "".concat((_TEST_TYPES$testType$2 = TEST_TYPES[testType.toUpperCase()]) === null || _TEST_TYPES$testType$2 === void 0 ? void 0 : _TEST_TYPES$testType$2.name, " Complete"),
              description: "".concat(metrics.passed, "/").concat(metrics.total, " tests passed"),
              duration: 3
            });
            _context.next = 5;
            break;
          case 4:
            _context.prev = 4;
            _t = _context["catch"](2);
            antd__WEBPACK_IMPORTED_MODULE_7__/* .notification */ .Ew.error({
              message: 'Test Execution Failed',
              description: _t.message,
              duration: 5
            });
          case 5:
            _context.prev = 5;
            setRunningTests(function (prev) {
              var newSet = new Set(prev);
              newSet["delete"](testType);
              return newSet;
            });
            return _context.finish(5);
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[2, 4, 5, 6]]);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), [runningTests, components, onTestStart, onTestComplete]);

  // Run all enabled tests
  var runAllTests = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee2() {
    var _iterator, _step, testType, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _iterator = _createForOfIteratorHelper(enabledTests);
          _context2.prev = 1;
          _iterator.s();
        case 2:
          if ((_step = _iterator.n()).done) {
            _context2.next = 4;
            break;
          }
          testType = _step.value;
          if (runningTests.has(testType)) {
            _context2.next = 3;
            break;
          }
          _context2.next = 3;
          return runTests(testType);
        case 3:
          _context2.next = 2;
          break;
        case 4:
          _context2.next = 6;
          break;
        case 5:
          _context2.prev = 5;
          _t2 = _context2["catch"](1);
          _iterator.e(_t2);
        case 6:
          _context2.prev = 6;
          _iterator.f();
          return _context2.finish(6);
        case 7:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 5, 6, 7]]);
  })), [enabledTests, runTests, runningTests]);

  // Clear test results
  var clearResults = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function () {
    setTestResults({});
    setTestProgress({});
  }, []);

  // Auto-run tests when components change
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (autoRun && components.length > 0) {
      var timer = setTimeout(function () {
        runAllTests();
      }, 1000);
      return function () {
        return clearTimeout(timer);
      };
    }
  }, [autoRun, components, runAllTests]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestingContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
    justify: "space-between",
    align: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 4,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, {
    style: {
      marginRight: 8,
      color: '#1890ff'
    }
  }), "Testing Tools")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SettingOutlined */ .JO7, null),
    onClick: function onClick() {
      return setShowSettings(true);
    }
  }, "Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
    onClick: runAllTests,
    loading: runningTests.size > 0,
    disabled: components.length === 0
  }, "Run All Tests"))))), showMetrics && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestMetrics, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Total Tests",
    value: testMetrics.total,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Pass Rate",
    value: testMetrics.passRate,
    suffix: "%",
    valueStyle: {
      color: testMetrics.passRate >= 80 ? '#3f8600' : '#cf1322'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckCircleOutlined */ .hWy, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Failed Tests",
    value: testMetrics.failed,
    valueStyle: {
      color: testMetrics.failed > 0 ? '#cf1322' : '#3f8600'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CloseCircleOutlined */ .bBN, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Avg Duration",
    value: testMetrics.avgDuration,
    suffix: "ms",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ThunderboltOutlined */ .CwG, null)
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
    tab: "Overview",
    key: "overview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
    gutter: [16, 16]
  }, enabledTests.map(function (testType) {
    var config = TEST_TYPES[testType.toUpperCase()];
    var results = testResults[testType] || [];
    var isRunning = runningTests.has(testType);
    var progress = testProgress[testType] || 0;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
      xs: 24,
      sm: 12,
      lg: 8,
      key: testType
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestResultCard, {
      size: "small",
      status: results.length > 0 ? results.some(function (r) {
        return r.status === 'failed';
      }) ? 'failed' : results.some(function (r) {
        return r.status === 'warning';
      }) ? 'warning' : 'passed' : 'default',
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, config === null || config === void 0 ? void 0 : config.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, config === null || config === void 0 ? void 0 : config.name), results.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Badge */ .Ex, {
        count: results.filter(function (r) {
          return r.status === 'passed';
        }).length,
        style: {
          backgroundColor: '#52c41a'
        }
      })),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        size: "small",
        type: "primary",
        icon: isRunning ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PauseCircleOutlined */ .Xcy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
        onClick: function onClick() {
          return runTests(testType);
        },
        loading: isRunning,
        disabled: components.length === 0
      }, isRunning ? 'Running' : 'Run')
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Paragraph, {
      style: {
        margin: 0,
        marginBottom: 12
      }
    }, config === null || config === void 0 ? void 0 : config.description), isRunning && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestProgress, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Progress */ .ke, {
      percent: Math.round(progress),
      size: "small",
      status: progress < 100 ? 'active' : 'success'
    })), results.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
      type: "secondary"
    }, results.filter(function (r) {
      return r.status === 'passed';
    }).length, "/", results.length, " tests passed"))));
  }))), enabledTests.map(function (testType) {
    var config = TEST_TYPES[testType.toUpperCase()];
    var results = testResults[testType] || [];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
      tab: config === null || config === void 0 ? void 0 : config.name,
      key: testType
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
      style: {
        marginBottom: 16
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
      justify: "space-between",
      align: "middle"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
      level: 5
    }, config === null || config === void 0 ? void 0 : config.icon, " ", config === null || config === void 0 ? void 0 : config.name)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ReloadOutlined */ .KF4, null),
      onClick: function onClick() {
        return runTests(testType);
      },
      loading: runningTests.has(testType),
      disabled: components.length === 0
    }, "Re-run"), results.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DownloadOutlined */ .jsW, null)
    }, "Export Results"))))), runningTests.has(testType) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestProgress, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Progress */ .ke, {
      percent: Math.round(testProgress[testType] || 0),
      status: "active"
    })), results.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .List */ .B8, {
      dataSource: results,
      renderItem: function renderItem(result) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .List */ .B8.Item.Meta, {
          avatar: result.status === 'passed' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckCircleOutlined */ .hWy, {
            style: {
              color: '#52c41a'
            }
          }) : result.status === 'failed' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CloseCircleOutlined */ .bBN, {
            style: {
              color: '#ff4d4f'
            }
          }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExclamationCircleOutlined */ .G2i, {
            style: {
              color: '#faad14'
            }
          }),
          title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, result.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tag */ .vw, {
            color: result.status === 'passed' ? 'green' : result.status === 'failed' ? 'red' : 'orange'
          }, result.status.toUpperCase())),
          description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, null, result.message), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
            type: "secondary"
          }, "Duration: ", result.duration, "ms | Assertions: ", result.details.assertions, " | Coverage: ", result.details.coverage, "%"))
        }));
      }
    }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
      message: "No test results",
      description: "Run ".concat(config === null || config === void 0 ? void 0 : config.name, " to see results here."),
      type: "info",
      showIcon: true
    }));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Modal */ .aF, {
    title: "Test Settings",
    open: showSettings,
    onCancel: function onCancel() {
      return setShowSettings(false);
    },
    footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      key: "cancel",
      onClick: function onClick() {
        return setShowSettings(false);
      }
    }, "Cancel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      key: "save",
      type: "primary",
      onClick: function onClick() {
        return setShowSettings(false);
      }
    }, "Save Settings")]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    strong: true
  }, "Test Options"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
    gutter: [16, 8]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.includePerformance,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          includePerformance: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Include Performance Tests")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.includeAccessibility,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          includeAccessibility: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Include Accessibility Tests")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.includeResponsive,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          includeResponsive: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Include Responsive Tests")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.strictMode,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          strictMode: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Strict Mode (Fail on Warnings)")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.generateReport,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          generateReport: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Generate Test Reports"))))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestingTools);

/***/ })

}]);