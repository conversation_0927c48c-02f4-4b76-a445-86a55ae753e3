<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Global Test - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background: #f0f9ff;
            border-color: #10b981;
            color: #065f46;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .info {
            background: #f0f9ff;
            border-color: #3b82f6;
            color: #1e40af;
        }
        .loading {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #92400e;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #10b981; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 React Global Test</h1>
        <p>Testing React and ReactDOM global availability in development mode.</p>
        
        <div id="test-results">
            <div class="test-result loading">
                <span class="status-indicator status-pending"></span>
                <strong>Initializing tests...</strong>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="runTests()">🔄 Run Tests</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
            <button onclick="window.location.href='/'">🏠 Back to App</button>
        </div>
        
        <div id="console-output" style="margin-top: 20px;">
            <h3>Console Output:</h3>
            <pre id="console-log"></pre>
        </div>
    </div>

    <script>
        let testResults = [];
        let consoleOutput = [];

        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            consoleOutput.push(`[LOG] ${args.join(' ')}`);
            updateConsoleOutput();
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            consoleOutput.push(`[ERROR] ${args.join(' ')}`);
            updateConsoleOutput();
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            consoleOutput.push(`[WARN] ${args.join(' ')}`);
            updateConsoleOutput();
            originalWarn.apply(console, args);
        };

        function updateConsoleOutput() {
            const consoleLog = document.getElementById('console-log');
            consoleLog.textContent = consoleOutput.slice(-20).join('\n');
        }

        function addTestResult(name, passed, message, details = '') {
            testResults.push({ name, passed, message, details });
            updateTestResults();
        }

        function updateTestResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = '';

            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.passed ? 'success' : 'error'}`;
                
                const statusClass = result.passed ? 'status-pass' : 'status-fail';
                const icon = result.passed ? '✅' : '❌';
                
                div.innerHTML = `
                    <span class="status-indicator ${statusClass}"></span>
                    <strong>${result.name}</strong>: ${result.message}
                    ${result.details ? `<pre>${result.details}</pre>` : ''}
                `;
                
                container.appendChild(div);
            });
        }

        function clearResults() {
            testResults = [];
            consoleOutput = [];
            updateTestResults();
            updateConsoleOutput();
        }

        async function runTests() {
            clearResults();
            
            console.log('🚀 Starting React Global Tests...');
            
            // Test 1: React Global Availability
            console.log('Test 1: Checking React global availability...');
            if (typeof window.React !== 'undefined') {
                const version = window.React.version || 'Unknown';
                addTestResult(
                    'React Global Availability',
                    true,
                    `React is available globally (v${version})`,
                    `Type: ${typeof window.React}\nVersion: ${version}\nKeys: ${Object.keys(window.React).slice(0, 10).join(', ')}...`
                );
            } else {
                addTestResult(
                    'React Global Availability',
                    false,
                    'React is not available in global scope'
                );
            }

            // Test 2: ReactDOM Global Availability
            console.log('Test 2: Checking ReactDOM global availability...');
            if (typeof window.ReactDOM !== 'undefined') {
                addTestResult(
                    'ReactDOM Global Availability',
                    true,
                    'ReactDOM is available globally',
                    `Type: ${typeof window.ReactDOM}\nKeys: ${Object.keys(window.ReactDOM).join(', ')}`
                );
            } else {
                addTestResult(
                    'ReactDOM Global Availability',
                    false,
                    'ReactDOM is not available in global scope'
                );
            }

            // Test 3: Bundle Loading Check
            console.log('Test 3: Checking bundle loading...');
            const scripts = Array.from(document.querySelectorAll('script[src]'));
            const mainScripts = scripts.filter(script => 
                script.src.includes('main') || 
                script.src.includes('bundle') || 
                script.src.includes('chunk')
            );

            if (mainScripts.length > 0) {
                const scriptList = mainScripts.map(s => s.src.split('/').pop()).join('\n');
                addTestResult(
                    'Bundle Loading',
                    true,
                    `Found ${mainScripts.length} bundle scripts`,
                    scriptList
                );
            } else {
                addTestResult(
                    'Bundle Loading',
                    false,
                    'No bundle scripts found'
                );
            }

            // Test 4: App Root Element
            console.log('Test 4: Checking app root element...');
            const rootElement = document.getElementById('root');
            if (rootElement) {
                const hasContent = rootElement.innerHTML.trim().length > 0;
                addTestResult(
                    'App Root Element',
                    true,
                    `Root element found ${hasContent ? 'with content' : '(empty)'}`,
                    `Element: ${rootElement.tagName}\nContent length: ${rootElement.innerHTML.length} chars`
                );
            } else {
                addTestResult(
                    'App Root Element',
                    false,
                    'Root element not found'
                );
            }

            // Test 5: Development Mode Check
            console.log('Test 5: Checking development mode...');
            const isDev = process?.env?.NODE_ENV === 'development' || 
                         window.location.hostname === 'localhost' ||
                         window.location.hostname === '127.0.0.1';
            
            addTestResult(
                'Development Mode',
                isDev,
                isDev ? 'Running in development mode' : 'Not in development mode',
                `Hostname: ${window.location.hostname}\nPort: ${window.location.port}`
            );

            console.log('✅ All tests completed!');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000); // Wait 1 second for everything to load
        });
    </script>
</body>
</html>
