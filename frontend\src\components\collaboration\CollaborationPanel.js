import React, { useState, useEffect } from 'react';
import { Card, List, Avatar, Badge, Button, Input, Space, Typography, Tooltip, Divider } from 'antd';
import {
  UserOutlined,
  MessageOutlined,
  SendOutlined,
  TeamOutlined,
  EyeOutlined,
  EditOutlined,
  CloseOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Text, Title } = Typography;
const { TextArea } = Input;

const CollaborationContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
`;

const UserList = styled.div`
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 16px;
`;

const CommentSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
`;

const CommentList = styled.div`
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  max-height: 300px;
`;

const CommentInput = styled.div`
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
`;

const OnlineIndicator = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => props.online ? '#52c41a' : '#d9d9d9'};
  display: inline-block;
  margin-right: 8px;
`;

const CollaborationPanel = ({
  collaborators = [],
  comments = [],
  currentUser = null,
  onAddComment = () => {},
  onResolveComment = () => {},
  onDeleteComment = () => {},
  onUserClick = () => {},
  isConnected = false
}) => {
  const [newComment, setNewComment] = useState('');
  const [activeTab, setActiveTab] = useState('users');

  // Mock data if no props provided
  const mockCollaborators = collaborators.length > 0 ? collaborators : [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: null,
      online: true,
      lastSeen: new Date(),
      cursor: { x: 100, y: 200 },
      currentComponent: 'button-1'
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: null,
      online: false,
      lastSeen: new Date(Date.now() - 300000), // 5 minutes ago
      cursor: null,
      currentComponent: null
    }
  ];

  const mockComments = comments.length > 0 ? comments : [
    {
      id: 1,
      author: 'John Doe',
      content: 'This button needs to be larger for better accessibility.',
      timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      componentId: 'button-1',
      resolved: false,
      replies: []
    },
    {
      id: 2,
      author: 'Jane Smith',
      content: 'Great work on the layout! The spacing looks perfect.',
      timestamp: new Date(Date.now() - 1800000), // 30 minutes ago
      componentId: null,
      resolved: true,
      replies: [
        {
          id: 3,
          author: 'John Doe',
          content: 'Thanks! I spent a lot of time on the grid system.',
          timestamp: new Date(Date.now() - 1200000) // 20 minutes ago
        }
      ]
    }
  ];

  const handleAddComment = () => {
    if (newComment.trim()) {
      const comment = {
        id: Date.now(),
        author: currentUser?.name || 'Anonymous',
        content: newComment.trim(),
        timestamp: new Date(),
        componentId: null,
        resolved: false,
        replies: []
      };
      onAddComment(comment);
      setNewComment('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleAddComment();
    }
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const renderUserList = () => (
    <UserList>
      <Title level={5}>
        <TeamOutlined /> Collaborators ({mockCollaborators.length})
      </Title>
      <List
        size="small"
        dataSource={mockCollaborators}
        renderItem={(user) => (
          <List.Item
            style={{ padding: '8px 0', cursor: 'pointer' }}
            onClick={() => onUserClick(user)}
          >
            <List.Item.Meta
              avatar={
                <Badge dot={user.online} color={user.online ? 'green' : 'default'}>
                  <Avatar 
                    size="small" 
                    src={user.avatar} 
                    icon={<UserOutlined />}
                  />
                </Badge>
              }
              title={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <OnlineIndicator online={user.online} />
                  <Text strong={user.online}>{user.name}</Text>
                </div>
              }
              description={
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {user.online ? (
                    user.currentComponent ? (
                      <>
                        <EditOutlined style={{ marginRight: 4 }} />
                        Editing {user.currentComponent}
                      </>
                    ) : (
                      <>
                        <EyeOutlined style={{ marginRight: 4 }} />
                        Viewing
                      </>
                    )
                  ) : (
                    `Last seen ${formatTime(user.lastSeen)}`
                  )}
                </Text>
              }
            />
          </List.Item>
        )}
      />
    </UserList>
  );

  const renderComments = () => (
    <CommentSection>
      <Title level={5}>
        <MessageOutlined /> Comments ({mockComments.length})
      </Title>
      <CommentList>
        {mockComments.map((comment) => (
          <Card
            key={comment.id}
            size="small"
            style={{ marginBottom: 12 }}
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  <Text strong>{comment.author}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {formatTime(comment.timestamp)}
                  </Text>
                </Space>
                <Space>
                  {!comment.resolved && (
                    <Button
                      type="text"
                      size="small"
                      onClick={() => onResolveComment(comment.id)}
                    >
                      Resolve
                    </Button>
                  )}
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => onDeleteComment(comment.id)}
                  />
                </Space>
              </div>
            }
          >
            <Text>{comment.content}</Text>
            {comment.componentId && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  Component: {comment.componentId}
                </Text>
              </div>
            )}
            {comment.resolved && (
              <div style={{ marginTop: 8 }}>
                <Text type="success" style={{ fontSize: '11px' }}>
                  ✓ Resolved
                </Text>
              </div>
            )}
            {comment.replies && comment.replies.length > 0 && (
              <div style={{ marginTop: 12, paddingLeft: 16, borderLeft: '2px solid #f0f0f0' }}>
                {comment.replies.map((reply) => (
                  <div key={reply.id} style={{ marginBottom: 8 }}>
                    <Space>
                      <Avatar size="small" icon={<UserOutlined />} />
                      <Text strong style={{ fontSize: '12px' }}>{reply.author}</Text>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {formatTime(reply.timestamp)}
                      </Text>
                    </Space>
                    <div style={{ marginTop: 4, fontSize: '12px' }}>
                      {reply.content}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        ))}
        {mockComments.length === 0 && (
          <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
            No comments yet. Start a conversation!
          </div>
        )}
      </CommentList>
      <CommentInput>
        <TextArea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Add a comment... (Ctrl+Enter to send)"
          rows={3}
          style={{ marginBottom: 8 }}
        />
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </Text>
          <Button
            type="primary"
            size="small"
            icon={<SendOutlined />}
            onClick={handleAddComment}
            disabled={!newComment.trim()}
          >
            Send
          </Button>
        </div>
      </CommentInput>
    </CommentSection>
  );

  return (
    <CollaborationContainer>
      <Card
        title="Collaboration"
        size="small"
        style={{ height: '100%' }}
        bodyStyle={{ padding: '12px', height: 'calc(100% - 57px)' }}
      >
        <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {renderUserList()}
          <Divider style={{ margin: '12px 0' }} />
          {renderComments()}
        </div>
      </Card>
    </CollaborationContainer>
  );
};

export default CollaborationPanel;
