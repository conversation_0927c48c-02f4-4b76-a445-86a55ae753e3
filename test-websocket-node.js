/**
 * Node.js WebSocket Test Script
 * Tests WebSocket connections to both backend and frontend proxy
 */

const WebSocket = require('ws');

// Test configuration
const TEST_CONFIG = {
    frontendProxyUrl: 'ws://localhost:3000',
    backendDirectUrl: 'ws://localhost:8000',
    testEndpoints: [
        '/ws/test/',
        '/ws/app_builder/',
        '/ws/simple/',
        '/ws/echo/'
    ],
    timeout: 5000
};

/**
 * Test WebSocket connection
 */
function testWebSocketConnection(url, description) {
    return new Promise((resolve) => {
        console.log(`\n🔌 Testing ${description}: ${url}`);
        
        const ws = new WebSocket(url);
        const startTime = Date.now();
        let resolved = false;
        
        const resolveTest = (result) => {
            if (!resolved) {
                resolved = true;
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                resolve(result);
            }
        };
        
        // Set timeout
        const timeout = setTimeout(() => {
            resolveTest({
                success: false,
                error: 'Connection timeout',
                duration: Date.now() - startTime
            });
        }, TEST_CONFIG.timeout);
        
        ws.on('open', () => {
            const duration = Date.now() - startTime;
            console.log(`✅ Connection successful in ${duration}ms`);
            
            // Send a test message
            ws.send(JSON.stringify({
                type: 'ping',
                message: 'Test message from Node.js',
                timestamp: Date.now()
            }));
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                console.log(`📨 Received message:`, message);
                
                clearTimeout(timeout);
                resolveTest({
                    success: true,
                    message: message,
                    duration: Date.now() - startTime
                });
            } catch (error) {
                console.log(`📨 Received raw message:`, data.toString());
                clearTimeout(timeout);
                resolveTest({
                    success: true,
                    message: data.toString(),
                    duration: Date.now() - startTime
                });
            }
        });
        
        ws.on('error', (error) => {
            console.log(`❌ Connection error:`, error.message);
            clearTimeout(timeout);
            resolveTest({
                success: false,
                error: error.message,
                duration: Date.now() - startTime
            });
        });
        
        ws.on('close', (code, reason) => {
            console.log(`🔌 Connection closed: code=${code}, reason=${reason}`);
            if (!resolved) {
                clearTimeout(timeout);
                resolveTest({
                    success: false,
                    error: `Connection closed: code=${code}`,
                    duration: Date.now() - startTime
                });
            }
        });
    });
}

/**
 * Test all endpoints
 */
async function testAllEndpoints() {
    console.log('🚀 Starting WebSocket Connection Tests...');
    console.log('=' * 50);
    
    const results = {
        backend: {},
        proxy: {}
    };
    
    // Test direct backend connections
    console.log('\n📡 Testing Direct Backend Connections:');
    for (const endpoint of TEST_CONFIG.testEndpoints) {
        const url = `${TEST_CONFIG.backendDirectUrl}${endpoint}`;
        const result = await testWebSocketConnection(url, `Backend ${endpoint}`);
        results.backend[endpoint] = result;
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Test proxy connections
    console.log('\n🔄 Testing Frontend Proxy Connections:');
    for (const endpoint of TEST_CONFIG.testEndpoints) {
        const url = `${TEST_CONFIG.frontendProxyUrl}${endpoint}`;
        const result = await testWebSocketConnection(url, `Proxy ${endpoint}`);
        results.proxy[endpoint] = result;
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Print summary
    console.log('\n' + '=' * 50);
    console.log('📊 Test Results Summary:');
    console.log('=' * 50);
    
    console.log('\n📡 Backend Direct Connections:');
    for (const [endpoint, result] of Object.entries(results.backend)) {
        const status = result.success ? '✅ PASS' : '❌ FAIL';
        const duration = result.duration ? `(${result.duration}ms)` : '';
        console.log(`  ${endpoint}: ${status} ${duration}`);
        if (!result.success) {
            console.log(`    Error: ${result.error}`);
        }
    }
    
    console.log('\n🔄 Frontend Proxy Connections:');
    for (const [endpoint, result] of Object.entries(results.proxy)) {
        const status = result.success ? '✅ PASS' : '❌ FAIL';
        const duration = result.duration ? `(${result.duration}ms)` : '';
        console.log(`  ${endpoint}: ${status} ${duration}`);
        if (!result.success) {
            console.log(`    Error: ${result.error}`);
        }
    }
    
    // Overall status
    const backendWorking = Object.values(results.backend).some(r => r.success);
    const proxyWorking = Object.values(results.proxy).some(r => r.success);
    
    console.log('\n🎯 Overall Status:');
    console.log(`  Backend: ${backendWorking ? '✅ Working' : '❌ Not Working'}`);
    console.log(`  Proxy: ${proxyWorking ? '✅ Working' : '❌ Not Working'}`);
    
    if (backendWorking && !proxyWorking) {
        console.log('\n💡 Diagnosis: Backend is working but proxy is failing.');
        console.log('   This suggests a proxy configuration issue.');
    } else if (!backendWorking && !proxyWorking) {
        console.log('\n💡 Diagnosis: Both backend and proxy are failing.');
        console.log('   This suggests the backend WebSocket server is not running.');
    } else if (backendWorking && proxyWorking) {
        console.log('\n💡 Diagnosis: Both backend and proxy are working correctly!');
    }
    
    return results;
}

// Run the tests
if (require.main === module) {
    testAllEndpoints()
        .then(() => {
            console.log('\n✅ Test completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Test failed:', error);
            process.exit(1);
        });
}

module.exports = { testWebSocketConnection, testAllEndpoints };
