from django.http import JsonResponse
from django.db import connection
import redis
import socket
import os
import time
import json

def health_check(request):
    """
    Health check endpoint for the application.
    Checks database connection, Redis connection, and system status.
    """
    start_time = time.time()
    
    # Check database connection
    db_status = check_database()
    
    # Check Redis connection if configured
    redis_status = check_redis()
    
    # Check system status
    system_status = check_system()
    
    # Overall status
    status = "healthy" if db_status["status"] == "ok" and system_status["status"] == "ok" else "unhealthy"
    
    # If Redis is configured, include it in the overall status
    if redis_status:
        status = "healthy" if status == "healthy" and redis_status["status"] == "ok" else "unhealthy"
    
    # Response data
    response_data = {
        "status": status,
        "timestamp": time.time(),
        "duration_ms": round((time.time() - start_time) * 1000, 2),
        "version": os.environ.get("APP_VERSION", "dev"),
        "checks": {
            "database": db_status,
            "system": system_status
        }
    }
    
    # Include Redis status if available
    if redis_status:
        response_data["checks"]["redis"] = redis_status
    
    # Return response with appropriate status code
    status_code = 200 if status == "healthy" else 503
    return JsonResponse(response_data, status=status_code)

def check_database():
    """Check database connection."""
    try:
        # Execute a simple query
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        return {
            "status": "ok" if result and result[0] == 1 else "error",
            "message": "Database connection successful"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Database connection failed: {str(e)}"
        }

def check_redis():
    """Check Redis connection if configured."""
    redis_url = os.environ.get("REDIS_URL")
    if not redis_url:
        return None

    try:
        # Parse Redis URL
        password = None
        if "://" in redis_url:
            # URL format: redis://[:password@]host[:port][/db-number]
            parts = redis_url.split("@")
            if len(parts) > 1:
                # Has password
                password_part = parts[0].split("://")[-1]
                if password_part.startswith(":"):
                    password = password_part[1:]
                else:
                    password = password_part
                host_port_db = parts[1]
            else:
                host_port_db = parts[0].split("://")[-1]

            host_port = host_port_db.split("/")[0]
            redis_host = host_port.split(":")[0]
            redis_port = int(host_port.split(":")[-1]) if ":" in host_port else 6379
        else:
            # Simple host:port format
            redis_host = redis_url.split(":")[0]
            redis_port = int(redis_url.split(":")[-1])

        # Connect to Redis
        r = redis.Redis(host=redis_host, port=redis_port, password=password, socket_timeout=2)
        r.ping()

        return {
            "status": "ok",
            "message": "Redis connection successful"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Redis connection failed: {str(e)}"
        }

def check_system():
    """Check system status."""
    try:
        # Get hostname
        hostname = socket.gethostname()
        
        # Get memory usage
        with open('/proc/meminfo', 'r') as f:
            mem_info = f.readlines()
        
        mem_total = None
        mem_free = None
        mem_available = None
        
        for line in mem_info:
            if 'MemTotal' in line:
                mem_total = int(line.split()[1])
            elif 'MemFree' in line:
                mem_free = int(line.split()[1])
            elif 'MemAvailable' in line:
                mem_available = int(line.split()[1])
        
        # Calculate memory usage percentage
        mem_usage = None
        if mem_total and mem_available:
            mem_usage = round(((mem_total - mem_available) / mem_total) * 100, 2)
        
        return {
            "status": "ok",
            "hostname": hostname,
            "memory": {
                "total_kb": mem_total,
                "free_kb": mem_free,
                "available_kb": mem_available,
                "usage_percent": mem_usage
            }
        }
    except Exception as e:
        return {
            "status": "ok",  # Still return ok as system checks are not critical
            "message": f"System check partial failure: {str(e)}"
        }
