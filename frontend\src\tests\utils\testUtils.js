/**
 * Test Utilities for App Builder Testing
 * 
 * Provides common utilities, mocks, and helpers for testing App Builder components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

// Import reducers
import appReducer from '../../store/slices/appSlice';
import uiReducer from '../../store/slices/uiSlice';
import templateReducer from '../../store/slices/templateSlice';

// Mock WebSocket
export const mockWebSocket = {
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1
};

// Mock APIs
export const mockApiServices = {
  templateService: {
    getTemplates: jest.fn(() => Promise.resolve({
      data: [
        {
          id: 1,
          name: '[Default] Hello World Starter',
          description: 'A simple starter template',
          app_category: 'other',
          components: {
            pages: [{
              name: 'home',
              components: [
                {
                  id: 'header-1',
                  type: 'header',
                  props: { title: 'Hello World!' }
                }
              ]
            }]
          },
          is_public: true
        }
      ]
    })),
    loadTemplate: jest.fn(() => Promise.resolve({ data: { success: true } })),
    saveTemplate: jest.fn(() => Promise.resolve({ data: { id: 2 } })),
    deleteTemplate: jest.fn(() => Promise.resolve({ data: { success: true } }))
  },
  exportService: {
    exportApp: jest.fn(() => Promise.resolve({
      data: {
        code: 'import React from "react";\n\nfunction App() {\n  return <div>Hello World!</div>;\n}\n\nexport default App;',
        format: 'react',
        files: {
          'App.jsx': 'import React from "react";\n\nfunction App() {\n  return <div>Hello World!</div>;\n}\n\nexport default App;',
          'package.json': '{"name": "my-app", "version": "1.0.0"}'
        }
      }
    })),
    validateCode: jest.fn(() => Promise.resolve({ data: { valid: true, errors: [] } }))
  },
  componentService: {
    getComponents: jest.fn(() => Promise.resolve({
      data: [
        { id: 1, type: 'header', name: 'Header', category: 'layout' },
        { id: 2, type: 'button', name: 'Button', category: 'input' },
        { id: 3, type: 'text', name: 'Text', category: 'content' }
      ]
    })),
    saveComponent: jest.fn(() => Promise.resolve({ data: { id: 4 } }))
  }
};

// Mock browser APIs
export const mockBrowserAPIs = () => {
  // Mock clipboard
  Object.assign(navigator, {
    clipboard: {
      writeText: jest.fn(() => Promise.resolve()),
      readText: jest.fn(() => Promise.resolve('mocked clipboard content'))
    }
  });

  // Mock file download
  global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
  global.URL.revokeObjectURL = jest.fn();

  // Mock WebSocket
  global.WebSocket = jest.fn(() => mockWebSocket);

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
  }));
};

// Test store factory
export const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      app: appReducer,
      ui: uiReducer,
      templates: templateReducer
    },
    preloadedState: {
      app: {
        components: [],
        selectedComponent: null,
        layouts: [],
        styles: {},
        isDirty: false,
        history: [],
        currentHistoryIndex: -1,
        ...initialState.app
      },
      ui: {
        sidebarOpen: true,
        currentView: 'builder',
        previewMode: false,
        theme: 'light',
        notifications: [],
        loading: false,
        ...initialState.ui
      },
      templates: {
        templates: [],
        loading: false,
        error: null,
        selectedTemplate: null,
        ...initialState.templates
      }
    }
  });
};

// Test wrapper component
export const TestWrapper = ({ children, store, router = true }) => {
  const content = router ? (
    <BrowserRouter>{children}</BrowserRouter>
  ) : children;

  return store ? (
    <Provider store={store}>{content}</Provider>
  ) : content;
};

// Custom render function with store and router
export const renderWithProviders = (
  ui,
  {
    initialState = {},
    store = createTestStore(initialState),
    router = true,
    ...renderOptions
  } = {}
) => {
  const Wrapper = ({ children }) => (
    <TestWrapper store={store} router={router}>
      {children}
    </TestWrapper>
  );

  return {
    store,
    user: userEvent.setup(),
    ...render(ui, { wrapper: Wrapper, ...renderOptions })
  };
};

// Sample component data for testing
export const sampleComponents = {
  header: {
    id: 'header-1',
    type: 'header',
    props: {
      title: 'Sample Header',
      subtitle: 'Sample subtitle',
      backgroundColor: '#1890ff',
      textColor: '#ffffff'
    },
    position: { x: 0, y: 0, width: 12, height: 3 }
  },
  button: {
    id: 'button-1',
    type: 'button',
    props: {
      text: 'Click me',
      backgroundColor: '#52c41a',
      color: '#ffffff',
      padding: '12px 24px'
    },
    position: { x: 0, y: 3, width: 3, height: 1 }
  },
  text: {
    id: 'text-1',
    type: 'text',
    props: {
      content: 'Sample text content',
      fontSize: '16px',
      color: '#333333'
    },
    position: { x: 0, y: 4, width: 6, height: 2 }
  },
  container: {
    id: 'container-1',
    type: 'container',
    props: {
      padding: '20px',
      backgroundColor: '#ffffff'
    },
    position: { x: 0, y: 6, width: 12, height: 4 },
    children: []
  }
};

// Sample templates for testing
export const sampleTemplates = {
  helloWorld: {
    id: 1,
    name: '[Default] Hello World Starter',
    description: 'A simple starter template',
    app_category: 'other',
    components: {
      pages: [{
        name: 'home',
        components: [
          sampleComponents.header,
          sampleComponents.button,
          sampleComponents.text
        ]
      }]
    },
    is_public: true
  },
  landingPage: {
    id: 2,
    name: '[Default] Landing Page',
    description: 'A landing page template',
    app_category: 'landing',
    components: {
      pages: [{
        name: 'home',
        components: [
          {
            id: 'hero-1',
            type: 'hero',
            props: {
              title: 'Welcome',
              subtitle: 'Build amazing apps'
            }
          }
        ]
      }]
    },
    is_public: true
  }
};

// Test helpers
export const testHelpers = {
  // Wait for component to be rendered
  waitForComponent: async (testId, timeout = 5000) => {
    return await waitFor(() => {
      expect(screen.getByTestId(testId)).toBeInTheDocument();
    }, { timeout });
  },

  // Wait for text to appear
  waitForText: async (text, timeout = 5000) => {
    return await waitFor(() => {
      expect(screen.getByText(text)).toBeInTheDocument();
    }, { timeout });
  },

  // Simulate drag and drop
  simulateDragDrop: async (source, target) => {
    fireEvent.dragStart(source);
    fireEvent.dragEnter(target);
    fireEvent.dragOver(target);
    fireEvent.drop(target);
    fireEvent.dragEnd(source);
  },

  // Simulate component selection
  selectComponent: async (componentTestId, user) => {
    const component = screen.getByTestId(componentTestId);
    await user.click(component);
    return component;
  },

  // Simulate property editing
  editProperty: async (propertyTestId, value, user) => {
    const input = screen.getByTestId(propertyTestId);
    await user.clear(input);
    await user.type(input, value);
    return input;
  },

  // Simulate template loading
  loadTemplate: async (templateName, user) => {
    const templateButton = screen.getByText(/template/i);
    await user.click(templateButton);

    await waitFor(() => {
      expect(screen.getByText(templateName)).toBeInTheDocument();
    });

    const template = screen.getByText(templateName);
    await user.click(template);

    const loadButton = screen.getByText(/load/i);
    await user.click(loadButton);
  },

  // Simulate code export
  exportCode: async (format, user) => {
    const exportButton = screen.getByText(/export/i);
    await user.click(exportButton);

    if (format) {
      const formatSelect = screen.getByTestId('export-format-select');
      await user.selectOptions(formatSelect, format);
    }

    const generateButton = screen.getByText(/generate/i);
    await user.click(generateButton);
  },

  // Check if component exists in store
  componentExistsInStore: (store, componentId) => {
    const state = store.getState();
    return state.app.components.some(c => c.id === componentId);
  },

  // Get component from store
  getComponentFromStore: (store, componentId) => {
    const state = store.getState();
    return state.app.components.find(c => c.id === componentId);
  },

  // Check if template is loaded
  isTemplateLoaded: (store) => {
    const state = store.getState();
    return state.app.components.length > 0;
  }
};

// Accessibility test helpers
export const a11yHelpers = {
  // Check for ARIA labels
  checkAriaLabels: (elements) => {
    elements.forEach(element => {
      expect(element).toHaveAttribute('aria-label');
    });
  },

  // Check for keyboard navigation
  checkKeyboardNavigation: async (user) => {
    // Tab through focusable elements
    await user.tab();
    expect(document.activeElement).toBeVisible();
  },

  // Check color contrast (basic check)
  checkColorContrast: (element) => {
    const styles = window.getComputedStyle(element);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    // Basic check - ensure colors are not the same
    expect(color).not.toBe(backgroundColor);
  }
};

// Performance test helpers
export const performanceHelpers = {
  // Measure render time
  measureRenderTime: (renderFunction) => {
    const start = performance.now();
    const result = renderFunction();
    const end = performance.now();
    
    return {
      result,
      renderTime: end - start
    };
  },

  // Check for memory leaks (basic)
  checkMemoryUsage: () => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }
};

// Setup function to run before each test
export const setupTest = () => {
  // Mock browser APIs
  mockBrowserAPIs();

  // Clear all mocks
  jest.clearAllMocks();

  // Reset API mocks
  Object.values(mockApiServices).forEach(service => {
    Object.values(service).forEach(method => {
      if (jest.isMockFunction(method)) {
        method.mockClear();
      }
    });
  });
};

// Cleanup function to run after each test
export const cleanupTest = () => {
  // Clean up any remaining timers
  jest.clearAllTimers();
  
  // Reset DOM
  document.body.innerHTML = '';
  
  // Clear any remaining mocks
  jest.restoreAllMocks();
};

export default {
  renderWithProviders,
  createTestStore,
  TestWrapper,
  mockApiServices,
  mockWebSocket,
  sampleComponents,
  sampleTemplates,
  testHelpers,
  a11yHelpers,
  performanceHelpers,
  setupTest,
  cleanupTest
};
