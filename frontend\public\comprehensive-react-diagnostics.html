<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive React Diagnostics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 24px;
        }

        h1 {
            color: #1f2937;
            margin-bottom: 24px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
        }

        .status {
            padding: 8px 12px;
            border-radius: 6px;
            margin: 4px 0;
            font-weight: 500;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .timing-test {
            background: #f3f4f6;
            border-left: 4px solid #6b7280;
            padding: 12px;
            margin: 8px 0;
        }

        .timing-test.pass {
            border-left-color: #10b981;
        }

        .timing-test.fail {
            border-left-color: #ef4444;
        }

        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
            font-weight: 500;
        }

        button:hover {
            background: #2563eb;
        }

        .log-container {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .test-summary {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔍 Comprehensive React Diagnostics</h1>
        <p>Advanced diagnostic tool to identify timing issues and inconsistencies in React application loading.</p>

        <div class="test-summary" id="summary">
            <strong>🔄 Initializing diagnostics...</strong>
        </div>

        <div class="grid">
            <div class="card">
                <h3>⏱️ Timing Analysis</h3>
                <div id="timing-results"></div>
            </div>
            <div class="card">
                <h3>🔄 Consistency Check</h3>
                <div id="consistency-results"></div>
            </div>
            <div class="card">
                <h3>⚛️ React Status</h3>
                <div id="react-status"></div>
            </div>
            <div class="card">
                <h3>🎨 CSS Bundle Status</h3>
                <div id="css-status"></div>
            </div>
        </div>

        <div class="card">
            <h3>📋 Diagnostic Log</h3>
            <div class="log-container" id="diagnostic-log"></div>
        </div>

        <div>
            <button onclick="runFullDiagnostics()">🔄 Run Full Diagnostics</button>
            <button onclick="runTimingTest()">⏱️ Timing Test Only</button>
            <button onclick="runConsistencyTest()">🔄 Consistency Test Only</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
            <button onclick="window.location.href='/'">🏠 Back to App</button>
        </div>
    </div>

    <script>
        let diagnosticLog = [];
        let testResults = {
            timing: {},
            consistency: {},
            react: {},
            css: {}
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            diagnosticLog.push(logEntry);
            updateLogDisplay();
            console.log(message);
        }

        function updateLogDisplay() {
            const logContainer = document.getElementById('diagnostic-log');
            logContainer.textContent = diagnosticLog.join('\n');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            diagnosticLog = [];
            updateLogDisplay();
        }

        function updateSummary() {
            const summary = document.getElementById('summary');
            const totalTests = Object.keys(testResults.timing).length +
                Object.keys(testResults.consistency).length +
                Object.keys(testResults.react).length +
                Object.keys(testResults.css).length;

            const passedTests = Object.values(testResults.timing).filter(r => r.passed).length +
                Object.values(testResults.consistency).filter(r => r.passed).length +
                Object.values(testResults.react).filter(r => r.passed).length +
                Object.values(testResults.css).filter(r => r.passed).length;

            if (totalTests === 0) {
                summary.innerHTML = '<strong>🔄 Running diagnostics...</strong>';
            } else {
                const status = passedTests === totalTests ? '✅ ALL TESTS PASSED' :
                    passedTests > totalTests / 2 ? '⚠️ SOME ISSUES FOUND' : '❌ MULTIPLE ISSUES FOUND';
                summary.innerHTML = `<strong>${status}</strong><br>Passed: ${passedTests}/${totalTests} tests`;
            }
        }

        async function runTimingTest() {
            log('🕐 Starting timing analysis...');
            const timingContainer = document.getElementById('timing-results');
            timingContainer.innerHTML = '';

            // Get main app context
            let mainAppContext = getMainAppContext();

            const tests = [
                {
                    name: 'React Global Immediate',
                    test: () => typeof mainAppContext.React !== 'undefined',
                    timing: 'immediate'
                },
                {
                    name: 'ReactDOM Global Immediate',
                    test: () => typeof mainAppContext.ReactDOM !== 'undefined',
                    timing: 'immediate'
                },
                {
                    name: 'App Loading Flags',
                    test: () => mainAppContext.__APP_LOADING__ === false && mainAppContext.__APP_LOADED__ === true,
                    timing: 'post-render'
                },
                {
                    name: 'Root Element Content',
                    test: () => {
                        const root = mainAppContext.document?.getElementById('root');
                        return root && root.innerHTML.trim().length > 0;
                    },
                    timing: 'post-render'
                }
            ];

            for (const test of tests) {
                const result = test.test();
                const passed = result === true;

                testResults.timing[test.name] = { passed, result, timing: test.timing };

                const div = document.createElement('div');
                div.className = `timing-test ${passed ? 'pass' : 'fail'}`;
                div.innerHTML = `
                    <strong>${passed ? '✅' : '❌'} ${test.name}</strong><br>
                    Result: ${result}<br>
                    Timing: ${test.timing}
                `;
                timingContainer.appendChild(div);

                log(`${passed ? '✅' : '❌'} ${test.name}: ${passed ? 'PASS' : 'FAIL'} (${test.timing})`);
            }

            updateSummary();
        }

        // Helper function to get main app context
        function getMainAppContext() {
            // Try different ways to access main app context
            if (window.opener && typeof window.opener.React !== 'undefined') {
                log('📱 Using opener window context');
                return window.opener;
            } else if (window.parent && window.parent !== window && typeof window.parent.React !== 'undefined') {
                log('📱 Using parent window context');
                return window.parent;
            } else if (window.top && window.top !== window && typeof window.top.React !== 'undefined') {
                log('📱 Using top window context');
                return window.top;
            } else {
                // Check if we have copied globals from main app
                if (typeof window.React !== 'undefined' && window.__MAIN_APP_DOCUMENT__) {
                    log('📱 Using copied globals with main app document');
                    return {
                        React: window.React,
                        ReactDOM: window.ReactDOM,
                        __APP_LOADED__: window.__APP_LOADED__,
                        __APP_LOADING__: window.__APP_LOADING__,
                        __REACT_LOADED__: window.__REACT_LOADED__,
                        document: window.__MAIN_APP_DOCUMENT__
                    };
                } else {
                    log('⚠️ Using current window context (may not have access to main app DOM)');
                    return window;
                }
            }
        }

        async function runConsistencyTest() {
            log('🔍 Starting consistency analysis...');
            const consistencyContainer = document.getElementById('consistency-results');
            consistencyContainer.innerHTML = '';

            // Get the main app context for comparison
            let mainAppContext = window;
            try {
                if (window.opener && typeof window.opener.React !== 'undefined') {
                    mainAppContext = window.opener;
                } else if (window.parent && window.parent !== window && typeof window.parent.React !== 'undefined') {
                    mainAppContext = window.parent;
                }
            } catch (e) {
                // Use current window if can't access parent/opener
            }

            // Test main app context
            const mainReact = typeof mainAppContext.React !== 'undefined';
            const mainReactDOM = typeof mainAppContext.ReactDOM !== 'undefined';

            log(`Main app React: ${mainReact}`);
            log(`Main app ReactDOM: ${mainReactDOM}`);

            // Test current diagnostic page context
            const currentReact = typeof window.React !== 'undefined';
            const currentReactDOM = typeof window.ReactDOM !== 'undefined';

            log(`Diagnostic page React: ${currentReact}`);
            log(`Diagnostic page ReactDOM: ${currentReactDOM}`);

            // Test iframe context (simulating cross-frame access)
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = 'about:blank';
            document.body.appendChild(iframe);

            return new Promise((resolve) => {
                iframe.onload = () => {
                    try {
                        const iframeWindow = iframe.contentWindow;

                        // Try to expose React to iframe (simulating our cross-frame exposure)
                        if (mainAppContext.React) {
                            iframeWindow.React = mainAppContext.React;
                            iframeWindow.ReactDOM = mainAppContext.ReactDOM;
                        }

                        const iframeReact = typeof iframeWindow.React !== 'undefined';
                        const iframeReactDOM = typeof iframeWindow.ReactDOM !== 'undefined';

                        log(`Iframe React (after exposure): ${iframeReact}`);
                        log(`Iframe ReactDOM (after exposure): ${iframeReactDOM}`);

                        const mainToCurrentConsistent = mainReact === currentReact;
                        const mainToIframeConsistent = mainReact === iframeReact;
                        const overallConsistent = mainToCurrentConsistent && mainToIframeConsistent;

                        testResults.consistency['Main to Current'] = {
                            passed: mainToCurrentConsistent,
                            main: mainReact,
                            current: currentReact
                        };
                        testResults.consistency['Main to Iframe'] = {
                            passed: mainToIframeConsistent,
                            main: mainReact,
                            iframe: iframeReact
                        };
                        testResults.consistency['Overall Consistency'] = {
                            passed: overallConsistent,
                            result: overallConsistent
                        };

                        consistencyContainer.innerHTML = `
                            <div class="status ${mainToCurrentConsistent ? 'success' : 'error'}">
                                ${mainToCurrentConsistent ? '✅' : '❌'} Main ↔ Current Page
                            </div>
                            <div class="status ${mainToIframeConsistent ? 'success' : 'error'}">
                                ${mainToIframeConsistent ? '✅' : '❌'} Main ↔ Iframe
                            </div>
                            <div class="status ${overallConsistent ? 'success' : 'error'}">
                                ${overallConsistent ? '✅' : '❌'} Overall Consistency
                            </div>
                            <div class="status info">
                                Main App: React=${mainReact}, ReactDOM=${mainReactDOM}<br>
                                Current: React=${currentReact}, ReactDOM=${currentReactDOM}<br>
                                Iframe: React=${iframeReact}, ReactDOM=${iframeReactDOM}
                            </div>
                        `;

                        document.body.removeChild(iframe);
                        updateSummary();
                        resolve();
                    } catch (error) {
                        log(`❌ Consistency test failed: ${error.message}`);
                        document.body.removeChild(iframe);
                        resolve();
                    }
                };
            });
        }

        async function runReactStatusTest() {
            log('⚛️ Checking React status...');
            const reactContainer = document.getElementById('react-status');

            // Try to get main app context if we're in standalone mode
            let mainAppContext = window;
            let contextSource = 'current';

            // Check if we have access to main app via opener or parent
            try {
                if (window.opener && typeof window.opener.React !== 'undefined') {
                    mainAppContext = window.opener;
                    contextSource = 'opener';
                    log('📱 Using opener window context for testing');
                } else if (window.parent && window.parent !== window && typeof window.parent.React !== 'undefined') {
                    mainAppContext = window.parent;
                    contextSource = 'parent';
                    log('📱 Using parent window context for testing');
                } else {
                    // Try to find main app via iframe if we copied globals earlier
                    const iframe = document.querySelector('iframe[src="/"]');
                    if (iframe && iframe.contentWindow && typeof iframe.contentWindow.React !== 'undefined') {
                        mainAppContext = iframe.contentWindow;
                        contextSource = 'iframe';
                        log('📱 Using iframe window context for testing');
                    }
                }
            } catch (e) {
                log('⚠️ Cannot access parent/opener context, using current window');
            }

            const tests = {
                'React Available': typeof mainAppContext.React !== 'undefined',
                'ReactDOM Available': typeof mainAppContext.ReactDOM !== 'undefined',
                'React Version': mainAppContext.React?.version || null,
                'Root Element': !!mainAppContext.document?.getElementById('root'),
                'Root Has Content': mainAppContext.document?.getElementById('root')?.innerHTML?.length > 0,
                'App Loaded Flag': mainAppContext.__APP_LOADED__,
                'React Loaded Flag': mainAppContext.__REACT_LOADED__
            };

            let html = `<div class="status info">📍 Context: ${contextSource} window</div>`;

            for (const [name, result] of Object.entries(tests)) {
                const passed = !!result;
                testResults.react[name] = { passed, result };

                html += `<div class="status ${passed ? 'success' : 'error'}">
                    ${passed ? '✅' : '❌'} ${name}: ${result}
                </div>`;

                log(`${passed ? '✅' : '❌'} ${name}: ${result}`);
            }

            // Additional debug info for root element
            if (mainAppContext.document) {
                const rootEl = mainAppContext.document.getElementById('root');
                if (rootEl) {
                    const contentPreview = rootEl.innerHTML.substring(0, 100);
                    html += `<div class="status info">📋 Root preview: ${contentPreview}...</div>`;
                    log(`📋 Root element content preview: ${contentPreview}...`);
                }
            }

            reactContainer.innerHTML = html;
            updateSummary();
        }

        async function runCSSStatusTest() {
            log('🎨 Checking CSS bundle status...');
            const cssContainer = document.getElementById('css-status');

            if (!cssContainer) {
                log('❌ CSS container not found!');
                return;
            }

            // Get main app context
            const mainAppContext = getMainAppContext();
            log(`🔍 CSS test using context: ${mainAppContext === window ? 'current window' : 'main app context'}`);

            // Check for CSS links in the main app document
            let cssLinks = [];
            let cssCount = 0;

            try {
                // Try multiple ways to access CSS links
                if (mainAppContext.document && mainAppContext.document.querySelectorAll) {
                    cssLinks = Array.from(mainAppContext.document.querySelectorAll('link[rel="stylesheet"]'));
                    cssCount = cssLinks.length;
                    log(`📱 Found ${cssCount} CSS links in main app document`);
                } else if (window.__MAIN_APP_DOCUMENT__ && window.__MAIN_APP_DOCUMENT__.querySelectorAll) {
                    cssLinks = Array.from(window.__MAIN_APP_DOCUMENT__.querySelectorAll('link[rel="stylesheet"]'));
                    cssCount = cssLinks.length;
                    log(`📱 Found ${cssCount} CSS links in stored main app document`);
                } else if (window.opener && window.opener.document && window.opener.document.querySelectorAll) {
                    cssLinks = Array.from(window.opener.document.querySelectorAll('link[rel="stylesheet"]'));
                    cssCount = cssLinks.length;
                    log(`📱 Found ${cssCount} CSS links in opener document`);
                } else if (window.parent && window.parent !== window && window.parent.document && window.parent.document.querySelectorAll) {
                    cssLinks = Array.from(window.parent.document.querySelectorAll('link[rel="stylesheet"]'));
                    cssCount = cssLinks.length;
                    log(`📱 Found ${cssCount} CSS links in parent document`);
                } else {
                    // Fallback to current document
                    cssLinks = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
                    cssCount = cssLinks.length;
                    log(`📱 Fallback: Found ${cssCount} CSS links in current document`);
                }
            } catch (error) {
                log(`❌ Error accessing CSS links: ${error.message}`);
                // Try one more fallback
                try {
                    cssLinks = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
                    cssCount = cssLinks.length;
                    log(`📱 Final fallback: Found ${cssCount} CSS links`);
                } catch (fallbackError) {
                    log(`❌ Final fallback failed: ${fallbackError.message}`);
                    cssCount = 0;
                }
            }

            testResults.css['CSS Links Found'] = { passed: cssCount > 0, result: cssCount };

            let html = `<div class="status ${cssCount > 0 ? 'success' : 'error'}">
                ${cssCount > 0 ? '✅' : '❌'} CSS Links: ${cssCount} found
            </div>`;

            // Test CSS loading
            for (let i = 0; i < Math.min(cssLinks.length, 3); i++) {
                const link = cssLinks[i];
                const href = link.href;
                const filename = href.split('/').pop();

                try {
                    const response = await fetch(href);
                    const loaded = response.ok;
                    testResults.css[`CSS Load: ${filename}`] = { passed: loaded, result: response.status };

                    html += `<div class="status ${loaded ? 'success' : 'error'}">
                        ${loaded ? '✅' : '❌'} ${filename}: ${response.status}
                    </div>`;

                    log(`${loaded ? '✅' : '❌'} CSS ${filename}: ${response.status}`);
                } catch (error) {
                    testResults.css[`CSS Load: ${filename}`] = { passed: false, result: error.message };
                    html += `<div class="status error">❌ ${filename}: ${error.message}</div>`;
                    log(`❌ CSS ${filename}: ${error.message}`);
                }
            }

            cssContainer.innerHTML = html;
            updateSummary();
        }

        async function runFullDiagnostics() {
            log('🚀 Starting comprehensive diagnostics...');

            // Clear previous results
            testResults = { timing: {}, consistency: {}, react: {}, css: {} };

            await runTimingTest();
            await runConsistencyTest();
            await runReactStatusTest();
            await runCSSStatusTest();

            log('✅ All diagnostics completed');
        }

        // Check if we're in the main app context or standalone
        function checkContext() {
            const isStandalone = window.location.pathname.includes('comprehensive-react-diagnostics.html');

            if (isStandalone) {
                log('🔍 Running in standalone diagnostic mode');

                // Check if opened as popup from main app
                if (window.opener) {
                    try {
                        if (typeof window.opener.React !== 'undefined') {
                            log('✅ Found React in opener window - using opener context');
                            setTimeout(runFullDiagnostics, 500);
                            return;
                        }
                    } catch (error) {
                        log(`⚠️ Cannot access opener: ${error.message}`);
                    }
                }

                // Try to access main app via iframe
                log('🔍 Attempting to connect to main app via iframe...');
                const iframe = document.createElement('iframe');
                iframe.src = '/';
                iframe.style.display = 'none';
                document.body.appendChild(iframe);

                let attempts = 0;
                const maxAttempts = 3;

                function checkIframe() {
                    attempts++;
                    try {
                        const mainWindow = iframe.contentWindow;
                        if (mainWindow && typeof mainWindow.React !== 'undefined') {
                            log('✅ Successfully connected to main app');
                            // Copy globals from main app for local testing
                            window.React = mainWindow.React;
                            window.ReactDOM = mainWindow.ReactDOM;
                            window.__APP_LOADED__ = mainWindow.__APP_LOADED__;
                            window.__APP_LOADING__ = mainWindow.__APP_LOADING__;
                            window.__REACT_LOADED__ = mainWindow.__REACT_LOADED__;
                            window.__REACT_GLOBALS_EXPOSED__ = mainWindow.__REACT_GLOBALS_EXPOSED__;

                            // Store reference to main app document for DOM testing
                            window.__MAIN_APP_DOCUMENT__ = mainWindow.document;

                            // Test document access immediately
                            try {
                                const testRoot = mainWindow.document.getElementById('root');
                                const testCSS = mainWindow.document.querySelectorAll('link[rel="stylesheet"]');
                                log(`📋 Document access test: root=${!!testRoot}, css=${testCSS.length}`);
                            } catch (testError) {
                                log(`⚠️ Document access test failed: ${testError.message}`);
                            }

                            log('📋 Copied main app globals and document reference');
                            setTimeout(runFullDiagnostics, 1000);
                        } else if (attempts < maxAttempts) {
                            log(`⏳ Main app not ready (attempt ${attempts}/${maxAttempts}), retrying...`);
                            setTimeout(checkIframe, 2000);
                        } else {
                            log('❌ Could not connect to main app after multiple attempts');
                            log('ℹ️ Running diagnostics in isolated mode...');
                            document.body.removeChild(iframe);
                            setTimeout(runFullDiagnostics, 500);
                        }
                    } catch (error) {
                        log(`❌ Error accessing main app: ${error.message}`);
                        if (attempts >= maxAttempts) {
                            log('ℹ️ Running diagnostics in isolated mode...');
                            document.body.removeChild(iframe);
                            setTimeout(runFullDiagnostics, 500);
                        } else {
                            setTimeout(checkIframe, 2000);
                        }
                    }
                }

                iframe.onload = checkIframe;
                iframe.onerror = () => {
                    log('❌ Failed to load main app iframe');
                    setTimeout(runFullDiagnostics, 500);
                };
            } else {
                log('✅ Running in main app context');
                setTimeout(runFullDiagnostics, 500);
            }
        }

        // Auto-run diagnostics on page load with context check
        window.addEventListener('load', () => {
            setTimeout(checkContext, 100);
        });
    </script>
</body>

</html>