/**
 * Enhanced Code Exporter Component
 * 
 * Expanded code export functionality supporting multiple frameworks (<PERSON><PERSON>, Vue, Angular),
 * TypeScript generation, and improved code quality with proper formatting.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Alert,
  Select,
  Switch,
  Form,
  Input,
  Checkbox,
  Modal,
  Row,
  Col,
  Divider,
  Tag,
  Tooltip,
  Progress,
  notification,
  Badge
} from 'antd';
import {
  CodeOutlined,
  DownloadOutlined,
  EyeOutlined,
  SettingOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ExportOutlined,
  BugOutlined,
  ThunderboltOutlined,
  CopyOutlined,
  FolderOutlined,
  ApiOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

// Styled Components
const ExportContainer = styled.div`
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const FrameworkCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid ${props => props.selected ? '#1890ff' : '#d9d9d9'};
  
  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }
`;

const CodePreview = styled.div`
  background: #f6f8fa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
`;

const ExportProgress = styled.div`
  margin: 16px 0;
`;

// Enhanced framework configurations
const FRAMEWORKS = {
  react: {
    id: 'react',
    name: 'React',
    description: 'Modern React components with hooks and functional components',
    icon: '⚛️',
    color: '#61dafb',
    extensions: ['jsx', 'tsx'],
    features: ['hooks', 'functional-components', 'context', 'styled-components'],
    typescript: true,
    testing: ['jest', 'react-testing-library'],
    bundlers: ['webpack', 'vite', 'parcel']
  },
  vue: {
    id: 'vue',
    name: 'Vue.js',
    description: 'Vue 3 composition API with single file components',
    icon: '🟢',
    color: '#4fc08d',
    extensions: ['vue'],
    features: ['composition-api', 'sfc', 'pinia', 'vue-router'],
    typescript: true,
    testing: ['vitest', 'vue-test-utils'],
    bundlers: ['vite', 'webpack']
  },
  angular: {
    id: 'angular',
    name: 'Angular',
    description: 'Angular components with TypeScript and dependency injection',
    icon: '🔴',
    color: '#dd0031',
    extensions: ['ts', 'html', 'scss'],
    features: ['components', 'services', 'modules', 'routing'],
    typescript: true,
    testing: ['jasmine', 'karma'],
    bundlers: ['angular-cli', 'webpack']
  },
  svelte: {
    id: 'svelte',
    name: 'Svelte',
    description: 'Svelte components with reactive statements',
    icon: '🧡',
    color: '#ff3e00',
    extensions: ['svelte'],
    features: ['reactive', 'stores', 'transitions'],
    typescript: true,
    testing: ['jest', 'svelte-testing-library'],
    bundlers: ['vite', 'rollup']
  },
  nextjs: {
    id: 'nextjs',
    name: 'Next.js',
    description: 'Next.js pages and components with SSR/SSG support',
    icon: '▲',
    color: '#000000',
    extensions: ['jsx', 'tsx'],
    features: ['ssr', 'ssg', 'api-routes', 'image-optimization'],
    typescript: true,
    testing: ['jest', 'react-testing-library'],
    bundlers: ['webpack', 'turbopack']
  },
  nuxt: {
    id: 'nuxt',
    name: 'Nuxt.js',
    description: 'Nuxt 3 with Vue composition API and auto-imports',
    icon: '💚',
    color: '#00dc82',
    extensions: ['vue'],
    features: ['ssr', 'ssg', 'auto-imports', 'nitro'],
    typescript: true,
    testing: ['vitest', 'vue-test-utils'],
    bundlers: ['vite', 'webpack']
  }
};

// Code quality options
const CODE_QUALITY_OPTIONS = {
  formatting: {
    prettier: 'Prettier code formatting',
    eslint: 'ESLint linting rules',
    editorconfig: 'EditorConfig settings'
  },
  structure: {
    components: 'Separate component files',
    hooks: 'Custom hooks extraction',
    utils: 'Utility functions',
    types: 'TypeScript type definitions'
  },
  optimization: {
    treeshaking: 'Tree shaking optimization',
    codesplitting: 'Code splitting',
    lazyloading: 'Lazy loading components',
    memoization: 'React.memo optimization'
  },
  testing: {
    unitTests: 'Unit test files',
    integrationTests: 'Integration tests',
    e2eTests: 'End-to-end tests',
    storybook: 'Storybook stories'
  }
};

/**
 * EnhancedCodeExporter Component
 */
const EnhancedCodeExporter = ({
  components = [],
  layouts = [],
  theme = {},
  onExport,
  onPreview,
  compact = false
}) => {
  // State
  const [activeTab, setActiveTab] = useState('framework');
  const [selectedFramework, setSelectedFramework] = useState('react');
  const [exportSettings, setExportSettings] = useState({
    typescript: true,
    includeStyles: true,
    includeTests: false,
    includeStorybook: false,
    codeQuality: {
      prettier: true,
      eslint: true,
      components: true,
      hooks: false,
      treeshaking: true,
      unitTests: false
    },
    projectStructure: 'components',
    bundler: 'vite',
    packageManager: 'npm'
  });
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [generatedCode, setGeneratedCode] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [exportHistory, setExportHistory] = useState([]);

  // Get selected framework config
  const frameworkConfig = FRAMEWORKS[selectedFramework];

  // Generate mock code based on framework and settings
  const generateCode = useCallback(() => {
    const { typescript, includeStyles, codeQuality } = exportSettings;
    const ext = typescript && frameworkConfig.typescript ?
      frameworkConfig.extensions.find(e => e.includes('ts')) || frameworkConfig.extensions[0] :
      frameworkConfig.extensions[0];

    let code = '';

    switch (selectedFramework) {
      case 'react':
        code = `${typescript ? '// TypeScript React Component\n' : '// React Component\n'}
import React${typescript ? ', { FC }' : ''} from 'react';
${includeStyles ? "import './Component.css';" : ''}
${codeQuality.types && typescript ? '\ninterface ComponentProps {\n  title?: string;\n  children?: React.ReactNode;\n}' : ''}

${typescript ? 'const Component: FC<ComponentProps> = ({ title, children })' : 'const Component = ({ title, children })'} => {
  return (
    <div className="component">
      {title && <h2>{title}</h2>}
      <div className="component-content">
        {children}
      </div>
    </div>
  );
};

export default Component;`;
        break;

      case 'vue':
        code = `<template>
  <div class="component">
    <h2 v-if="title">{{ title }}</h2>
    <div class="component-content">
      <slot />
    </div>
  </div>
</template>

<script${typescript ? ' setup lang="ts"' : ' setup'}>
${typescript ? 'interface Props {\n  title?: string;\n}\n\n' : ''}defineProps${typescript ? '<Props>' : ''}({
  title: {
    type: String,
    default: ''
  }
});
</script>

${includeStyles ? '<style scoped>\n.component {\n  padding: 16px;\n}\n</style>' : ''}`;
        break;

      case 'angular':
        code = `import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-component',
  template: \`
    <div class="component">
      <h2 *ngIf="title">{{ title }}</h2>
      <div class="component-content">
        <ng-content></ng-content>
      </div>
    </div>
  \`,
  ${includeStyles ? "styleUrls: ['./component.component.scss']" : 'styles: []'}
})
export class ComponentComponent {
  @Input() title${typescript ? ': string' : ''} = '';
}`;
        break;

      case 'svelte':
        code = `<script${typescript ? ' lang="ts"' : ''}>
  export let title${typescript ? ': string' : ''} = '';
</script>

<div class="component">
  {#if title}
    <h2>{title}</h2>
  {/if}
  <div class="component-content">
    <slot />
  </div>
</div>

${includeStyles ? '<style>\n  .component {\n    padding: 16px;\n  }\n</style>' : ''}`;
        break;

      default:
        code = '// Code generation for this framework is coming soon...';
    }

    return code;
  }, [selectedFramework, exportSettings, frameworkConfig]);

  // Handle framework selection
  const handleFrameworkSelect = useCallback((frameworkId) => {
    setSelectedFramework(frameworkId);
    const framework = FRAMEWORKS[frameworkId];

    // Update settings based on framework capabilities
    setExportSettings(prev => ({
      ...prev,
      typescript: framework.typescript && prev.typescript,
      bundler: framework.bundlers[0]
    }));
  }, []);

  // Handle export
  const handleExport = useCallback(async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => Math.min(prev + Math.random() * 20, 95));
      }, 200);

      // Generate code
      const code = generateCode();
      setGeneratedCode(code);

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));

      clearInterval(progressInterval);
      setExportProgress(100);

      // Add to history
      const exportRecord = {
        id: Date.now(),
        framework: selectedFramework,
        timestamp: new Date().toISOString(),
        settings: exportSettings,
        componentCount: components.length
      };
      setExportHistory(prev => [exportRecord, ...prev.slice(0, 9)]);

      if (onExport) {
        onExport({
          framework: selectedFramework,
          code,
          settings: exportSettings
        });
      }

      notification.success({
        message: 'Export Complete',
        description: `Code exported successfully for ${frameworkConfig.name}`,
        duration: 3
      });

      setActiveTab('preview');

    } catch (error) {
      notification.error({
        message: 'Export Failed',
        description: error.message,
        duration: 5
      });
    } finally {
      setIsExporting(false);
    }
  }, [selectedFramework, exportSettings, generateCode, components.length, frameworkConfig.name, onExport]);

  // Handle download
  const handleDownload = useCallback(() => {
    if (!generatedCode) return;

    const framework = FRAMEWORKS[selectedFramework];
    const ext = exportSettings.typescript && framework.typescript ?
      framework.extensions.find(e => e.includes('ts')) || framework.extensions[0] :
      framework.extensions[0];

    const fileName = `Component.${ext}`;
    const blob = new Blob([generatedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    notification.success({
      message: 'Download Complete',
      description: `Code downloaded as ${fileName}`,
      duration: 3
    });
  }, [generatedCode, selectedFramework, exportSettings]);

  // Copy to clipboard
  const handleCopyCode = useCallback(() => {
    if (!generatedCode) return;

    navigator.clipboard.writeText(generatedCode).then(() => {
      notification.success({
        message: 'Code Copied',
        description: 'Code has been copied to clipboard',
        duration: 2
      });
    }).catch(() => {
      notification.error({
        message: 'Copy Failed',
        description: 'Failed to copy code to clipboard',
        duration: 3
      });
    });
  }, [generatedCode]);

  return (
    <ExportContainer>
      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              <ExportOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              Enhanced Code Export
            </Title>
          </Col>
          <Col>
            <Space>
              <Badge count={exportHistory.length} size="small">
                <Button icon={<FileTextOutlined />}>
                  History
                </Button>
              </Badge>
              <Button
                type="primary"
                icon={<ExportOutlined />}
                onClick={handleExport}
                loading={isExporting}
                disabled={components.length === 0}
              >
                Export Code
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {isExporting && (
        <ExportProgress>
          <Progress
            percent={Math.round(exportProgress)}
            status="active"
            format={() => `Generating ${frameworkConfig.name} code...`}
          />
        </ExportProgress>
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Framework" key="framework">
          <div style={{ marginBottom: 16 }}>
            <Text strong>Select Target Framework</Text>
            <Paragraph type="secondary">
              Choose the framework you want to export your components to.
            </Paragraph>
          </div>

          <Row gutter={[16, 16]}>
            {Object.values(FRAMEWORKS).map(framework => (
              <Col xs={24} sm={12} lg={8} key={framework.id}>
                <FrameworkCard
                  size="small"
                  selected={selectedFramework === framework.id}
                  onClick={() => handleFrameworkSelect(framework.id)}
                  title={
                    <Space>
                      <span style={{ fontSize: 18 }}>{framework.icon}</span>
                      <span>{framework.name}</span>
                      {framework.typescript && (
                        <Tag color="blue" size="small">TS</Tag>
                      )}
                    </Space>
                  }
                >
                  <Paragraph style={{ margin: 0, fontSize: 12 }}>
                    {framework.description}
                  </Paragraph>
                  <div style={{ marginTop: 8 }}>
                    <Space wrap>
                      {framework.features.slice(0, 3).map(feature => (
                        <Tag key={feature} size="small" color="geekblue">
                          {feature}
                        </Tag>
                      ))}
                    </Space>
                  </div>
                </FrameworkCard>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="Settings" key="settings">
          <Form layout="vertical">
            <Row gutter={[24, 16]}>
              <Col xs={24} lg={12}>
                <Card title="Language & Structure" size="small">
                  <Form.Item label="Language">
                    <Switch
                      checked={exportSettings.typescript}
                      onChange={(checked) => setExportSettings(prev => ({ ...prev, typescript: checked }))}
                      disabled={!frameworkConfig.typescript}
                      checkedChildren="TypeScript"
                      unCheckedChildren="JavaScript"
                    />
                  </Form.Item>

                  <Form.Item label="Project Structure">
                    <Select
                      value={exportSettings.projectStructure}
                      onChange={(value) => setExportSettings(prev => ({ ...prev, projectStructure: value }))}
                      style={{ width: '100%' }}
                    >
                      <Option value="single">Single File</Option>
                      <Option value="components">Component Files</Option>
                      <Option value="modules">Module Structure</Option>
                      <Option value="full">Full Project</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item label="Bundler">
                    <Select
                      value={exportSettings.bundler}
                      onChange={(value) => setExportSettings(prev => ({ ...prev, bundler: value }))}
                      style={{ width: '100%' }}
                    >
                      {frameworkConfig.bundlers.map(bundler => (
                        <Option key={bundler} value={bundler}>
                          {bundler.charAt(0).toUpperCase() + bundler.slice(1)}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Code Quality" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {Object.entries(CODE_QUALITY_OPTIONS.formatting).map(([key, label]) => (
                      <Checkbox
                        key={key}
                        checked={exportSettings.codeQuality[key]}
                        onChange={(e) => setExportSettings(prev => ({
                          ...prev,
                          codeQuality: { ...prev.codeQuality, [key]: e.target.checked }
                        }))}
                      >
                        {label}
                      </Checkbox>
                    ))}
                    <Divider style={{ margin: '8px 0' }} />
                    {Object.entries(CODE_QUALITY_OPTIONS.structure).map(([key, label]) => (
                      <Checkbox
                        key={key}
                        checked={exportSettings.codeQuality[key]}
                        onChange={(e) => setExportSettings(prev => ({
                          ...prev,
                          codeQuality: { ...prev.codeQuality, [key]: e.target.checked }
                        }))}
                      >
                        {label}
                      </Checkbox>
                    ))}
                  </Space>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Optimization" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {Object.entries(CODE_QUALITY_OPTIONS.optimization).map(([key, label]) => (
                      <Checkbox
                        key={key}
                        checked={exportSettings.codeQuality[key]}
                        onChange={(e) => setExportSettings(prev => ({
                          ...prev,
                          codeQuality: { ...prev.codeQuality, [key]: e.target.checked }
                        }))}
                      >
                        {label}
                      </Checkbox>
                    ))}
                  </Space>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="Testing & Documentation" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Switch
                      checked={exportSettings.includeTests}
                      onChange={(checked) => setExportSettings(prev => ({ ...prev, includeTests: checked }))}
                      checkedChildren="Include Tests"
                      unCheckedChildren="No Tests"
                    />
                    <Switch
                      checked={exportSettings.includeStorybook}
                      onChange={(checked) => setExportSettings(prev => ({ ...prev, includeStorybook: checked }))}
                      checkedChildren="Include Storybook"
                      unCheckedChildren="No Storybook"
                    />
                    {Object.entries(CODE_QUALITY_OPTIONS.testing).map(([key, label]) => (
                      <Checkbox
                        key={key}
                        checked={exportSettings.codeQuality[key]}
                        onChange={(e) => setExportSettings(prev => ({
                          ...prev,
                          codeQuality: { ...prev.codeQuality, [key]: e.target.checked }
                        }))}
                        disabled={!exportSettings.includeTests && key.includes('Tests')}
                      >
                        {label}
                      </Checkbox>
                    ))}
                  </Space>
                </Card>
              </Col>
            </Row>
          </Form>
        </TabPane>

        <TabPane tab="Preview" key="preview">
          <div style={{ marginBottom: 16 }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Text strong>Code Preview</Text>
                  <Tag color={frameworkConfig.color}>{frameworkConfig.name}</Tag>
                  {exportSettings.typescript && <Tag color="blue">TypeScript</Tag>}
                </Space>
              </Col>
              <Col>
                <Space>
                  <Button
                    icon={<CopyOutlined />}
                    onClick={handleCopyCode}
                    disabled={!generatedCode}
                  >
                    Copy
                  </Button>
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                    disabled={!generatedCode}
                  >
                    Download
                  </Button>
                </Space>
              </Col>
            </Row>
          </div>

          {generatedCode ? (
            <CodePreview>
              {generatedCode}
            </CodePreview>
          ) : (
            <Alert
              message="No code generated yet"
              description="Click 'Export Code' to generate code preview."
              type="info"
              showIcon
            />
          )}
        </TabPane>

        <TabPane tab="History" key="history">
          <div style={{ marginBottom: 16 }}>
            <Text strong>Export History</Text>
            <Paragraph type="secondary">
              Recent code exports and their configurations.
            </Paragraph>
          </div>

          {exportHistory.length > 0 ? (
            <Space direction="vertical" style={{ width: '100%' }}>
              {exportHistory.map(record => (
                <Card key={record.id} size="small">
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space>
                        <span style={{ fontSize: 16 }}>
                          {FRAMEWORKS[record.framework]?.icon}
                        </span>
                        <Text strong>{FRAMEWORKS[record.framework]?.name}</Text>
                        {record.settings.typescript && <Tag color="blue" size="small">TS</Tag>}
                        <Text type="secondary">
                          {record.componentCount} components
                        </Text>
                      </Space>
                    </Col>
                    <Col>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {new Date(record.timestamp).toLocaleString()}
                      </Text>
                    </Col>
                  </Row>
                </Card>
              ))}
            </Space>
          ) : (
            <Alert
              message="No export history"
              description="Your export history will appear here after you export code."
              type="info"
              showIcon
            />
          )}
        </TabPane>
      </Tabs>
    </ExportContainer>
  );
};

export default EnhancedCodeExporter;
