"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[3815],{

/***/ 25577:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71468);
/* harmony import */ var _services_PreviewWebSocketService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(75121);




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Custom hook for collaborative preview functionality
 * Handles real-time synchronization, cursor tracking, and collaborative editing
 */
var useCollaborativePreview = function useCollaborativePreview(_ref) {
  var sessionId = _ref.sessionId,
    userId = _ref.userId,
    _ref$username = _ref.username,
    username = _ref$username === void 0 ? 'Anonymous' : _ref$username,
    _ref$avatar = _ref.avatar,
    avatar = _ref$avatar === void 0 ? null : _ref$avatar,
    _ref$enableCollaborat = _ref.enableCollaboration,
    enableCollaboration = _ref$enableCollaborat === void 0 ? true : _ref$enableCollaborat,
    _ref$enableCursorTrac = _ref.enableCursorTracking,
    enableCursorTracking = _ref$enableCursorTrac === void 0 ? true : _ref$enableCursorTrac,
    _ref$enableDeviceSync = _ref.enableDeviceSync,
    enableDeviceSync = _ref$enableDeviceSync === void 0 ? true : _ref$enableDeviceSync;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    collaborators = _useState2[0],
    setCollaborators = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    cursors = _useState4[0],
    setCursors = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isConnected = _useState6[0],
    setIsConnected = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('disconnected'),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    connectionStatus = _useState8[0],
    setConnectionStatus = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    syncedComponents = _useState0[0],
    setSyncedComponents = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    deviceState = _useState10[0],
    setDeviceState = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(new Map()),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    conflictResolution = _useState12[0],
    setConflictResolution = _useState12[1];

  // Refs
  var wsServiceRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var cursorTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(new Map());
  var lastSyncTime = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(new Date());

  // Redux state
  var websocketConfig = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return ((_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.config) || {};
  });

  // Initialize WebSocket service
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (!enableCollaboration || !sessionId) return;
    var wsService = new _services_PreviewWebSocketService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A({
      url: websocketConfig.url || 'ws://localhost:8000/ws/collaboration/',
      autoConnect: true,
      reconnectOptions: {
        maxAttempts: 10,
        initialDelay: 1000,
        maxDelay: 30000
      }
    });
    wsServiceRef.current = wsService;

    // Connection event handlers
    wsService.on('connect', function () {
      setIsConnected(true);
      setConnectionStatus('connected');

      // Join the collaborative session
      wsService.joinSession(sessionId, {
        username: username,
        avatar: avatar
      });
    });
    wsService.on('disconnect', function () {
      setIsConnected(false);
      setConnectionStatus('disconnected');
    });
    wsService.on('error', function (error) {
      setConnectionStatus('error');
      console.error('Collaborative WebSocket error:', error);
    });

    // Preview event handlers
    setupPreviewEventHandlers(wsService);
    return function () {
      if (wsService) {
        wsService.leaveSession(sessionId);
        wsService.disconnect();
      }

      // Clear cursor timeouts
      cursorTimeoutRef.current.forEach(function (timeout) {
        return clearTimeout(timeout);
      });
      cursorTimeoutRef.current.clear();
    };
  }, [sessionId, enableCollaboration, username, avatar, websocketConfig.url]);

  // Setup preview event handlers
  var setupPreviewEventHandlers = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (wsService) {
    // Collaborator events
    wsService.onPreviewEvent('collaborator_joined', function (data) {
      var user_id = data.user_id,
        collaboratorName = data.username,
        collaboratorAvatar = data.avatar;
      setCollaborators(function (prev) {
        return new Map(prev.set(user_id, {
          id: user_id,
          username: collaboratorName,
          avatar: collaboratorAvatar,
          joinedAt: new Date(),
          isActive: true
        }));
      });
    });
    wsService.onPreviewEvent('collaborator_left', function (data) {
      var user_id = data.user_id;
      setCollaborators(function (prev) {
        var newMap = new Map(prev);
        newMap["delete"](user_id);
        return newMap;
      });
      setCursors(function (prev) {
        var newMap = new Map(prev);
        newMap["delete"](user_id);
        return newMap;
      });
    });

    // Cursor tracking events
    if (enableCursorTracking) {
      wsService.onPreviewEvent('cursor_moved', function (data) {
        var user_id = data.user_id,
          position = data.position;
        if (user_id !== userId) {
          setCursors(function (prev) {
            return new Map(prev.set(user_id, {
              position: position,
              timestamp: new Date(),
              userId: user_id
            }));
          });

          // Auto-hide cursor after inactivity
          var existingTimeout = cursorTimeoutRef.current.get(user_id);
          if (existingTimeout) {
            clearTimeout(existingTimeout);
          }
          var timeout = setTimeout(function () {
            setCursors(function (prev) {
              var newMap = new Map(prev);
              newMap["delete"](user_id);
              return newMap;
            });
            cursorTimeoutRef.current["delete"](user_id);
          }, 5000);
          cursorTimeoutRef.current.set(user_id, timeout);
        }
      });
    }

    // Component synchronization events
    wsService.onPreviewEvent('component_updated', function (data) {
      var component_id = data.component_id,
        component_data = data.component_data,
        updatedBy = data.user_id;
      if (updatedBy !== userId) {
        setSyncedComponents(function (prev) {
          return new Map(prev.set(component_id, _objectSpread(_objectSpread({}, component_data), {}, {
            lastUpdatedBy: updatedBy,
            lastUpdated: new Date(),
            synced: true
          })));
        });
      }
    });
    wsService.onPreviewEvent('component_added', function (data) {
      var component = data.component,
        addedBy = data.user_id;
      if (addedBy !== userId && component !== null && component !== void 0 && component.id) {
        setSyncedComponents(function (prev) {
          return new Map(prev.set(component.id, _objectSpread(_objectSpread({}, component), {}, {
            addedBy: addedBy,
            synced: true
          })));
        });
      }
    });
    wsService.onPreviewEvent('component_deleted', function (data) {
      var component_id = data.component_id,
        deletedBy = data.user_id;
      if (deletedBy !== userId) {
        setSyncedComponents(function (prev) {
          var newMap = new Map(prev);
          newMap["delete"](component_id);
          return newMap;
        });
      }
    });

    // Device synchronization events
    if (enableDeviceSync) {
      wsService.onPreviewEvent('device_changed', function (data) {
        var device_type = data.device_type,
          device_config = data.device_config,
          changedBy = data.user_id;
        if (changedBy !== userId) {
          setDeviceState({
            type: device_type,
            config: device_config,
            changedBy: changedBy,
            timestamp: new Date()
          });
        }
      });
    }

    // Preview state synchronization
    wsService.onPreviewEvent('preview_state_synced', function (data) {
      var state = data.state;
      if (state.components) {
        var componentsMap = new Map(state.components);
        setSyncedComponents(componentsMap);
      }
      if (state.deviceSettings && enableDeviceSync) {
        setDeviceState(state.deviceSettings);
      }
      lastSyncTime.current = new Date();
    });
  }, [userId, enableCursorTracking, enableDeviceSync]);

  // Send component update
  var sendComponentUpdate = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee(componentId, componentData) {
      var immediate,
        _args = arguments,
        _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            immediate = _args.length > 2 && _args[2] !== undefined ? _args[2] : false;
            if (!(!wsServiceRef.current || !isConnected)) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return", false);
          case 1:
            _context.prev = 1;
            _context.next = 2;
            return wsServiceRef.current.sendComponentUpdate(componentId, componentData, {
              userId: userId,
              sessionId: sessionId,
              immediate: immediate
            });
          case 2:
            return _context.abrupt("return", true);
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            console.error('Failed to send component update:', _t);
            return _context.abrupt("return", false);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3]]);
    }));
    return function (_x, _x2) {
      return _ref2.apply(this, arguments);
    };
  }(), [isConnected, userId, sessionId]);

  // Send cursor position
  var sendCursorPosition = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2(position) {
      var _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            if (!(!wsServiceRef.current || !isConnected || !enableCursorTracking)) {
              _context2.next = 1;
              break;
            }
            return _context2.abrupt("return");
          case 1:
            _context2.prev = 1;
            _context2.next = 2;
            return wsServiceRef.current.sendCursorPosition(position, {
              userId: userId,
              sessionId: sessionId
            });
          case 2:
            _context2.next = 4;
            break;
          case 3:
            _context2.prev = 3;
            _t2 = _context2["catch"](1);
            console.error('Failed to send cursor position:', _t2);
          case 4:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 3]]);
    }));
    return function (_x3) {
      return _ref3.apply(this, arguments);
    };
  }(), [isConnected, userId, sessionId, enableCursorTracking]);

  // Send device change
  var sendDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3(deviceType, deviceConfig) {
      var _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (!(!wsServiceRef.current || !isConnected || !enableDeviceSync)) {
              _context3.next = 1;
              break;
            }
            return _context3.abrupt("return", false);
          case 1:
            _context3.prev = 1;
            _context3.next = 2;
            return wsServiceRef.current.sendDeviceChange(deviceType, deviceConfig, {
              userId: userId,
              sessionId: sessionId
            });
          case 2:
            return _context3.abrupt("return", true);
          case 3:
            _context3.prev = 3;
            _t3 = _context3["catch"](1);
            console.error('Failed to send device change:', _t3);
            return _context3.abrupt("return", false);
          case 4:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 3]]);
    }));
    return function (_x4, _x5) {
      return _ref4.apply(this, arguments);
    };
  }(), [isConnected, userId, sessionId, enableDeviceSync]);

  // Request preview state sync
  var requestSync = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee4() {
    var _t4;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          if (!(!wsServiceRef.current || !isConnected)) {
            _context4.next = 1;
            break;
          }
          return _context4.abrupt("return", false);
        case 1:
          _context4.prev = 1;
          _context4.next = 2;
          return wsServiceRef.current.requestPreviewState(sessionId);
        case 2:
          return _context4.abrupt("return", true);
        case 3:
          _context4.prev = 3;
          _t4 = _context4["catch"](1);
          console.error('Failed to request preview state:', _t4);
          return _context4.abrupt("return", false);
        case 4:
        case "end":
          return _context4.stop();
      }
    }, _callee4, null, [[1, 3]]);
  })), [isConnected, sessionId]);

  // Resolve component conflicts
  var resolveConflict = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (componentId, resolution) {
    setConflictResolution(function (prev) {
      return new Map(prev.set(componentId, {
        resolution: resolution,
        timestamp: new Date(),
        resolvedBy: userId
      }));
    });
  }, [userId]);

  // Get component with conflict resolution
  var getResolvedComponent = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (componentId, localComponent) {
    var syncedComponent = syncedComponents.get(componentId);
    var conflict = conflictResolution.get(componentId);
    if (!syncedComponent) return localComponent;
    if (!conflict) {
      // Check for conflicts based on timestamps
      var localTime = new Date((localComponent === null || localComponent === void 0 ? void 0 : localComponent.lastUpdated) || 0);
      var syncedTime = new Date((syncedComponent === null || syncedComponent === void 0 ? void 0 : syncedComponent.lastUpdated) || 0);
      if (Math.abs(localTime - syncedTime) < 1000) {
        // Recent conflict, use last-writer-wins
        return syncedTime > localTime ? syncedComponent : localComponent;
      }
    }

    // Apply conflict resolution
    switch (conflict === null || conflict === void 0 ? void 0 : conflict.resolution) {
      case 'use_local':
        return localComponent;
      case 'use_remote':
        return syncedComponent;
      case 'merge':
        return _objectSpread(_objectSpread({}, localComponent), syncedComponent);
      default:
        return syncedComponent;
      // Default to remote
    }
  }, [syncedComponents, conflictResolution]);

  // Get collaboration status
  var getCollaborationStatus = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    return {
      isConnected: isConnected,
      connectionStatus: connectionStatus,
      collaboratorCount: collaborators.size,
      hasActiveCollaborators: collaborators.size > 0,
      lastSyncTime: lastSyncTime.current,
      syncedComponentCount: syncedComponents.size
    };
  }, [isConnected, connectionStatus, collaborators.size, syncedComponents.size]);
  return {
    // Connection state
    isConnected: isConnected,
    connectionStatus: connectionStatus,
    // Collaboration data
    collaborators: Array.from(collaborators.values()),
    cursors: Array.from(cursors.values()),
    syncedComponents: Array.from(syncedComponents.entries()),
    deviceState: deviceState,
    // Actions
    sendComponentUpdate: sendComponentUpdate,
    sendCursorPosition: sendCursorPosition,
    sendDeviceChange: sendDeviceChange,
    requestSync: requestSync,
    resolveConflict: resolveConflict,
    // Utilities
    getResolvedComponent: getResolvedComponent,
    getCollaborationStatus: getCollaborationStatus,
    // WebSocket service reference
    wsService: wsServiceRef.current
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useCollaborativePreview);

/***/ }),

/***/ 82569:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ZV: () => (/* binding */ useEnhancedTheme),
/* harmony export */   fx: () => (/* binding */ EnhancedThemeProvider)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(1807);




// Enhanced theme context
var EnhancedThemeContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({
  isDarkMode: false,
  themeMode: 'light',
  // 'light', 'dark', 'system'
  toggleDarkMode: function toggleDarkMode() {},
  setThemeMode: function setThemeMode() {},
  colors: {},
  systemPrefersDark: false
});

// Theme colors for light and dark modes
var lightTheme = {
  primary: '#1890ff',
  primaryHover: '#40a9ff',
  secondary: '#52c41a',
  background: '#ffffff',
  backgroundSecondary: '#f5f5f5',
  backgroundTertiary: '#fafafa',
  surface: '#ffffff',
  text: '#000000d9',
  textSecondary: '#00000073',
  textTertiary: '#00000040',
  border: '#d9d9d9',
  borderLight: '#f0f0f0',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff'
};
var darkTheme = {
  primary: '#1890ff',
  primaryHover: '#40a9ff',
  secondary: '#52c41a',
  background: '#141414',
  backgroundSecondary: '#1f1f1f',
  backgroundTertiary: '#262626',
  surface: '#1f1f1f',
  text: '#ffffffd9',
  textSecondary: '#ffffff73',
  textTertiary: '#ffffff40',
  border: '#434343',
  borderLight: '#303030',
  shadow: 'rgba(0, 0, 0, 0.3)',
  shadowLight: 'rgba(0, 0, 0, 0.2)',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff'
};

// Enhanced Theme Provider Component
var EnhancedThemeProvider = function EnhancedThemeProvider(_ref) {
  var children = _ref.children;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      // Check localStorage first, then system preference
      var savedTheme = localStorage.getItem('app-theme-mode');
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        return savedTheme;
      }
      return 'system';
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    themeMode = _useState2[0],
    setThemeMode = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    systemPrefersDark = _useState4[0],
    setSystemPrefersDark = _useState4[1];

  // Calculate effective dark mode state
  var isDarkMode = themeMode === 'dark' || themeMode === 'system' && systemPrefersDark;

  // Get current theme colors
  var colors = isDarkMode ? darkTheme : lightTheme;

  // Listen for system theme changes
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    var handleChange = function handleChange(e) {
      setSystemPrefersDark(e.matches);
    };
    mediaQuery.addEventListener('change', handleChange);
    return function () {
      return mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  // Apply theme to document
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var root = document.documentElement;

    // Set CSS custom properties
    Object.entries(colors).forEach(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        value = _ref3[1];
      root.style.setProperty("--color-".concat(key), value);
    });

    // Set data attribute for CSS selectors
    root.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

    // Add/remove dark class for compatibility
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }

    // Update meta theme-color for mobile browsers
    var metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', colors.primary);
    }
  }, [colors, isDarkMode]);

  // Save theme preference to localStorage
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    localStorage.setItem('app-theme-mode', themeMode);
  }, [themeMode]);

  // Toggle between light and dark mode
  var toggleDarkMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    setThemeMode(function (current) {
      if (current === 'system') {
        return systemPrefersDark ? 'light' : 'dark';
      }
      return current === 'light' ? 'dark' : 'light';
    });
  }, [systemPrefersDark]);

  // Set specific theme mode
  var handleSetThemeMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (mode) {
    if (['light', 'dark', 'system'].includes(mode)) {
      setThemeMode(mode);
    }
  }, []);

  // Ant Design theme configuration
  var antdThemeConfig = {
    algorithm: isDarkMode ? antd__WEBPACK_IMPORTED_MODULE_2__/* .theme */ .w4.darkAlgorithm : antd__WEBPACK_IMPORTED_MODULE_2__/* .theme */ .w4.defaultAlgorithm,
    token: {
      colorPrimary: colors.primary,
      colorSuccess: colors.success,
      colorWarning: colors.warning,
      colorError: colors.error,
      colorInfo: colors.info,
      colorBgBase: colors.background,
      colorBgContainer: colors.surface,
      colorText: colors.text,
      colorTextSecondary: colors.textSecondary,
      colorBorder: colors.border,
      borderRadius: 6,
      wireframe: false
    },
    components: {
      Layout: {
        bodyBg: colors.background,
        headerBg: colors.surface,
        footerBg: colors.surface
      },
      Card: {
        colorBgContainer: colors.surface
      },
      Menu: {
        colorBgContainer: colors.surface
      }
    }
  };
  var contextValue = {
    isDarkMode: isDarkMode,
    themeMode: themeMode,
    toggleDarkMode: toggleDarkMode,
    setThemeMode: handleSetThemeMode,
    colors: colors,
    systemPrefersDark: systemPrefersDark
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnhancedThemeContext.Provider, {
    value: contextValue
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(antd__WEBPACK_IMPORTED_MODULE_2__/* .ConfigProvider */ .sG, {
    theme: antdThemeConfig
  }, children));
};

// Custom hook to use the enhanced theme context
var useEnhancedTheme = function useEnhancedTheme() {
  var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EnhancedThemeContext);
  if (!context) {
    throw new Error('useEnhancedTheme must be used within an EnhancedThemeProvider');
  }
  return context;
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (EnhancedThemeContext)));

/***/ }),

/***/ 87169:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useAIDesignSuggestions */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71468);
/* harmony import */ var _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(3288);
/* harmony import */ var _redux_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(81616);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






/**
 * Custom hook for managing AI design suggestions
 * Provides layout suggestions, component combinations, and app analysis
 */
var useAIDesignSuggestions = function useAIDesignSuggestions() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$autoRefresh = options.autoRefresh,
    autoRefresh = _options$autoRefresh === void 0 ? true : _options$autoRefresh,
    _options$refreshInter = options.refreshInterval,
    refreshInterval = _options$refreshInter === void 0 ? 30000 : _options$refreshInter,
    _options$enableCache = options.enableCache,
    enableCache = _options$enableCache === void 0 ? true : _options$enableCache,
    _options$context = options.context,
    context = _options$context === void 0 ? {} : _options$context;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useDispatch */ .wA)();

  // Get app state from Redux
  var components = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || state.components || [];
  });
  var layouts = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$app2;
    return ((_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.layouts) || state.layouts || [];
  });
  var selectedComponent = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$ui;
    return ((_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.selectedComponent) || null;
  });

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      layout: [],
      combinations: [],
      analysis: null
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    suggestions = _useState2[0],
    setSuggestions = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      layout: false,
      combinations: false,
      analysis: false
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    lastRefresh = _useState8[0],
    setLastRefresh = _useState8[1];

  // Refs for cleanup
  var refreshIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);

  // Load all suggestions
  var loadSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
    var force,
      _yield$Promise$allSet,
      _yield$Promise$allSet2,
      layoutResponse,
      combinationsResponse,
      analysisResponse,
      layoutSuggestions,
      combinationSuggestions,
      analysis,
      _args = arguments,
      _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          force = _args.length > 0 && _args[0] !== undefined ? _args[0] : false;
          if (!(!components || components.length === 0)) {
            _context.next = 1;
            break;
          }
          setSuggestions({
            layout: [],
            combinations: [],
            analysis: null
          });
          return _context.abrupt("return");
        case 1:
          // Abort previous requests
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          abortControllerRef.current = new AbortController();
          setError(null);
          setLoading({
            layout: true,
            combinations: true,
            analysis: true
          });
          _context.prev = 2;
          // Clear cache if force refresh
          if (force && enableCache) {
            _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.clearCache();
          }

          // Load all suggestions in parallel
          _context.next = 3;
          return Promise.allSettled([_services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context), _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context), _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.analyzeAppStructure(components, layouts)]);
        case 3:
          _yield$Promise$allSet = _context.sent;
          _yield$Promise$allSet2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_yield$Promise$allSet, 3);
          layoutResponse = _yield$Promise$allSet2[0];
          combinationsResponse = _yield$Promise$allSet2[1];
          analysisResponse = _yield$Promise$allSet2[2];
          // Process layout suggestions
          layoutSuggestions = layoutResponse.status === 'fulfilled' ? layoutResponse.value.suggestions || [] : []; // Process combination suggestions
          combinationSuggestions = combinationsResponse.status === 'fulfilled' ? combinationsResponse.value.suggestions || [] : []; // Process analysis
          analysis = analysisResponse.status === 'fulfilled' ? analysisResponse.value.analysis || null : null;
          setSuggestions({
            layout: layoutSuggestions,
            combinations: combinationSuggestions,
            analysis: analysis
          });
          setLastRefresh(new Date());

          // Log any errors
          [layoutResponse, combinationsResponse, analysisResponse].forEach(function (response, index) {
            if (response.status === 'rejected') {
              var names = ['layout', 'combinations', 'analysis'];
              console.warn("Failed to load ".concat(names[index], " suggestions:"), response.reason);
            }
          });
          _context.next = 5;
          break;
        case 4:
          _context.prev = 4;
          _t = _context["catch"](2);
          if (_t.name !== 'AbortError') {
            setError("Failed to load suggestions: ".concat(_t.message));
          }
        case 5:
          _context.prev = 5;
          setLoading({
            layout: false,
            combinations: false,
            analysis: false
          });
          return _context.finish(5);
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[2, 4, 5, 6]]);
  })), [components, layouts, selectedComponent, context, enableCache]);

  // Load specific suggestion type
  var loadLayoutSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2() {
    var response, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return");
        case 1:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: true
            });
          });
          setError(null);
          _context2.prev = 2;
          _context2.next = 3;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context);
        case 3:
          response = _context2.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: response.suggestions || []
            });
          });
          _context2.next = 5;
          break;
        case 4:
          _context2.prev = 4;
          _t2 = _context2["catch"](2);
          setError("Failed to load layout suggestions: ".concat(_t2.message));
        case 5:
          _context2.prev = 5;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: false
            });
          });
          return _context2.finish(5);
        case 6:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[2, 4, 5, 6]]);
  })), [components, layouts, context]);
  var loadCombinationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3() {
    var response, _t3;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context3.next = 1;
            break;
          }
          return _context3.abrupt("return");
        case 1:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: true
            });
          });
          setError(null);
          _context3.prev = 2;
          _context3.next = 3;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context);
        case 3:
          response = _context3.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: response.suggestions || []
            });
          });
          _context3.next = 5;
          break;
        case 4:
          _context3.prev = 4;
          _t3 = _context3["catch"](2);
          setError("Failed to load combination suggestions: ".concat(_t3.message));
        case 5:
          _context3.prev = 5;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: false
            });
          });
          return _context3.finish(5);
        case 6:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[2, 4, 5, 6]]);
  })), [components, selectedComponent, context]);

  // Apply layout suggestion
  var applyLayoutSuggestion = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    try {
      // This would integrate with your layout system
      // For now, we'll dispatch a generic action
      console.log('Applying layout suggestion:', suggestion);

      // You could dispatch a specific action here
      // dispatch(applyLayout(suggestion));

      return true;
    } catch (err) {
      setError("Failed to apply layout suggestion: ".concat(err.message));
      return false;
    }
  }, []);

  // Apply component combination suggestion
  var applyComponentCombination = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    try {
      if (suggestion.missing_components && suggestion.missing_components.length > 0) {
        // Add missing components
        suggestion.missing_components.forEach(function (componentType) {
          var newComponent = {
            type: componentType,
            props: {},
            id: "".concat(componentType, "-").concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9))
          };
          dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_7__/* .addComponent */ .X8)(newComponent.type, newComponent.props));
        });
      }
      console.log('Applied component combination:', suggestion);
      return true;
    } catch (err) {
      setError("Failed to apply component combination: ".concat(err.message));
      return false;
    }
  }, [dispatch]);

  // Refresh suggestions
  var refresh = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    loadSuggestions(true);
  }, [loadSuggestions]);

  // Clear error
  var clearError = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    setError(null);
  }, []);

  // Setup auto-refresh
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(function () {
        loadSuggestions();
      }, refreshInterval);
      return function () {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval]); // Removed loadSuggestions to prevent infinite re-renders

  // Load suggestions when components change (but not on every loadSuggestions change)
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    loadSuggestions();
  }, [components.length, selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id]); // Only depend on specific values that should trigger reload

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    return function () {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  return {
    // Data
    suggestions: suggestions,
    loading: loading,
    error: error,
    lastRefresh: lastRefresh,
    // Actions
    loadSuggestions: loadSuggestions,
    loadLayoutSuggestions: loadLayoutSuggestions,
    loadCombinationSuggestions: loadCombinationSuggestions,
    applyLayoutSuggestion: applyLayoutSuggestion,
    applyComponentCombination: applyComponentCombination,
    refresh: refresh,
    clearError: clearError,
    // Computed values
    hasLayoutSuggestions: suggestions.layout.length > 0,
    hasCombinationSuggestions: suggestions.combinations.length > 0,
    hasAnalysis: suggestions.analysis !== null,
    isLoading: loading.layout || loading.combinations || loading.analysis,
    // Component counts for display
    componentCount: components.length,
    layoutCount: layouts.length,
    selectedComponentType: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.type) || null
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAIDesignSuggestions);

/***/ })

}]);