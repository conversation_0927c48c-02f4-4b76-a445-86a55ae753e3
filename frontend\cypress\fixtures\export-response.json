{"success": true, "code": "import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"app\">\n      <header className=\"header\" style={{\n        backgroundColor: '#1890ff',\n        color: '#ffffff',\n        height: '200px',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center'\n      }}>\n        <h1>Hello World!</h1>\n        <p>Welcome to your new app built with App Builder</p>\n      </header>\n      \n      <div className=\"container\" style={{\n        padding: '40px 20px',\n        maxWidth: '1200px',\n        margin: '0 auto',\n        backgroundColor: '#ffffff'\n      }}>\n        <div className=\"text-content\" style={{\n          fontSize: '18px',\n          lineHeight: '1.6',\n          color: '#333333',\n          marginBottom: '30px'\n        }}>\n          This is your first app! You can drag and drop components, edit their properties, and export your creation as working code.\n        </div>\n        \n        <div className=\"button-group\">\n          <button \n            className=\"primary-button\"\n            style={{\n              backgroundColor: '#52c41a',\n              color: '#ffffff',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              fontSize: '16px',\n              border: 'none',\n              cursor: 'pointer',\n              marginRight: '15px'\n            }}\n            onClick={() => console.log('Get Started clicked')}\n          >\n            Get Started\n          </button>\n          \n          <button \n            className=\"secondary-button\"\n            style={{\n              backgroundColor: 'transparent',\n              color: '#1890ff',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              fontSize: '16px',\n              border: '2px solid #1890ff',\n              cursor: 'pointer'\n            }}\n            onClick={() => console.log('Learn More clicked')}\n          >\n            Learn More\n          </button>\n        </div>\n      </div>\n      \n      <footer className=\"footer\" style={{\n        backgroundColor: '#f5f5f5',\n        textAlign: 'center',\n        padding: '20px',\n        color: '#666666',\n        fontSize: '14px'\n      }}>\n        © 2024 Your App. Built with App Builder.\n      </footer>\n    </div>\n  );\n}\n\nexport default App;", "format": "react", "files": {"App.jsx": "import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"app\">\n      <header className=\"header\" style={{\n        backgroundColor: '#1890ff',\n        color: '#ffffff',\n        height: '200px',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center'\n      }}>\n        <h1>Hello World!</h1>\n        <p>Welcome to your new app built with App Builder</p>\n      </header>\n      \n      <div className=\"container\" style={{\n        padding: '40px 20px',\n        maxWidth: '1200px',\n        margin: '0 auto',\n        backgroundColor: '#ffffff'\n      }}>\n        <div className=\"text-content\" style={{\n          fontSize: '18px',\n          lineHeight: '1.6',\n          color: '#333333',\n          marginBottom: '30px'\n        }}>\n          This is your first app! You can drag and drop components, edit their properties, and export your creation as working code.\n        </div>\n        \n        <div className=\"button-group\">\n          <button \n            className=\"primary-button\"\n            style={{\n              backgroundColor: '#52c41a',\n              color: '#ffffff',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              fontSize: '16px',\n              border: 'none',\n              cursor: 'pointer',\n              marginRight: '15px'\n            }}\n            onClick={() => console.log('Get Started clicked')}\n          >\n            Get Started\n          </button>\n          \n          <button \n            className=\"secondary-button\"\n            style={{\n              backgroundColor: 'transparent',\n              color: '#1890ff',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              fontSize: '16px',\n              border: '2px solid #1890ff',\n              cursor: 'pointer'\n            }}\n            onClick={() => console.log('Learn More clicked')}\n          >\n            Learn More\n          </button>\n        </div>\n      </div>\n      \n      <footer className=\"footer\" style={{\n        backgroundColor: '#f5f5f5',\n        textAlign: 'center',\n        padding: '20px',\n        color: '#666666',\n        fontSize: '14px'\n      }}>\n        © 2024 Your App. Built with App Builder.\n      </footer>\n    </div>\n  );\n}\n\nexport default App;", "App.css": ".app {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.header {\n  flex-shrink: 0;\n}\n\n.container {\n  flex: 1;\n}\n\n.footer {\n  flex-shrink: 0;\n}\n\n.button-group {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.primary-button:hover {\n  background-color: #73d13d !important;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);\n}\n\n.secondary-button:hover {\n  background-color: #1890ff !important;\n  color: #ffffff !important;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);\n}\n\n@media (max-width: 768px) {\n  .header {\n    height: 150px !important;\n    padding: 20px;\n  }\n  \n  .header h1 {\n    font-size: 24px;\n  }\n  \n  .container {\n    padding: 20px 15px;\n  }\n  \n  .text-content {\n    font-size: 16px !important;\n  }\n  \n  .button-group {\n    flex-direction: column;\n    align-items: center;\n  }\n  \n  .primary-button,\n  .secondary-button {\n    width: 100%;\n    max-width: 200px;\n    margin-right: 0 !important;\n  }\n}", "package.json": "{\n  \"name\": \"my-app-builder-app\",\n  \"version\": \"1.0.0\",\n  \"description\": \"An app built with App Builder\",\n  \"main\": \"src/App.jsx\",\n  \"scripts\": {\n    \"start\": \"react-scripts start\",\n    \"build\": \"react-scripts build\",\n    \"test\": \"react-scripts test\",\n    \"eject\": \"react-scripts eject\"\n  },\n  \"dependencies\": {\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\",\n    \"react-scripts\": \"5.0.1\"\n  },\n  \"browserslist\": {\n    \"production\": [\n      \">0.2%\",\n      \"not dead\",\n      \"not op_mini all\"\n    ],\n    \"development\": [\n      \"last 1 chrome version\",\n      \"last 1 firefox version\",\n      \"last 1 safari version\"\n    ]\n  },\n  \"keywords\": [\n    \"react\",\n    \"app-builder\",\n    \"generated\"\n  ],\n  \"author\": \"App Builder\",\n  \"license\": \"MIT\"\n}", "README.md": "# My App Builder App\n\nThis app was generated using App Builder - a visual drag-and-drop application builder.\n\n## Getting Started\n\n1. Install dependencies:\n   ```bash\n   npm install\n   ```\n\n2. Start the development server:\n   ```bash\n   npm start\n   ```\n\n3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.\n\n## Available Scripts\n\n- `npm start` - Runs the app in development mode\n- `npm run build` - Builds the app for production\n- `npm test` - Launches the test runner\n- `npm run eject` - Ejects from Create React App (one-way operation)\n\n## Features\n\n- Responsive design that works on all devices\n- Modern React components\n- Clean, semantic HTML structure\n- Accessible design following WCAG guidelines\n- Optimized for performance\n\n## Customization\n\nYou can customize this app by:\n\n1. Editing the component styles in `App.css`\n2. Modifying the component structure in `App.jsx`\n3. Adding new components and features\n4. Integrating with APIs and databases\n\n## Built With\n\n- [React](https://reactjs.org/) - JavaScript library for building user interfaces\n- [Create React App](https://create-react-app.dev/) - Set up a modern web app by running one command\n- [App Builder](https://appbuilder.com) - Visual application builder\n\n## Learn More\n\nTo learn more about React and modern web development:\n\n- [React Documentation](https://reactjs.org/docs/getting-started.html)\n- [Create React App Documentation](https://create-react-app.dev/docs/getting-started/)\n- [App Builder Documentation](https://docs.appbuilder.com)\n\n## Support\n\nIf you need help with this app or App Builder, please visit our [support center](https://support.appbuilder.com)."}, "metadata": {"generatedAt": "2024-01-01T12:00:00Z", "framework": "react", "version": "1.0.0", "components": [{"type": "header", "count": 1}, {"type": "container", "count": 1}, {"type": "text", "count": 1}, {"type": "button", "count": 2}, {"type": "footer", "count": 1}], "totalLines": 156, "estimatedSize": "12.5KB"}}