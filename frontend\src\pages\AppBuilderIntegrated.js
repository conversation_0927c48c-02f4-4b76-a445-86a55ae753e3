import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Layout, Tabs, Card, Button, Space, Typography, Alert, Row, Col, Divider } from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  SaveOutlined,
  ShareAltOutlined,
  RocketOutlined,
  QuestionCircleOutlined,
  BugOutlined,
  DatabaseOutlined,
  DashboardOutlined,
  ExportOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

// Import the core feature components with fallbacks
let ComponentBuilder, LayoutDesigner, ThemeManager, WebSocketManager;

// Import the new MVP feature components with fallbacks
let IntegratedTutorialAssistant, TestingTools, DataManagementDemo, EnhancedPerformanceMonitor, EnhancedCodeExporter;

try {
  ComponentBuilder = require('../components/enhanced/ComponentBuilder').default;
} catch (error) {
  console.warn('ComponentBuilder not available, using fallback');
  ComponentBuilder = () => (
    <Card title="Component Builder">
      <p>Component Builder is loading...</p>
      <Button type="primary">Add Button</Button>
      <Button style={{ marginLeft: '8px' }}>Add Text</Button>
      <Button style={{ marginLeft: '8px' }}>Add Input</Button>
    </Card>
  );
}

try {
  LayoutDesigner = require('../components/enhanced/LayoutDesigner').default;
} catch (error) {
  console.warn('LayoutDesigner not available, using fallback');
  LayoutDesigner = () => (
    <Card title="Layout Designer">
      <p>Layout Designer is loading...</p>
      <Button type="primary">Grid Layout</Button>
      <Button style={{ marginLeft: '8px' }}>Flex Layout</Button>
    </Card>
  );
}

try {
  ThemeManager = require('../components/enhanced/ThemeManager').default;
} catch (error) {
  console.warn('ThemeManager not available, using fallback');
  ThemeManager = () => (
    <Card title="Theme Manager">
      <p>Theme Manager is loading...</p>
      <Button type="primary">Primary Color</Button>
      <Button style={{ marginLeft: '8px' }}>Typography</Button>
    </Card>
  );
}

try {
  WebSocketManager = require('../components/websocket/WebSocketManager').default;
} catch (error) {
  console.warn('WebSocketManager not available, using fallback');
  WebSocketManager = ({ onConnectionChange }) => (
    <Card title="WebSocket Manager">
      <Space direction="vertical" style={{ width: '100%' }}>
        <p>Real-time collaboration features</p>
        <Button
          type="primary"
          onClick={() => {
            console.log('WebSocket connection simulated');
            if (onConnectionChange) onConnectionChange(true);
          }}
        >
          Connect
        </Button>
        <Button
          onClick={() => {
            console.log('WebSocket disconnection simulated');
            if (onConnectionChange) onConnectionChange(false);
          }}
        >
          Disconnect
        </Button>
      </Space>
    </Card>
  );
}

// Import Tutorial Assistant
try {
  IntegratedTutorialAssistant = require('../components/tutorial/IntegratedTutorialAssistant').default;
} catch (error) {
  console.warn('IntegratedTutorialAssistant not available, using fallback');
  IntegratedTutorialAssistant = () => (
    <Card title="Tutorial Assistant" style={{ height: '100%' }}>
      <p>Interactive tutorials and context-aware help system</p>
      <Button type="primary">Start Tutorial</Button>
      <Button style={{ marginLeft: '8px' }}>View Help</Button>
    </Card>
  );
}

// Import Testing Tools
try {
  TestingTools = require('../components/testing/TestingTools').default;
} catch (error) {
  console.warn('TestingTools not available, using fallback');
  TestingTools = () => (
    <Card title="Testing Tools" style={{ height: '100%' }}>
      <p>Component testing, validation, and accessibility checks</p>
      <Button type="primary">Run Tests</Button>
      <Button style={{ marginLeft: '8px' }}>Accessibility Check</Button>
    </Card>
  );
}

// Import Data Management
try {
  DataManagementDemo = require('../components/enhanced/DataManagementDemo').default;
} catch (error) {
  console.warn('DataManagementDemo not available, using fallback');
  DataManagementDemo = () => (
    <Card title="Data Management" style={{ height: '100%' }}>
      <p>Data binding, state management, and flow visualization</p>
      <Button type="primary">Manage Data</Button>
      <Button style={{ marginLeft: '8px' }}>View Flow</Button>
    </Card>
  );
}

// Import Performance Monitor
try {
  EnhancedPerformanceMonitor = require('../components/performance/EnhancedPerformanceMonitor').default;
} catch (error) {
  console.warn('EnhancedPerformanceMonitor not available, using fallback');
  EnhancedPerformanceMonitor = () => (
    <Card title="Performance Monitor" style={{ height: '100%' }}>
      <p>Bundle size tracking and optimization suggestions</p>
      <Button type="primary">Analyze Performance</Button>
      <Button style={{ marginLeft: '8px' }}>View Metrics</Button>
    </Card>
  );
}

// Import Enhanced Code Exporter
try {
  EnhancedCodeExporter = require('../components/export/EnhancedCodeExporter').default;
} catch (error) {
  console.warn('EnhancedCodeExporter not available, using fallback');
  EnhancedCodeExporter = () => (
    <Card title="Enhanced Export" style={{ height: '100%' }}>
      <p>Multi-framework export with TypeScript generation</p>
      <Button type="primary">Export React</Button>
      <Button style={{ marginLeft: '8px' }}>Export Vue</Button>
      <Button style={{ marginLeft: '8px' }}>Export Angular</Button>
    </Card>
  );
}

const { Title, Text } = Typography;

const IntegratedContainer = styled.div`
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden;
`;

const HeaderBar = styled.div`
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
`;

const ContentArea = styled.div`
  height: calc(100vh - 80px);
  overflow: auto;
  padding: 24px;
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 16px;
  height: 100%;
  min-height: 800px;
`;

const FeatureCard = styled(Card)`
  height: 100%;
  
  .ant-card-body {
    height: calc(100% - 57px);
    overflow: auto;
  }
`;

const DemoArea = styled(Card)`
  grid-column: 1 / -1;
  margin-top: 24px;
  
  .demo-canvas {
    min-height: 300px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 20px;
    background: #fafafa;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
`;

/**
 * AppBuilderIntegrated - Comprehensive integrated app builder with all core features
 */
const AppBuilderIntegrated = () => {
  const [activeFeature, setActiveFeature] = useState('overview');
  const [projectData, setProjectData] = useState({
    components: [],
    layout: { type: 'grid', columns: 3 },
    theme: { primaryColor: '#1890ff', fontFamily: 'Inter' },
    websocket: { connected: false, collaborators: [] }
  });
  const [demoApp, setDemoApp] = useState({
    components: [],
    layout: null,
    theme: null,
    isBuilding: false
  });

  // Sample app creation workflow
  const createSampleApp = useCallback(async () => {
    setDemoApp(prev => ({ ...prev, isBuilding: true }));

    // Step 1: Add components
    const sampleComponents = [
      { id: 'btn1', type: 'button', props: { text: 'Get Started', type: 'primary' }, position: { x: 50, y: 50 } },
      { id: 'txt1', type: 'text', props: { text: 'Welcome to App Builder', size: 'large' }, position: { x: 50, y: 100 } },
      { id: 'card1', type: 'card', props: { title: 'Feature Card', content: 'This is a sample card' }, position: { x: 200, y: 50 } },
      { id: 'input1', type: 'input', props: { placeholder: 'Enter your name' }, position: { x: 50, y: 200 } }
    ];

    // Step 2: Apply layout
    const sampleLayout = {
      type: 'grid',
      columns: 2,
      gap: '16px',
      responsive: true
    };

    // Step 3: Apply theme
    const sampleTheme = {
      primaryColor: '#52c41a',
      fontFamily: 'Inter, sans-serif',
      borderRadius: '8px',
      spacing: '16px'
    };

    // Simulate building process
    await new Promise(resolve => setTimeout(resolve, 1000));

    setDemoApp({
      components: sampleComponents,
      layout: sampleLayout,
      theme: sampleTheme,
      isBuilding: false
    });

    setProjectData(prev => ({
      ...prev,
      components: sampleComponents,
      layout: sampleLayout,
      theme: sampleTheme
    }));
  }, []);

  const tabItems = useMemo(() => [
    {
      key: 'overview',
      label: (
        <Space>
          <RocketOutlined />
          <span>Integrated Builder</span>
        </Space>
      ),
      children: (
        <div>
          <Alert
            message="App Builder Enhanced - All Features Integrated"
            description="This integrated view demonstrates all four core features working together: Component Builder, Layout Designer, Theme Manager, and WebSocket Manager."
            type="success"
            showIcon
            style={{ marginBottom: '24px' }}
          />

          <FeatureGrid>
            <FeatureCard
              title={<Space><AppstoreOutlined />Component Builder</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('components')}>Open</Button>}
            >
              <ComponentBuilder
                onComponentAdd={(component) => {
                  setProjectData(prev => ({
                    ...prev,
                    components: [...prev.components, component]
                  }));
                }}
              />
            </FeatureCard>

            <FeatureCard
              title={<Space><LayoutOutlined />Layout Designer</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('layouts')}>Open</Button>}
            >
              <LayoutDesigner
                components={projectData.components}
                onLayoutChange={(layout) => {
                  setProjectData(prev => ({ ...prev, layout }));
                }}
              />
            </FeatureCard>

            <FeatureCard
              title={<Space><BgColorsOutlined />Theme Manager</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('themes')}>Open</Button>}
            >
              <ThemeManager
                currentTheme={projectData.theme}
                onThemeChange={(theme) => {
                  setProjectData(prev => ({ ...prev, theme }));
                }}
              />
            </FeatureCard>

            <FeatureCard
              title={<Space><ApiOutlined />WebSocket Manager</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('websocket')}>Open</Button>}
            >
              <WebSocketManager
                onConnectionChange={(status) => {
                  setProjectData(prev => ({
                    ...prev,
                    websocket: { ...prev.websocket, connected: status }
                  }));
                }}
              />
            </FeatureCard>

            <FeatureCard
              title={<Space><QuestionCircleOutlined />Tutorial Assistant</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('tutorial')}>Open</Button>}
            >
              <div style={{ padding: '16px', textAlign: 'center' }}>
                <p>Interactive tutorials and context-aware help system</p>
                <Button type="primary" size="small">Start Tutorial</Button>
              </div>
            </FeatureCard>

            <FeatureCard
              title={<Space><BugOutlined />Testing Tools</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('testing')}>Open</Button>}
            >
              <div style={{ padding: '16px', textAlign: 'center' }}>
                <p>Component testing, validation, and accessibility checks</p>
                <Button type="primary" size="small">Run Tests</Button>
              </div>
            </FeatureCard>

            <FeatureCard
              title={<Space><DatabaseOutlined />Data Management</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('data')}>Open</Button>}
            >
              <div style={{ padding: '16px', textAlign: 'center' }}>
                <p>Data binding, state management, and flow visualization</p>
                <Button type="primary" size="small">Manage Data</Button>
              </div>
            </FeatureCard>

            <FeatureCard
              title={<Space><DashboardOutlined />Performance Monitor</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('performance')}>Open</Button>}
            >
              <div style={{ padding: '16px', textAlign: 'center' }}>
                <p>Bundle size tracking and optimization suggestions</p>
                <Button type="primary" size="small">Analyze</Button>
              </div>
            </FeatureCard>

            <FeatureCard
              title={<Space><ExportOutlined />Enhanced Export</Space>}
              extra={<Button size="small" onClick={() => setActiveFeature('export')}>Open</Button>}
            >
              <div style={{ padding: '16px', textAlign: 'center' }}>
                <p>Multi-framework export with TypeScript generation</p>
                <Button type="primary" size="small">Export Code</Button>
              </div>
            </FeatureCard>
          </FeatureGrid>

          <DemoArea title="Sample App Demonstration">
            <Row gutter={[24, 24]}>
              <Col xs={24} md={12}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Title level={4}>Create Sample App</Title>
                  <Text type="secondary">
                    Demonstrate the complete workflow from component creation to styled, collaborative application.
                  </Text>
                  <Button
                    type="primary"
                    size="large"
                    icon={<PlayCircleOutlined />}
                    onClick={createSampleApp}
                    loading={demoApp.isBuilding}
                  >
                    Build Sample App
                  </Button>
                </Space>
              </Col>
              <Col xs={24} md={12}>
                <div className="demo-canvas">
                  {demoApp.isBuilding ? (
                    <div style={{ textAlign: 'center' }}>
                      <Title level={4}>Building Sample App...</Title>
                      <Text type="secondary">Adding components, applying layout, styling theme...</Text>
                    </div>
                  ) : demoApp.components.length > 0 ? (
                    <div style={{ width: '100%' }}>
                      <Title level={4}>Sample App Preview</Title>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: `repeat(${demoApp.layout?.columns || 2}, 1fr)`,
                        gap: demoApp.layout?.gap || '16px',
                        marginTop: '16px'
                      }}>
                        {demoApp.components.map(component => (
                          <div
                            key={component.id}
                            style={{
                              padding: '12px',
                              border: '1px solid #d9d9d9',
                              borderRadius: demoApp.theme?.borderRadius || '4px',
                              background: '#fff'
                            }}
                          >
                            <Text strong>{component.type}: </Text>
                            <Text>{component.props.text || component.props.title || component.props.placeholder}</Text>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div style={{ textAlign: 'center' }}>
                      <Title level={4} type="secondary">Ready to Build</Title>
                      <Text type="secondary">Click "Build Sample App" to see all features in action</Text>
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          </DemoArea>
        </div>
      )
    },
    {
      key: 'components',
      label: <Space><AppstoreOutlined />Component Builder</Space>,
      children: <ComponentBuilder />
    },
    {
      key: 'layouts',
      label: <Space><LayoutOutlined />Layout Designer</Space>,
      children: <LayoutDesigner />
    },
    {
      key: 'themes',
      label: <Space><BgColorsOutlined />Theme Manager</Space>,
      children: <ThemeManager />
    },
    {
      key: 'websocket',
      label: <Space><ApiOutlined />WebSocket Manager</Space>,
      children: <WebSocketManager />
    },
    {
      key: 'tutorial',
      label: <Space><QuestionCircleOutlined />Tutorial Assistant</Space>,
      children: (
        <IntegratedTutorialAssistant
          enableAutoStart={false}
          showContextualHelp={true}
          onTutorialComplete={(tutorialId) => {
            console.log('Tutorial completed:', tutorialId);
          }}
          onTutorialSkip={(tutorialId) => {
            console.log('Tutorial skipped:', tutorialId);
          }}
          features={['components', 'layouts', 'themes', 'websocket', 'testing', 'data', 'performance', 'export']}
        />
      )
    },
    {
      key: 'testing',
      label: <Space><BugOutlined />Testing Tools</Space>,
      children: (
        <TestingTools
          components={projectData.components || []}
          onTestComplete={(testType, results) => {
            console.log('Test completed:', testType, results);
          }}
          onTestStart={(testType) => {
            console.log('Test started:', testType);
          }}
          enabledTests={['component', 'accessibility', 'performance', 'responsive']}
          autoRun={false}
          showMetrics={true}
          compact={false}
        />
      )
    },
    {
      key: 'data',
      label: <Space><DatabaseOutlined />Data Management</Space>,
      children: <DataManagementDemo />
    },
    {
      key: 'performance',
      label: <Space><DashboardOutlined />Performance Monitor</Space>,
      children: (
        <EnhancedPerformanceMonitor
          enabled={true}
          initiallyMinimized={false}
          refreshInterval={5000}
          wsUrl="ws://localhost:8000/ws"
          showNetworkStatus={true}
          showMemoryUsage={true}
          showRenderCounts={true}
          showWebSocketStatus={true}
        />
      )
    },
    {
      key: 'export',
      label: <Space><ExportOutlined />Enhanced Export</Space>,
      children: (
        <EnhancedCodeExporter
          components={projectData.components || []}
          layouts={projectData.layout ? [projectData.layout] : []}
          theme={projectData.theme || {}}
          onExport={(exportData) => {
            console.log('Code exported:', exportData);
          }}
          onPreview={(previewData) => {
            console.log('Code preview:', previewData);
          }}
          compact={false}
        />
      )
    }
  ], [projectData, demoApp, createSampleApp]);

  return (
    <IntegratedContainer>
      <HeaderBar>
        <Space>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            App Builder Enhanced
          </Title>
          <Text type="secondary">Integrated Development Environment</Text>
        </Space>
        <Space>
          <Button icon={<SaveOutlined />}>Save Project</Button>
          <Button icon={<ShareAltOutlined />}>Share</Button>
          <Button type="primary" icon={<PlayCircleOutlined />}>Preview</Button>
        </Space>
      </HeaderBar>

      <ContentArea>
        <Tabs
          activeKey={activeFeature}
          onChange={setActiveFeature}
          type="card"
          size="large"
          items={tabItems}
        />
      </ContentArea>
    </IntegratedContainer>
  );
};

export default AppBuilderIntegrated;
