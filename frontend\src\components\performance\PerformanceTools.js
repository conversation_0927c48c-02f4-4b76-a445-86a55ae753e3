/**
 * Performance Tools Component
 * 
 * Comprehensive performance analysis tools for the App Builder including
 * bundle size tracking, render performance metrics, and optimization suggestions.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Alert,
  Progress,
  List,
  Statistic,
  Tag,
  Tooltip,
  Modal,
  Row,
  Col,
  Table,
  Switch,
  Select,
  notification,
  Badge,
  Divider
} from 'antd';
import {
  ThunderboltOutlined,
  DashboardOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
  BugOutlined,
  RocketOutlined,
  MonitorOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  HddOutlined,
  LineChartOutlined,

  BulbOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// Styled Components
const PerformanceContainer = styled.div`
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
`;

const PerformanceChart = styled.div`
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const OptimizationCard = styled(Card)`
  margin-bottom: 12px;
  
  .ant-card-head {
    background: ${props => {
    switch (props.severity) {
      case 'high': return '#fff2f0';
      case 'medium': return '#fffbe6';
      case 'low': return '#f6ffed';
      default: return '#fafafa';
    }
  }};
  }
`;

// Mock performance data
const generateMockPerformanceData = () => ({
  coreWebVitals: {
    fcp: { value: 1.2, threshold: 1.8, status: 'good' },
    lcp: { value: 2.1, threshold: 2.5, status: 'good' },
    fid: { value: 45, threshold: 100, status: 'good' },
    cls: { value: 0.08, threshold: 0.1, status: 'good' },
    ttfb: { value: 0.6, threshold: 0.8, status: 'good' }
  },
  bundleSize: {
    total: 245.6,
    javascript: 180.2,
    css: 45.8,
    images: 19.6,
    threshold: 250
  },
  renderMetrics: {
    componentsRendered: 24,
    averageRenderTime: 12.5,
    slowestComponent: 'DataTable',
    slowestRenderTime: 45.2,
    reRenders: 8
  },
  memoryUsage: {
    used: 42.8,
    total: 100,
    peak: 58.3,
    threshold: 80
  },
  networkRequests: {
    total: 12,
    cached: 8,
    failed: 1,
    averageTime: 245
  }
});

const generateOptimizationSuggestions = () => [
  {
    id: 'opt-1',
    title: 'Optimize Bundle Size',
    description: 'Consider code splitting for large components to reduce initial bundle size.',
    severity: 'medium',
    impact: 'high',
    effort: 'medium',
    category: 'bundle'
  },
  {
    id: 'opt-2',
    title: 'Reduce Re-renders',
    description: 'Use React.memo() for components that render frequently with same props.',
    severity: 'low',
    impact: 'medium',
    effort: 'low',
    category: 'rendering'
  },
  {
    id: 'opt-3',
    title: 'Implement Virtual Scrolling',
    description: 'Large lists should use virtual scrolling to improve performance.',
    severity: 'high',
    impact: 'high',
    effort: 'high',
    category: 'rendering'
  },
  {
    id: 'opt-4',
    title: 'Optimize Images',
    description: 'Use WebP format and lazy loading for better image performance.',
    severity: 'medium',
    impact: 'medium',
    effort: 'low',
    category: 'assets'
  }
];

/**
 * PerformanceTools Component
 */
const PerformanceTools = ({
  components = [],
  onOptimizationApply,
  realTimeMonitoring = true,
  showSuggestions = true,
  compact = false
}) => {
  // State
  const [activeTab, setActiveTab] = useState('overview');
  const [performanceData, setPerformanceData] = useState(generateMockPerformanceData());
  const [optimizationSuggestions, setOptimizationSuggestions] = useState(generateOptimizationSuggestions());
  const [monitoringEnabled, setMonitoringEnabled] = useState(realTimeMonitoring);
  const [selectedMetric, setSelectedMetric] = useState('all');
  const [showDetails, setShowDetails] = useState(false);

  // Computed metrics
  const performanceScore = useMemo(() => {
    const { coreWebVitals } = performanceData;
    const scores = Object.values(coreWebVitals).map(metric => {
      if (metric.status === 'good') return 100;
      if (metric.status === 'needs-improvement') return 75;
      return 50;
    });
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }, [performanceData]);

  const bundleHealthScore = useMemo(() => {
    const { bundleSize } = performanceData;
    const ratio = bundleSize.total / bundleSize.threshold;
    if (ratio <= 0.7) return 100;
    if (ratio <= 0.9) return 75;
    if (ratio <= 1.0) return 50;
    return 25;
  }, [performanceData]);

  // Core Web Vitals table columns
  const webVitalsColumns = [
    {
      title: 'Metric',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      render: (value, record) => (
        <Space>
          <Text>{value}</Text>
          <Text type="secondary">{record.unit}</Text>
        </Space>
      )
    },
    {
      title: 'Threshold',
      dataIndex: 'threshold',
      key: 'threshold',
      render: (threshold, record) => (
        <Text type="secondary">{threshold}{record.unit}</Text>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={
          status === 'good' ? 'green' :
            status === 'needs-improvement' ? 'orange' : 'red'
        }>
          {status.toUpperCase().replace('-', ' ')}
        </Tag>
      )
    }
  ];

  // Transform Core Web Vitals data for table
  const webVitalsData = useMemo(() => {
    const { coreWebVitals } = performanceData;
    return [
      { name: 'First Contentful Paint', ...coreWebVitals.fcp, unit: 's' },
      { name: 'Largest Contentful Paint', ...coreWebVitals.lcp, unit: 's' },
      { name: 'First Input Delay', ...coreWebVitals.fid, unit: 'ms' },
      { name: 'Cumulative Layout Shift', ...coreWebVitals.cls, unit: '' },
      { name: 'Time to First Byte', ...coreWebVitals.ttfb, unit: 's' }
    ];
  }, [performanceData]);

  // Handle optimization suggestion actions
  const handleApplyOptimization = useCallback((suggestion) => {
    if (onOptimizationApply) {
      onOptimizationApply(suggestion);
    }

    setOptimizationSuggestions(prev => prev.filter(s => s.id !== suggestion.id));

    notification.success({
      message: 'Optimization Applied',
      description: `${suggestion.title} has been applied successfully.`,
      duration: 3
    });
  }, [onOptimizationApply]);

  const handleDismissSuggestion = useCallback((suggestionId) => {
    setOptimizationSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    notification.info({
      message: 'Suggestion Dismissed',
      duration: 2
    });
  }, []);

  // Refresh performance data
  const refreshData = useCallback(() => {
    setPerformanceData(generateMockPerformanceData());
    notification.success({
      message: 'Performance Data Refreshed',
      duration: 2
    });
  }, []);

  // Real-time monitoring effect
  useEffect(() => {
    if (!monitoringEnabled) return;

    const interval = setInterval(() => {
      setPerformanceData(prev => ({
        ...prev,
        renderMetrics: {
          ...prev.renderMetrics,
          componentsRendered: prev.renderMetrics.componentsRendered + Math.floor(Math.random() * 3),
          averageRenderTime: Math.max(5, prev.renderMetrics.averageRenderTime + (Math.random() - 0.5) * 2),
          reRenders: prev.renderMetrics.reRenders + Math.floor(Math.random() * 2)
        },
        memoryUsage: {
          ...prev.memoryUsage,
          used: Math.min(prev.memoryUsage.total * 0.9,
            Math.max(20, prev.memoryUsage.used + (Math.random() - 0.5) * 5))
        }
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, [monitoringEnabled]);

  return (
    <PerformanceContainer>
      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              <DashboardOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              Performance Tools
            </Title>
          </Col>
          <Col>
            <Space>
              <Switch
                checked={monitoringEnabled}
                onChange={setMonitoringEnabled}
                checkedChildren="Live"
                unCheckedChildren="Static"
              />
              <Button icon={<ReloadOutlined />} onClick={refreshData}>
                Refresh
              </Button>
              <Button icon={<SettingOutlined />} onClick={() => setShowDetails(true)}>
                Details
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <MetricsGrid>
        <Card size="small">
          <Statistic
            title="Performance Score"
            value={performanceScore}
            suffix="/100"
            valueStyle={{ color: performanceScore >= 80 ? '#3f8600' : performanceScore >= 60 ? '#faad14' : '#cf1322' }}
            prefix={<ThunderboltOutlined />}
          />
        </Card>
        <Card size="small">
          <Statistic
            title="Bundle Size"
            value={performanceData.bundleSize.total}
            suffix="KB"
            valueStyle={{ color: bundleHealthScore >= 75 ? '#3f8600' : '#cf1322' }}
            prefix={<FileTextOutlined />}
          />
        </Card>
        <Card size="small">
          <Statistic
            title="Avg Render Time"
            value={performanceData.renderMetrics.averageRenderTime}
            suffix="ms"
            prefix={<ClockCircleOutlined />}
          />
        </Card>
        <Card size="small">
          <Statistic
            title="Memory Usage"
            value={Math.round((performanceData.memoryUsage.used / performanceData.memoryUsage.total) * 100)}
            suffix="%"
            valueStyle={{
              color: performanceData.memoryUsage.used / performanceData.memoryUsage.total > 0.8 ? '#cf1322' : '#3f8600'
            }}
            prefix={<HddOutlined />}
          />
        </Card>
      </MetricsGrid>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Overview" key="overview">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="Core Web Vitals" size="small">
                <Table
                  dataSource={webVitalsData}
                  columns={webVitalsColumns}
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Bundle Analysis" size="small">
                <div style={{ marginBottom: 16 }}>
                  <Progress
                    percent={Math.round((performanceData.bundleSize.total / performanceData.bundleSize.threshold) * 100)}
                    status={bundleHealthScore >= 75 ? 'success' : 'exception'}
                    format={() => `${performanceData.bundleSize.total}KB / ${performanceData.bundleSize.threshold}KB`}
                  />
                </div>
                <Row gutter={[8, 8]}>
                  <Col span={12}>
                    <Text type="secondary">JavaScript:</Text>
                    <br />
                    <Text strong>{performanceData.bundleSize.javascript}KB</Text>
                  </Col>
                  <Col span={12}>
                    <Text type="secondary">CSS:</Text>
                    <br />
                    <Text strong>{performanceData.bundleSize.css}KB</Text>
                  </Col>
                  <Col span={12}>
                    <Text type="secondary">Images:</Text>
                    <br />
                    <Text strong>{performanceData.bundleSize.images}KB</Text>
                  </Col>
                  <Col span={12}>
                    <Text type="secondary">Total:</Text>
                    <br />
                    <Text strong>{performanceData.bundleSize.total}KB</Text>
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24} lg={12}>
              <Card title="Render Performance" size="small">
                <Row gutter={[8, 8]}>
                  <Col span={12}>
                    <Statistic
                      title="Components Rendered"
                      value={performanceData.renderMetrics.componentsRendered}
                      prefix={<MonitorOutlined />}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="Re-renders"
                      value={performanceData.renderMetrics.reRenders}
                      prefix={<ReloadOutlined />}
                    />
                  </Col>
                  <Col span={24}>
                    <Divider style={{ margin: '12px 0' }} />
                    <Text type="secondary">Slowest Component:</Text>
                    <br />
                    <Text strong>{performanceData.renderMetrics.slowestComponent}</Text>
                    <Text type="secondary"> ({performanceData.renderMetrics.slowestRenderTime}ms)</Text>
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="Memory & Network" size="small">
                <Row gutter={[8, 8]}>
                  <Col span={24}>
                    <Text type="secondary">Memory Usage:</Text>
                    <Progress
                      percent={Math.round((performanceData.memoryUsage.used / performanceData.memoryUsage.total) * 100)}
                      format={() => `${performanceData.memoryUsage.used}MB / ${performanceData.memoryUsage.total}MB`}
                      status={performanceData.memoryUsage.used / performanceData.memoryUsage.total > 0.8 ? 'exception' : 'success'}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="Network Requests"
                      value={performanceData.networkRequests.total}
                      prefix={<LineChartOutlined />}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="Cached"
                      value={performanceData.networkRequests.cached}
                      prefix={<CheckCircleOutlined />}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="Optimization" key="optimization">
          <div style={{ marginBottom: 16 }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Title level={5}>
                  <BulbOutlined style={{ marginRight: 8 }} />
                  Performance Suggestions
                </Title>
              </Col>
              <Col>
                <Select
                  value={selectedMetric}
                  onChange={setSelectedMetric}
                  style={{ width: 120 }}
                >
                  <Option value="all">All</Option>
                  <Option value="bundle">Bundle</Option>
                  <Option value="rendering">Rendering</Option>
                  <Option value="assets">Assets</Option>
                </Select>
              </Col>
            </Row>
          </div>

          {optimizationSuggestions
            .filter(suggestion => selectedMetric === 'all' || suggestion.category === selectedMetric)
            .map(suggestion => (
              <OptimizationCard
                key={suggestion.id}
                size="small"
                severity={suggestion.severity}
                title={
                  <Space>
                    <span>{suggestion.title}</span>
                    <Tag color={
                      suggestion.severity === 'high' ? 'red' :
                        suggestion.severity === 'medium' ? 'orange' : 'green'
                    }>
                      {suggestion.severity.toUpperCase()}
                    </Tag>
                    <Tag color="blue">{suggestion.impact.toUpperCase()} IMPACT</Tag>
                  </Space>
                }
                extra={
                  <Space>
                    <Button
                      size="small"
                      type="primary"
                      onClick={() => handleApplyOptimization(suggestion)}
                    >
                      Apply
                    </Button>
                    <Button
                      size="small"
                      onClick={() => handleDismissSuggestion(suggestion.id)}
                    >
                      Dismiss
                    </Button>
                  </Space>
                }
              >
                <Paragraph style={{ margin: 0 }}>
                  {suggestion.description}
                </Paragraph>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">Effort: </Text>
                  <Tag color={
                    suggestion.effort === 'high' ? 'red' :
                      suggestion.effort === 'medium' ? 'orange' : 'green'
                  }>
                    {suggestion.effort.toUpperCase()}
                  </Tag>
                </div>
              </OptimizationCard>
            ))}

          {optimizationSuggestions.filter(suggestion => selectedMetric === 'all' || suggestion.category === selectedMetric).length === 0 && (
            <Alert
              message="No optimization suggestions"
              description="Your application is performing well! No immediate optimizations needed."
              type="success"
              showIcon
            />
          )}
        </TabPane>

        <TabPane tab="Charts" key="charts">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="Performance Trends" size="small">
                <PerformanceChart>
                  <div style={{ textAlign: 'center' }}>
                    <LineChartOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                    <div>
                      <Text type="secondary">Performance charts will be displayed here</Text>
                      <br />
                      <Text type="secondary">Showing trends for Core Web Vitals and bundle size</Text>
                    </div>
                  </div>
                </PerformanceChart>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* Details Modal */}
      <Modal
        title="Performance Details"
        open={showDetails}
        onCancel={() => setShowDetails(false)}
        footer={[
          <Button key="close" onClick={() => setShowDetails(false)}>
            Close
          </Button>,
          <Button key="export" type="primary" icon={<DownloadOutlined />}>
            Export Report
          </Button>
        ]}
        width={800}
      >
        <Tabs defaultActiveKey="metrics">
          <TabPane tab="Detailed Metrics" key="metrics">
            <Table
              dataSource={webVitalsData}
              columns={webVitalsColumns}
              pagination={false}
              size="small"
            />
          </TabPane>
          <TabPane tab="Component Analysis" key="components">
            <List
              dataSource={components}
              renderItem={component => (
                <List.Item>
                  <List.Item.Meta
                    title={component.name || component.type}
                    description={`Render time: ${Math.random() * 20 + 5}ms`}
                  />
                  <Tag color="blue">Active</Tag>
                </List.Item>
              )}
            />
          </TabPane>
        </Tabs>
      </Modal>
    </PerformanceContainer>
  );
};

export default PerformanceTools;
