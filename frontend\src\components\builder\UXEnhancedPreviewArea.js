/**
 * UX Enhanced Preview Area
 * 
 * A comprehensive preview area with enhanced UI/UX features:
 * - Responsive breakpoint indicators and device frames
 * - Advanced zoom controls and canvas interactions
 * - Enhanced drag-and-drop visual feedback
 * - Real-time collaboration indicators
 * - Performance monitoring and optimization
 * - Accessibility improvements
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import {
  Typography,
  Button,
  Card,
  Space,
  Slider,
  Switch,
  Select,
  Badge,
  Tooltip,
  Dropdown,
  Menu,
  Divider,
  Alert,
  Progress,
  Spin,
  Empty,
  Input,
  Form,
  Table
} from 'antd';
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  BorderOutlined,
  DragOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined,
  WifiOutlined,
  DisconnectOutlined,
  SyncOutlined,
  ExpandOutlined,
  CompressOutlined,
  EditOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { theme, a11yUtils, animationUtils, responsiveUtils } from '../../design-system';
import useRealTimePreview from '../../hooks/useRealTimePreview';
import usePreviewPerformance from '../../hooks/usePreviewPerformance';

const { Title, Text } = Typography;
const { Option } = Select;

// Device configurations with enhanced metadata
const DEVICE_CONFIGS = {
  mobile: {
    name: 'Mobile',
    icon: <MobileOutlined />,
    width: 375,
    height: 812,
    scale: 0.8,
    frame: true,
    breakpoint: 'sm',
    description: 'iPhone 12 Pro (375×812)',
    userAgent: 'mobile'
  },
  tablet: {
    name: 'Tablet',
    icon: <TabletOutlined />,
    width: 768,
    height: 1024,
    scale: 0.7,
    frame: true,
    breakpoint: 'md',
    description: 'iPad (768×1024)',
    userAgent: 'tablet'
  },
  desktop: {
    name: 'Desktop',
    icon: <DesktopOutlined />,
    width: 1440,
    height: 900,
    scale: 1,
    frame: false,
    breakpoint: 'lg',
    description: 'Desktop (1440×900)',
    userAgent: 'desktop'
  },
  wide: {
    name: 'Wide Screen',
    icon: <ExpandOutlined />,
    width: 1920,
    height: 1080,
    scale: 0.8,
    frame: false,
    breakpoint: 'xl',
    description: 'Wide Screen (1920×1080)',
    userAgent: 'desktop'
  }
};

// Enhanced styled components
const PreviewContainer = styled.div`
  position: relative;
  height: 100%;
  background: ${theme.colors.background.secondary};
  border-radius: ${theme.borderRadius.lg};
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid ${theme.colors.border.light};
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    border: 2px solid ${theme.colors.border.dark};
  }
`;

const PreviewToolbar = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${theme.spacing[2]} ${theme.spacing[4]};
  background: ${theme.colors.background.paper};
  border-bottom: 1px solid ${theme.colors.border.light};
  box-shadow: ${theme.shadows.sm};
  z-index: ${theme.zIndex.sticky};
  flex-wrap: wrap;
  gap: ${theme.spacing[2]};
  min-height: 60px;

  ${theme.mediaQueries.maxMd} {
    padding: ${theme.spacing[2]} ${theme.spacing[3]};
    gap: ${theme.spacing[1]};
  }
`;

const ToolbarSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  
  ${theme.mediaQueries.maxMd} {
    gap: ${theme.spacing[1]};
  }
`;

const DeviceSelector = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[1]};
  padding: ${theme.spacing[1]};
  background: ${theme.colors.background.tertiary};
  border-radius: ${theme.borderRadius.md};
  border: 1px solid ${theme.colors.border.light};
`;

const DeviceButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[1]};
  border: none;
  background: ${props => props.active ? theme.colors.primary.main : 'transparent'};
  color: ${props => props.active ? theme.colors.primary.contrastText : theme.colors.text.secondary};
  box-shadow: none;
  border-radius: ${theme.borderRadius.sm};
  transition: ${theme.transitions.default};
  min-width: auto;
  padding: ${theme.spacing[1]} ${theme.spacing[2]};

  &:hover {
    background: ${props => props.active ? theme.colors.primary.dark : theme.colors.interactive.hover};
    color: ${props => props.active ? theme.colors.primary.contrastText : theme.colors.primary.main};
  }
  
  &:focus {
    ${a11yUtils.focusRing()};
  }
  
  ${theme.mediaQueries.maxMd} {
    padding: ${theme.spacing[1]};
    
    .device-label {
      display: none;
    }
  }
`;

const ZoomControls = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[2]};
  padding: ${theme.spacing[1]} ${theme.spacing[2]};
  background: ${theme.colors.background.tertiary};
  border-radius: ${theme.borderRadius.md};
  border: 1px solid ${theme.colors.border.light};
`;

const ZoomSlider = styled(Slider)`
  width: 100px;
  margin: 0 ${theme.spacing[2]};
  
  .ant-slider-rail {
    background: ${theme.colors.border.medium};
  }
  
  .ant-slider-track {
    background: ${theme.colors.primary.main};
  }
  
  .ant-slider-handle {
    border-color: ${theme.colors.primary.main};
    
    &:focus {
      box-shadow: ${theme.shadows.focus};
    }
  }
  
  ${theme.mediaQueries.maxMd} {
    width: 60px;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing[1]};
  padding: ${theme.spacing[1]} ${theme.spacing[2]};
  background: ${props => props.connected ? theme.colors.success.light : theme.colors.error.light};
  color: ${props => props.connected ? theme.colors.success.dark : theme.colors.error.dark};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.typography.fontSize.xs};
  font-weight: ${theme.typography.fontWeight.medium};
  border: 1px solid ${props => props.connected ? theme.colors.success.main : theme.colors.error.main};
`;

const CanvasContainer = styled.div`
  flex: 1;
  position: relative;
  overflow: auto;
  background: ${props => props.showGrid ?
    `radial-gradient(circle, ${theme.colors.border.light} 1px, transparent 1px)` :
    theme.colors.background.secondary
  };
  background-size: ${props => props.gridSize || 20}px ${props => props.gridSize || 20}px;
  background-position: ${props => props.gridOffset?.x || 0}px ${props => props.gridOffset?.y || 0}px;
  
  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: ${theme.colors.background.tertiary};
  }
  
  &::-webkit-scrollbar-thumb {
    background: ${theme.colors.border.medium};
    border-radius: ${theme.borderRadius.full};
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: ${theme.colors.border.dark};
  }
`;

const DeviceFrame = styled.div`
  position: relative;
  margin: ${theme.spacing[4]} auto;
  background: ${props => {
    switch (props.deviceType) {
      case 'mobile': return theme.colors.neutral[800];
      case 'tablet': return theme.colors.neutral[700];
      default: return 'transparent';
    }
  }};
  border-radius: ${props => {
    switch (props.deviceType) {
      case 'mobile': return '30px';
      case 'tablet': return '20px';
      default: return '0';
    }
  }};
  padding: ${props => {
    switch (props.deviceType) {
      case 'mobile': return `${theme.spacing[6]} ${theme.spacing[3]}`;
      case 'tablet': return `${theme.spacing[4]} ${theme.spacing[2]}`;
      default: return '0';
    }
  }};
  box-shadow: ${props => props.frame ? theme.shadows.xl : 'none'};
  transition: ${theme.transitions.default};
  
  /* Mobile device frame details */
  ${props => props.deviceType === 'mobile' && `
    &::before {
      content: '';
      position: absolute;
      top: ${theme.spacing[2]};
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: ${theme.colors.neutral[600]};
      border-radius: ${theme.borderRadius.full};
    }

    &::after {
      content: '';
      position: absolute;
      bottom: ${theme.spacing[2]};
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 40px;
      border: 2px solid ${theme.colors.neutral[600]};
      border-radius: 50%;
    }
  `}

  /* Tablet device frame details */
  ${props => props.deviceType === 'tablet' && `
    &::before {
      content: '';
      position: absolute;
      bottom: ${theme.spacing[1]};
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 30px;
      border: 2px solid ${theme.colors.neutral[600]};
      border-radius: 50%;
    }
  `}
`;

const ResponsiveCanvas = styled.div`
  width: ${props => props.deviceWidth}px;
  height: ${props => props.deviceHeight}px;
  max-width: 100%;
  max-height: 100%;
  background: ${theme.colors.background.paper};
  border-radius: ${props => {
    switch (props.deviceType) {
      case 'mobile': return theme.borderRadius.lg;
      case 'tablet': return theme.borderRadius.md;
      default: return '0';
    }
  }};
  overflow: auto;
  position: relative;
  transform: scale(${props => props.scale});
  transform-origin: top center;
  transition: ${theme.transitions.default};
  box-shadow: ${props => props.deviceType !== 'desktop' ? theme.shadows.inner : 'none'};
  
  /* Responsive scaling */
  ${theme.mediaQueries.maxXl} {
    transform: scale(${props => Math.min(props.scale, 0.9)});
  }
  
  ${theme.mediaQueries.maxLg} {
    transform: scale(${props => Math.min(props.scale, 0.8)});
  }
  
  ${theme.mediaQueries.maxMd} {
    transform: scale(${props => Math.min(props.scale, 0.7)});
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    transition: none;
  }
`;

const BreakpointIndicator = styled.div`
  position: absolute;
  top: ${theme.spacing[2]};
  left: ${theme.spacing[2]};
  background: ${theme.colors.primary.main};
  color: ${theme.colors.primary.contrastText};
  padding: ${theme.spacing[1]} ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.sm};
  font-size: ${theme.typography.fontSize.xs};
  font-weight: ${theme.typography.fontWeight.medium};
  z-index: ${theme.zIndex.popover};
  box-shadow: ${theme.shadows.sm};
`;

const PerformanceIndicator = styled.div`
  position: absolute;
  top: ${theme.spacing[2]};
  right: ${theme.spacing[2]};
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.md};
  font-size: ${theme.typography.fontSize.xs};
  z-index: ${theme.zIndex.popover};
  min-width: 120px;
  
  .metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: ${theme.spacing[1]};
    
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const DropZone = styled.div.withConfig({
  shouldForwardProp: (prop) => !['visible', 'isActive'].includes(prop),
})`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed ${theme.colors.border.medium};
  border-radius: ${theme.borderRadius.lg};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  opacity: ${props => props.visible ? 1 : 0};
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  transition: ${theme.transitions.default};
  z-index: ${theme.zIndex.overlay};

  ${props => props.isActive && `
    border-color: ${theme.colors.success.main};
    background: ${theme.colors.success.light};

    .drop-message {
      color: ${theme.colors.success.dark};
    }
  `}
  
  .drop-message {
    color: ${theme.colors.text.secondary};
    font-weight: ${theme.typography.fontWeight.medium};
    font-size: ${theme.typography.fontSize.lg};
    margin-top: ${theme.spacing[2]};
  }
  
  .drop-hint {
    color: ${theme.colors.text.tertiary};
    font-size: ${theme.typography.fontSize.sm};
    margin-top: ${theme.spacing[1]};
  }
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: ${theme.zIndex.modal};
  
  .loading-text {
    margin-top: ${theme.spacing[2]};
    color: ${theme.colors.text.secondary};
    font-weight: ${theme.typography.fontWeight.medium};
  }
`;

// Component renderer function - moved to inside the main component

// Enhanced component wrapper with clear visual indicators
const ComponentWrapper = styled.div.withConfig({
  shouldForwardProp: (prop) => !['previewMode', 'isSelected', 'isHovered', 'isDragOver'].includes(prop),
})`
    position: relative;
    margin: 4px 0;
    border: ${props => {
    if (props.previewMode) return '1px solid transparent';
    if (props.isSelected) return `2px solid ${theme.colors.primary.main}`;
    return '1px dashed rgba(0, 0, 0, 0.1)';
  }};
    border-radius: ${theme.borderRadius.sm};
    background: ${props => {
    if (props.previewMode) return 'transparent';
    if (props.isSelected) return `${theme.colors.primary.main}08`;
    return 'rgba(255, 255, 255, 0.8)';
  }};
    transition: all 0.2s ease;
    cursor: ${props => props.previewMode ? 'default' : 'pointer'};
    min-height: ${props => props.previewMode ? 'auto' : '32px'};
    padding: ${props => props.previewMode ? '0' : '8px'};

    &:hover {
      ${props => !props.previewMode && `
        border-color: ${theme.colors.primary.main};
        background: ${theme.colors.primary.main}12;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        transform: translateY(-1px);
      `}
    }

    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border: 2px solid transparent;
      border-radius: ${theme.borderRadius.sm};
      pointer-events: none;
      transition: border-color 0.2s ease;
      ${props => props.isSelected && `border-color: ${theme.colors.primary.main};`}
    }
  `;

// Component type badge for identification
const ComponentBadge = styled.div.withConfig({
  shouldForwardProp: (prop) => !['isSelected', 'previewMode'].includes(prop),
})`
    position: absolute;
    top: -8px;
    left: 8px;
    background: ${props => props.isSelected ? theme.colors.primary.main : '#666'};
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
    opacity: ${props => props.previewMode ? 0 : 1};
    transform: ${props => props.previewMode ? 'scale(0.8)' : 'scale(1)'};
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  `;

// Selection handles for resize/move operations
const SelectionHandles = styled.div`
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    pointer-events: none;
    opacity: ${props => props.visible ? 1 : 0};
    transition: opacity 0.2s ease;

    &::before, &::after {
      content: '';
      position: absolute;
      width: 8px;
      height: 8px;
      background: ${theme.colors.primary.main};
      border: 2px solid white;
      border-radius: 50%;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    &::before {
      top: -4px;
      left: -4px;
    }

    &::after {
      bottom: -4px;
      right: -4px;
    }
  `;

// Edit indicator for text components
const EditIndicator = styled.div`
    position: absolute;
    top: 4px;
    right: 4px;
    width: 16px;
    height: 16px;
    background: ${theme.colors.primary.main};
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: ${props => props.visible ? 1 : 0};
    transition: all 0.2s ease;
    cursor: pointer;
    z-index: 10;

    &:hover {
      transform: scale(1.1);
      background: ${theme.colors.primary.dark};
    }

    svg {
      width: 10px;
      height: 10px;
      color: white;
    }
  `;

// Component rendering logic moved inside main component

export default function UXEnhancedPreviewArea({
  components = [],
  onSelectComponent,
  onDeleteComponent,
  onUpdateComponent,
  onMoveComponent,
  previewMode = false,
  selectedComponentId,
  onDrop,
  onDragOver,
  onDragLeave,
  realTimeUpdates = true,
  websocketConnected = false,
  showPerformanceMetrics = false,
  enableDeviceFrames = true,
  loading = false
}) {
  // State management
  const [deviceType, setDeviceType] = useState('desktop');
  const [zoom, setZoom] = useState(1);
  const [showGrid, setShowGrid] = useState(false);
  const [gridSize, setGridSize] = useState(20);
  const [showBreakpoints, setShowBreakpoints] = useState(true);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [hoveredComponent, setHoveredComponent] = useState(null);

  // Refs
  const canvasRef = useRef(null);
  const containerRef = useRef(null);

  // Enhanced component rendering function with visual indicators
  const renderComponent = useCallback((component, currentDeviceType, currentPreviewMode) => {
    if (!component) return null;

    const isSelected = selectedComponentId === component.id;
    const isHovered = hoveredComponent === component.id;
    const showIndicators = !currentPreviewMode && (isSelected || isHovered);

    // Responsive styles based on device type
    const getResponsiveStyles = () => {
      const baseStyles = {
        fontSize: currentDeviceType === 'mobile' ? '14px' : currentDeviceType === 'tablet' ? '16px' : '16px',
        padding: currentDeviceType === 'mobile' ? '8px' : '12px',
        margin: currentDeviceType === 'mobile' ? '4px 0' : '8px 0'
      };
      return { ...baseStyles, ...component.style };
    };

    const responsiveStyles = getResponsiveStyles();

    // Component content renderer
    const renderComponentContent = () => {
      switch (component.type) {
        case 'text':
          return (
            <Text
              style={{
                ...responsiveStyles,
                minHeight: currentPreviewMode ? 'auto' : '20px',
                display: 'block',
                padding: currentPreviewMode ? '0' : '4px',
                background: currentPreviewMode ? 'transparent' : 'rgba(255, 255, 255, 0.9)',
                borderRadius: '2px'
              }}
            >
              {component.props?.content || 'Click to edit text...'}
            </Text>
          );
        case 'button':
          return (
            <Button
              type={component.props?.type || 'default'}
              size={currentDeviceType === 'mobile' ? 'small' : 'middle'}
              style={{
                fontSize: responsiveStyles.fontSize,
                minWidth: currentPreviewMode ? 'auto' : '80px'
              }}
            >
              {component.props?.text || 'Button'}
            </Button>
          );
        case 'header':
          return (
            <Title
              level={component.props?.level || (currentDeviceType === 'mobile' ? 4 : 2)}
              style={responsiveStyles}
            >
              {component.props?.text || 'Header'}
            </Title>
          );
        case 'card':
          return (
            <Card
              title={component.props?.title || 'Card Title'}
              size={currentDeviceType === 'mobile' ? 'small' : 'default'}
              style={{ fontSize: responsiveStyles.fontSize }}
            >
              {component.props?.content || 'Card content'}
            </Card>
          );
        case 'image':
          return (
            <img
              src={component.props?.src || 'https://via.placeholder.com/150'}
              alt={component.props?.alt || 'Image'}
              style={{
                maxWidth: '100%',
                height: 'auto',
                borderRadius: currentDeviceType === 'mobile' ? '4px' : '6px'
              }}
            />
          );
        case 'divider':
          return (
            <Divider style={responsiveStyles}>{component.props?.text}</Divider>
          );
        case 'input':
          return (
            <Input
              placeholder={component.props?.placeholder || 'Enter text'}
              disabled={currentPreviewMode ? false : true}
              size={currentDeviceType === 'mobile' ? 'small' : 'middle'}
              style={responsiveStyles}
            />
          );
        case 'form':
          return (
            <Form layout="vertical" size={currentDeviceType === 'mobile' ? 'small' : 'middle'}>
              <Form.Item label="Sample Field">
                <Input
                  placeholder="Sample input"
                  disabled={!currentPreviewMode}
                  style={responsiveStyles}
                />
              </Form.Item>
            </Form>
          );
        case 'table':
          const columns = [
            { title: 'Name', dataIndex: 'name', key: 'name' },
            { title: 'Age', dataIndex: 'age', key: 'age' },
          ];
          const data = [
            { key: '1', name: 'John', age: 32 },
            { key: '2', name: 'Jane', age: 28 },
          ];
          return (
            <Table
              columns={columns}
              dataSource={data}
              size={currentDeviceType === 'mobile' ? 'small' : 'middle'}
              scroll={currentDeviceType === 'mobile' ? { x: true } : undefined}
            />
          );
        default:
          return (
            <div style={{
              padding: '8px',
              textAlign: 'center',
              color: '#666',
              border: '1px dashed #ccc',
              borderRadius: currentDeviceType === 'mobile' ? '4px' : '6px'
            }}>
              {component.type} Component
            </div>
          );
      }
    };

    return (
      <div
        key={component.id}
        onMouseEnter={() => setHoveredComponent(component.id)}
        onMouseLeave={() => setHoveredComponent(null)}
      >
        <ComponentWrapper
          isSelected={isSelected}
          previewMode={currentPreviewMode}
          style={responsiveStyles}
          onClick={(e) => {
            e.stopPropagation();
            if (!currentPreviewMode && onSelectComponent) {
              onSelectComponent(component);
            }
          }}
        >
          {/* Component type badge */}
          <ComponentBadge
            isSelected={isSelected}
            previewMode={currentPreviewMode}
          >
            {component.type}
          </ComponentBadge>

          {/* Selection handles */}
          <SelectionHandles visible={showIndicators} />

          {/* Edit indicator for text components */}
          {component.type === 'text' && (
            <EditIndicator visible={showIndicators}>
              <EditOutlined />
            </EditIndicator>
          )}

          {/* Component content */}
          {renderComponentContent()}
        </ComponentWrapper>
      </div>
    );
  }, [selectedComponentId, hoveredComponent, onSelectComponent]);

  // Debug logging
  useEffect(() => {
    console.log('UXEnhancedPreviewArea - Components:', components);
    console.log('UXEnhancedPreviewArea - Components length:', components.length);
    console.log('UXEnhancedPreviewArea - Device type:', deviceType);
    console.log('UXEnhancedPreviewArea - Preview mode:', previewMode);
    console.log('UXEnhancedPreviewArea - Component rendering fixed!');
  }, [components, deviceType, previewMode]);

  // Test component addition
  useEffect(() => {
    if (components.length === 0) {
      console.log('No components found. You can test by clicking on components in the palette.');

      // Add a test component for debugging (only in development)
      if (process.env.NODE_ENV === 'development' && onDrop) {
        console.log('Adding test component for debugging...');
        setTimeout(() => {
          // Simulate adding a test text component
          const testComponent = {
            id: 'test-component-' + Date.now(),
            type: 'text',
            props: {
              content: 'Test Component - Click components in the palette to add more!'
            },
            position: { x: 50, y: 50 }
          };
          console.log('Test component created:', testComponent);
        }, 2000);
      }
    }
  }, [components.length, onDrop]);

  // Get current device configuration
  const currentDevice = DEVICE_CONFIGS[deviceType];

  // Memoized device-specific styles
  const deviceStyles = useMemo(() => ({
    width: currentDevice.width,
    height: currentDevice.height,
    scale: zoom
  }), [currentDevice, zoom]);

  // Real-time preview hook (with fallback)
  const realTimePreviewHook = useMemo(() => {
    try {
      return useRealTimePreview({
        components,
        onUpdateComponent,
        onDeleteComponent,
        enableWebSocket: realTimeUpdates && websocketConnected
      });
    } catch (error) {
      console.warn('Real-time preview hook failed, using fallback:', error);
      return {
        isUpdating: false,
        websocketConnected: false,
        updateComponent: onUpdateComponent || (() => { }),
        getAllComponents: () => components
      };
    }
  }, [components, onUpdateComponent, onDeleteComponent, realTimeUpdates, websocketConnected]);

  const {
    isUpdating,
    websocketConnected: realtimeConnected,
    updateComponent,
    getAllComponents
  } = realTimePreviewHook;

  // Performance monitoring hook (with fallback)
  const performanceHook = useMemo(() => {
    try {
      return usePreviewPerformance({
        components: getAllComponents(),
        enablePerformanceMonitoring: showPerformanceMetrics
      });
    } catch (error) {
      console.warn('Performance monitoring hook failed, using fallback:', error);
      return {
        renderTime: 0,
        frameRate: 60,
        memoryUsage: 0,
        componentCount: components.length
      };
    }
  }, [getAllComponents, showPerformanceMetrics, components.length]);

  const {
    renderTime,
    frameRate,
    memoryUsage,
    componentCount
  } = performanceHook;

  // Device change handler
  const handleDeviceChange = useCallback((newDeviceType) => {
    setDeviceType(newDeviceType);
    const device = DEVICE_CONFIGS[newDeviceType];
    setZoom(device.scale);
  }, []);

  // Zoom controls
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.1, 0.3));
  }, []);

  const handleZoomReset = useCallback(() => {
    setZoom(currentDevice.scale);
  }, [currentDevice.scale]);

  // Fullscreen toggle
  const handleFullscreenToggle = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Drag and drop handlers
  const handleDragEnter = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragOverInternal = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    if (onDragOver) onDragOver(e);
  }, [onDragOver]);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      if (onDragLeave) onDragLeave(e);
    }
  }, [onDragLeave]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);

    if (onDrop) {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        const x = (e.clientX - rect.left) / zoom;
        const y = (e.clientY - rect.top) / zoom;
        onDrop(e, { x, y });
      }
    }
  }, [onDrop, zoom]);

  // Settings menu
  const settingsMenu = (
    <Menu>
      <Menu.Item key="grid">
        <Space>
          <Switch
            size="small"
            checked={showGrid}
            onChange={setShowGrid}
          />
          <span>Show Grid</span>
        </Space>
      </Menu.Item>
      <Menu.Item key="breakpoints">
        <Space>
          <Switch
            size="small"
            checked={showBreakpoints}
            onChange={setShowBreakpoints}
          />
          <span>Show Breakpoints</span>
        </Space>
      </Menu.Item>
      <Menu.Item key="performance">
        <Space>
          <Switch
            size="small"
            checked={showPerformanceMetrics}
            onChange={() => { }}
          />
          <span>Performance Metrics</span>
        </Space>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="gridSize">
        <div>
          <Text style={{ fontSize: theme.typography.fontSize.xs }}>Grid Size: {gridSize}px</Text>
          <Slider
            min={10}
            max={50}
            value={gridSize}
            onChange={setGridSize}
            style={{ width: 100, margin: `${theme.spacing[1]} 0` }}
          />
        </div>
      </Menu.Item>
    </Menu>
  );

  return (
    <PreviewContainer ref={containerRef}>
      <PreviewToolbar>
        <ToolbarSection>
          <DeviceSelector>
            {Object.entries(DEVICE_CONFIGS).map(([key, device]) => (
              <DeviceButton
                key={key}
                size="small"
                active={deviceType === key}
                onClick={() => handleDeviceChange(key)}
                icon={device.icon}
                title={device.description}
              >
                <span className="device-label">{device.name}</span>
              </DeviceButton>
            ))}
          </DeviceSelector>

          {showBreakpoints && (
            <BreakpointIndicator>
              {currentDevice.breakpoint.toUpperCase()} • {currentDevice.width}×{currentDevice.height}
            </BreakpointIndicator>
          )}
        </ToolbarSection>

        <ToolbarSection>
          <ZoomControls>
            <Button
              size="small"
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              disabled={zoom <= 0.3}
              title="Zoom Out"
            />
            <ZoomSlider
              min={30}
              max={200}
              value={Math.round(zoom * 100)}
              onChange={(value) => setZoom(value / 100)}
              tooltip={{ formatter: (value) => `${value}%` }}
            />
            <Button
              size="small"
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              disabled={zoom >= 2}
              title="Zoom In"
            />
            <Button
              size="small"
              onClick={handleZoomReset}
              title="Reset Zoom"
            >
              {Math.round(zoom * 100)}%
            </Button>
          </ZoomControls>

          <StatusIndicator connected={realtimeConnected}>
            {realtimeConnected ? <WifiOutlined /> : <DisconnectOutlined />}
            {realtimeConnected ? 'Connected' : 'Offline'}
            {isUpdating && <SyncOutlined spin />}
          </StatusIndicator>

          <Space>
            <Dropdown overlay={settingsMenu} trigger={['click']}>
              <Button size="small" icon={<SettingOutlined />} title="Settings" />
            </Dropdown>

            <Button
              size="small"
              icon={isFullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
              onClick={handleFullscreenToggle}
              title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
            />
          </Space>
        </ToolbarSection>
      </PreviewToolbar>

      <CanvasContainer
        showGrid={showGrid}
        gridSize={gridSize}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOverInternal}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {enableDeviceFrames && currentDevice.frame ? (
          <DeviceFrame deviceType={deviceType} frame={currentDevice.frame}>
            <ResponsiveCanvas
              ref={canvasRef}
              deviceWidth={deviceStyles.width}
              deviceHeight={deviceStyles.height}
              deviceType={deviceType}
              scale={zoom}
              onClick={() => onSelectComponent && onSelectComponent(null)}
            >
              {/* Render components */}
              {components.length > 0 ? (
                components.map((component) => renderComponent(component, deviceType, previewMode))
              ) : (
                <Empty
                  description={
                    <span>
                      No components added yet.
                      <br />
                      {previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'}
                    </span>
                  }
                  style={{
                    margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
                    fontSize: deviceType === 'mobile' ? '14px' : '16px'
                  }}
                />
              )}
            </ResponsiveCanvas>
          </DeviceFrame>
        ) : (
          <ResponsiveCanvas
            ref={canvasRef}
            deviceWidth={deviceStyles.width}
            deviceHeight={deviceStyles.height}
            deviceType={deviceType}
            scale={zoom}
            onClick={() => onSelectComponent && onSelectComponent(null)}
          >
            {/* Render components */}
            {components.length > 0 ? (
              components.map((component) => renderComponent(component, deviceType, previewMode))
            ) : (
              <Empty
                description={
                  <span>
                    No components added yet.
                    <br />
                    {previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'}
                  </span>
                }
                style={{
                  margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
                  fontSize: deviceType === 'mobile' ? '14px' : '16px'
                }}
              />
            )}
          </ResponsiveCanvas>
        )}

        {/* Performance metrics overlay */}
        {showPerformanceMetrics && process.env.NODE_ENV === 'development' && (
          <PerformanceIndicator>
            <div className="metric">
              <span>Render:</span>
              <span>{renderTime.toFixed(1)}ms</span>
            </div>
            <div className="metric">
              <span>FPS:</span>
              <span>{frameRate}</span>
            </div>
            <div className="metric">
              <span>Memory:</span>
              <span>{memoryUsage}MB</span>
            </div>
            <div className="metric">
              <span>Components:</span>
              <span>{componentCount}</span>
            </div>
          </PerformanceIndicator>
        )}

        {/* Drag and drop overlay */}
        <DropZone visible={isDragOver} isActive={isDragOver}>
          <DragOutlined style={{ fontSize: 48, color: theme.colors.text.tertiary }} />
          <div className="drop-message">Drop component here</div>
          <div className="drop-hint">Release to add to canvas</div>
        </DropZone>

        {/* Loading overlay */}
        {loading && (
          <LoadingOverlay>
            <Spin size="large" />
            <div className="loading-text">Loading preview...</div>
          </LoadingOverlay>
        )}
      </CanvasContainer>
    </PreviewContainer>
  );
}
