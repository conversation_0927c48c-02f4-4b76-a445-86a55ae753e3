<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Main Route - App Builder 201</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Main Route - App Builder 201</h1>
        <p>This page helps debug why the main route (/) is not loading properly.</p>

        <div class="test-section">
            <h3>🌐 Network Tests</h3>
            <button onclick="testMainRoute()">Test Main Route (/)</button>
            <button onclick="testQuickCheck()">Test Quick Check</button>
            <button onclick="testScriptLoading()">Test Script Loading</button>
            <div id="network-results"></div>
        </div>

        <div class="test-section">
            <h3>📱 React App Tests</h3>
            <button onclick="testReactGlobals()">Test React Globals</button>
            <button onclick="testReactMount()">Test React Mount</button>
            <button onclick="openMainApp()">Open Main App</button>
            <div id="react-results"></div>
        </div>

        <div class="test-section">
            <h3>🔧 Console Logs</h3>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="captureErrors()">Capture Errors</button>
            <pre id="console-logs"></pre>
        </div>

        <div class="test-section">
            <h3>📊 Current Status</h3>
            <div id="status-info"></div>
        </div>
    </div>

    <script>
        let logs = [];
        let originalConsole = {};

        // Capture console logs
        function setupConsoleCapture() {
            ['log', 'error', 'warn', 'info'].forEach(method => {
                originalConsole[method] = console[method];
                console[method] = function(...args) {
                    logs.push({
                        type: method,
                        message: args.join(' '),
                        timestamp: new Date().toLocaleTimeString()
                    });
                    updateConsoleLogs();
                    originalConsole[method].apply(console, args);
                };
            });
        }

        function updateConsoleLogs() {
            const logsElement = document.getElementById('console-logs');
            logsElement.textContent = logs.slice(-20).map(log => 
                `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}`
            ).join('\n');
        }

        function clearLogs() {
            logs = [];
            updateConsoleLogs();
        }

        function captureErrors() {
            window.addEventListener('error', (e) => {
                logs.push({
                    type: 'error',
                    message: `${e.message} at ${e.filename}:${e.lineno}`,
                    timestamp: new Date().toLocaleTimeString()
                });
                updateConsoleLogs();
            });
        }

        async function testMainRoute() {
            const resultsDiv = document.getElementById('network-results');
            try {
                const response = await fetch('/');
                const text = await response.text();
                const hasScripts = text.includes('main.src_');
                const hasRoot = text.includes('id="root"');
                
                resultsDiv.innerHTML = `
                    <div class="status ${response.ok ? 'success' : 'error'}">
                        Main Route Status: ${response.status} ${response.statusText}
                    </div>
                    <div class="status ${hasScripts ? 'success' : 'error'}">
                        Scripts Found: ${hasScripts ? 'Yes' : 'No'}
                    </div>
                    <div class="status ${hasRoot ? 'success' : 'error'}">
                        Root Element: ${hasRoot ? 'Yes' : 'No'}
                    </div>
                    <details>
                        <summary>Response Preview (first 500 chars)</summary>
                        <pre>${text.substring(0, 500)}...</pre>
                    </details>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">Error: ${error.message}</div>`;
            }
        }

        async function testQuickCheck() {
            const resultsDiv = document.getElementById('network-results');
            try {
                const response = await fetch('/quick-check.html');
                resultsDiv.innerHTML += `
                    <div class="status ${response.ok ? 'success' : 'error'}">
                        Quick Check Status: ${response.status} ${response.statusText}
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML += `<div class="status error">Quick Check Error: ${error.message}</div>`;
            }
        }

        async function testScriptLoading() {
            const resultsDiv = document.getElementById('network-results');
            try {
                // Get main page to find script URLs
                const response = await fetch('/');
                const text = await response.text();
                const scriptMatches = text.match(/src="\/static\/js\/main\.[^"]+"/g);
                
                if (scriptMatches) {
                    for (const match of scriptMatches) {
                        const scriptUrl = match.replace('src="', '').replace('"', '');
                        try {
                            const scriptResponse = await fetch(scriptUrl);
                            resultsDiv.innerHTML += `
                                <div class="status ${scriptResponse.ok ? 'success' : 'error'}">
                                    Script ${scriptUrl}: ${scriptResponse.status}
                                </div>
                            `;
                        } catch (scriptError) {
                            resultsDiv.innerHTML += `
                                <div class="status error">
                                    Script ${scriptUrl}: Error - ${scriptError.message}
                                </div>
                            `;
                        }
                    }
                } else {
                    resultsDiv.innerHTML += `<div class="status error">No main scripts found in HTML</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<div class="status error">Script test error: ${error.message}</div>`;
            }
        }

        function testReactGlobals() {
            const resultsDiv = document.getElementById('react-results');
            const reactAvailable = typeof window.React !== 'undefined';
            const reactDOMAvailable = typeof window.ReactDOM !== 'undefined';
            const appLoaded = window.__APP_LOADED__;
            const appLoading = window.__APP_LOADING__;
            
            resultsDiv.innerHTML = `
                <div class="status ${reactAvailable ? 'success' : 'error'}">
                    React Global: ${reactAvailable ? 'Available' : 'Not Available'}
                </div>
                <div class="status ${reactDOMAvailable ? 'success' : 'error'}">
                    ReactDOM Global: ${reactDOMAvailable ? 'Available' : 'Not Available'}
                </div>
                <div class="status ${appLoaded ? 'success' : 'warning'}">
                    App Loaded: ${appLoaded ? 'Yes' : 'No'}
                </div>
                <div class="status ${appLoading ? 'warning' : 'info'}">
                    App Loading: ${appLoading ? 'Yes' : 'No'}
                </div>
            `;
        }

        function testReactMount() {
            const resultsDiv = document.getElementById('react-results');
            const rootElement = document.getElementById('root');
            
            if (rootElement) {
                const hasLoadingSpinner = rootElement.innerHTML.includes('loading-container');
                const hasReactContent = rootElement.innerHTML.includes('data-reactroot') || 
                                       rootElement.children.length > 1 ||
                                       !hasLoadingSpinner;
                
                resultsDiv.innerHTML += `
                    <div class="status success">Root Element: Found</div>
                    <div class="status ${hasLoadingSpinner ? 'warning' : 'success'}">
                        Loading Spinner: ${hasLoadingSpinner ? 'Still showing' : 'Replaced'}
                    </div>
                    <div class="status ${hasReactContent ? 'success' : 'error'}">
                        React Content: ${hasReactContent ? 'Mounted' : 'Not mounted'}
                    </div>
                    <details>
                        <summary>Root Element Content</summary>
                        <pre>${rootElement.innerHTML.substring(0, 300)}...</pre>
                    </details>
                `;
            } else {
                resultsDiv.innerHTML += `<div class="status error">Root Element: Not found</div>`;
            }
        }

        function openMainApp() {
            window.open('/', '_blank');
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status-info');
            statusDiv.innerHTML = `
                <div class="status info">
                    Current URL: ${window.location.href}
                </div>
                <div class="status info">
                    User Agent: ${navigator.userAgent.substring(0, 100)}...
                </div>
                <div class="status info">
                    Timestamp: ${new Date().toLocaleString()}
                </div>
            `;
        }

        // Initialize
        setupConsoleCapture();
        captureErrors();
        updateStatus();
        
        // Auto-refresh status every 5 seconds
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>
