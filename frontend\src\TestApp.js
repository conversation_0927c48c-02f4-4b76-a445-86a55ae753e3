import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { ConfigProvider, Layout, Menu, Button, Drawer, theme } from 'antd';
import {
  MenuOutlined,
  HomeOutlined,
  DashboardOutlined,
  ApiOutlined,
  SettingOutlined,
  UserOutlined,
  LineChartOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import themeConfig from './theme/themeConfig';

const { Header, Content, Sider } = Layout;

const TestApp = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    return localStorage.getItem('theme') === 'dark';
  });

  // Toggle theme
  const toggleTheme = () => {
    const newTheme = isDarkMode ? 'light' : 'dark';
    setIsDarkMode(!isDarkMode);
    localStorage.setItem('theme', newTheme);
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  // Close mobile menu when route changes
  const handleRouteChange = () => {
    setMobileMenuVisible(false);
  };

  // Menu items
  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: <Link to="/" onClick={handleRouteChange}>Home</Link>
    },
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: <Link to="/dashboard" onClick={handleRouteChange}>Dashboard</Link>
    },
    {
      key: 'websocket',
      icon: <ApiOutlined />,
      label: 'WebSocket',
      children: [
        {
          key: 'websocket-diagnostics',
          label: <Link to="/websocket-diagnostics" onClick={handleRouteChange}>Diagnostics</Link>
        },
        {
          key: 'websocket-testing',
          label: <Link to="/websocket-testing" onClick={handleRouteChange}>Testing</Link>
        }
      ]
    }
  ];

  return (
    <ConfigProvider
      theme={{
        ...themeConfig,
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <Layout style={{ minHeight: '100vh' }}>
          {/* Desktop Sidebar */}
          <Sider
            collapsible
            collapsed={collapsed}
            onCollapse={setCollapsed}
            style={{
              overflow: 'auto',
              height: '100vh',
              position: 'fixed',
              left: 0,
              top: 0,
              bottom: 0,
              zIndex: 1000
            }}
            theme={isDarkMode ? 'dark' : 'light'}
          >
            <div style={{
              height: 64,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '16px'
            }}>
              <h1 style={{
                margin: 0,
                color: isDarkMode ? 'white' : 'inherit',
                fontSize: collapsed ? '20px' : '24px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}>
                {collapsed ? 'AB' : 'App Builder'}
              </h1>
            </div>
            <Menu
              theme={isDarkMode ? 'dark' : 'light'}
              defaultSelectedKeys={['home']}
              mode="inline"
              items={menuItems}
            />
          </Sider>

          <Layout style={{ marginLeft: collapsed ? 80 : 200, transition: 'margin-left 0.3s' }}>
            <Header style={{
              padding: '0 16px',
              background: isDarkMode ? '#141414' : '#fff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'sticky',
              top: 0,
              zIndex: 999,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {/* Mobile menu button */}
                <Button
                  type="text"
                  icon={<MenuOutlined />}
                  onClick={toggleMobileMenu}
                  style={{ marginRight: 16 }}
                />
                <h1 style={{
                  margin: 0,
                  fontSize: '20px'
                }}>
                  App Builder
                </h1>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <Button
                  type="text"
                  icon={isDarkMode ? <LineChartOutlined /> : <BarChartOutlined />}
                  onClick={toggleTheme}
                />
                <Button
                  type="text"
                  icon={<UserOutlined />}
                  style={{ marginLeft: 8 }}
                />
              </div>
            </Header>

            <Content style={{
              margin: '24px 16px',
              minHeight: 'calc(100vh - 64px)',
              borderRadius: '8px',
              overflow: 'hidden',
              background: isDarkMode ? '#141414' : '#fff',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
            }}>
              <div style={{ padding: '24px' }}>
                <h2>Test App</h2>
                <p>This is a simplified version of the App.js component to test if the basic structure works.</p>

                <Routes>
                  <Route path="/" element={<div>Home Page</div>} />
                  <Route path="/dashboard" element={<div>Dashboard Page</div>} />
                  <Route path="/websocket-diagnostics" element={<div>WebSocket Diagnostics Page</div>} />
                  <Route path="/websocket-testing" element={<div>WebSocket Testing Page</div>} />
                </Routes>
              </div>
            </Content>
          </Layout>

          {/* Mobile Menu Drawer */}
          <Drawer
            title="App Builder"
            placement="left"
            onClose={toggleMobileMenu}
            open={mobileMenuVisible}
            width={250}
          >
            <Menu
              theme={isDarkMode ? 'dark' : 'light'}
              defaultSelectedKeys={['home']}
              mode="inline"
              items={menuItems}
            />
          </Drawer>
        </Layout>
      </Router>
    </ConfigProvider>
  );
};

export default TestApp;
