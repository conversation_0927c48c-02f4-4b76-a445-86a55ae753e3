"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4816,8952],{

/***/ 18952:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _hooks_useDataManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7535);
/* harmony import */ var _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(50263);
/* harmony import */ var _redux_minimal_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(34816);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;








var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var DemoContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 20px;\n"])));
var DemoCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 20px;\n"])));
var FormGroup = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  \n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"])));
var ButtonGroup = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"])));
var InfoBox = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n"])));

/**
 * DataManagementDemo component
 * Demonstrates the use of data management utilities
 */
var DataManagementDemo = function DataManagementDemo() {
  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    inputValue = _useState2[0],
    setInputValue = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('temporary'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    cacheType = _useState4[0],
    setCacheType = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    useCache = _useState6[0],
    setUseCache = _useState6[1];

  // Use our custom hook to manage data
  var _useDataManager = (0,_hooks_useDataManager__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)({
      selector: function selector(state) {
        var _state$ui;
        return (_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.currentView;
      },
      action: _redux_minimal_store__WEBPACK_IMPORTED_MODULE_10__.setCurrentView,
      cacheKey: 'current_view',
      cacheType: cacheType,
      useCache: useCache,
      defaultValue: 'components'
    }),
    currentView = _useDataManager.data,
    loading = _useDataManager.loading,
    error = _useDataManager.error,
    updateData = _useDataManager.updateData,
    refreshData = _useDataManager.refreshData,
    clearCache = _useDataManager.clearCache;

  // Handle saving data
  var handleSave = /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
      var _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (inputValue.trim()) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            _context.prev = 1;
            _context.next = 2;
            return updateData(inputValue);
          case 2:
            setInputValue('');
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            console.error('Error saving data:', _t);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3]]);
    }));
    return function handleSave() {
      return _ref.apply(this, arguments);
    };
  }();

  // Handle refreshing data
  var handleRefresh = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2() {
      var _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 1;
            return refreshData();
          case 1:
            _context2.next = 3;
            break;
          case 2:
            _context2.prev = 2;
            _t2 = _context2["catch"](0);
            console.error('Error refreshing data:', _t2);
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 2]]);
    }));
    return function handleRefresh() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle clearing cache
  var handleClearCache = function handleClearCache() {
    clearCache();
  };

  // Handle direct cache operations
  var handleSetDirectCache = function handleSetDirectCache() {
    if (!inputValue.trim()) return;
    _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__/* ["default"].setCache */ .Ay.setCache('direct_cache_demo', inputValue, {
      type: cacheType,
      expiresIn: 3600000 // 1 hour
    });
    setInputValue('');
  };
  var handleGetDirectCache = function handleGetDirectCache() {
    var cachedValue = _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__/* ["default"].getCache */ .Ay.getCache('direct_cache_demo', cacheType);
    setInputValue(cachedValue || '');
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DemoContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 3
  }, "Data Management Demo"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, null, "This demo shows how to use the data management utilities to optimize Redux usage and handle data operations."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DemoCard, {
    title: "Current View Data"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(InfoBox, {
    type: "info",
    message: "Redux + Cache Integration",
    description: "This example shows how to use the useDataManager hook to manage data with Redux and caching.",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, null)
  }), loading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: '10px'
    }
  }, "Loading data...")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    type: "error",
    message: "Error",
    description: error.message || 'An error occurred while managing data',
    style: {
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Current View:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, currentView)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "New View:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    value: inputValue,
    onChange: function onChange(e) {
      return setInputValue(e.target.value);
    },
    placeholder: "Enter a new view name"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Cache Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: cacheType,
    onChange: setCacheType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "temporary"
  }, "Temporary (Memory only)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "persistent"
  }, "Persistent (LocalStorage)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "expiring"
  }, "Expiring (Time-based)"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Use Cache:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: useCache,
    onChange: setUseCache
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ButtonGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SaveOutlined */ .ylI, null),
    onClick: handleSave,
    disabled: !inputValue.trim()
  }, "Save to Redux"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ReloadOutlined */ .KF4, null),
    onClick: handleRefresh
  }, "Refresh Data"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    danger: true,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ClearOutlined */ .ohj, null),
    onClick: handleClearCache
  }, "Clear Cache")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DemoCard, {
    title: "Direct Cache Operations"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(InfoBox, {
    type: "info",
    message: "Direct Cache API",
    description: "This example shows how to use the dataManager utility directly for caching operations.",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Cache Value:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    value: inputValue,
    onChange: function onChange(e) {
      return setInputValue(e.target.value);
    },
    placeholder: "Enter a value to cache"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Cache Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: cacheType,
    onChange: setCacheType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "temporary"
  }, "Temporary (Memory only)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "persistent"
  }, "Persistent (LocalStorage)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "expiring"
  }, "Expiring (Time-based)"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ButtonGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    onClick: handleSetDirectCache,
    disabled: !inputValue.trim()
  }, "Set Cache"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    onClick: handleGetDirectCache
  }, "Get Cache"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    danger: true,
    onClick: function onClick() {
      return _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__/* ["default"].clearCache */ .Ay.clearCache('direct_cache_demo', cacheType);
    }
  }, "Clear Cache"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataManagementDemo);

/***/ }),

/***/ 34816:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addComponent: () => (/* binding */ addComponent),
/* harmony export */   addLayout: () => (/* binding */ addLayout),
/* harmony export */   addTheme: () => (/* binding */ addTheme),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   removeComponent: () => (/* binding */ removeComponent),
/* harmony export */   removeLayout: () => (/* binding */ removeLayout),
/* harmony export */   removeTheme: () => (/* binding */ removeTheme),
/* harmony export */   setActiveTheme: () => (/* binding */ setActiveTheme),
/* harmony export */   setCurrentView: () => (/* binding */ setCurrentView),
/* harmony export */   togglePreviewMode: () => (/* binding */ togglePreviewMode),
/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),
/* harmony export */   updateComponent: () => (/* binding */ updateComponent),
/* harmony export */   updateLayout: () => (/* binding */ updateLayout),
/* harmony export */   updateTheme: () => (/* binding */ updateTheme),
/* harmony export */   websocketConnected: () => (/* binding */ websocketConnected),
/* harmony export */   websocketDisconnected: () => (/* binding */ websocketDisconnected),
/* harmony export */   websocketMessageReceived: () => (/* binding */ websocketMessageReceived)
/* harmony export */ });
/* harmony import */ var _actions_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4318);
/**
 * Compatibility layer for the minimal-store.js
 * This file re-exports actions from the main Redux store to maintain backward compatibility
 *
 * IMPORTANT: This file is designed to avoid circular dependencies by directly defining
 * action creators rather than importing them from other files.
 */

// Import action types directly to avoid circular dependencies


// Define action creators directly to avoid circular dependencies
// Component actions
var addComponent = function addComponent(component) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_COMPONENT */ .oz || 'ADD_COMPONENT',
    payload: component
  };
};
var updateComponent = function updateComponent(componentId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_COMPONENT */ .ei || 'UPDATE_COMPONENT',
    payload: {
      id: componentId,
      props: props
    }
  };
};
var removeComponent = function removeComponent(componentId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_COMPONENT */ .xS || 'REMOVE_COMPONENT',
    payload: {
      id: componentId
    }
  };
};

// Layout actions
var addLayout = function addLayout(layout) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_LAYOUT */ .vs || 'ADD_LAYOUT',
    payload: layout
  };
};
var updateLayout = function updateLayout(layoutId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_LAYOUT */ .Pe || 'UPDATE_LAYOUT',
    payload: {
      id: layoutId,
      props: props
    }
  };
};
var removeLayout = function removeLayout(layoutId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_LAYOUT */ .gV || 'REMOVE_LAYOUT',
    payload: {
      id: layoutId
    }
  };
};

// Theme actions
var addTheme = function addTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_THEME */ .U_ || 'ADD_THEME',
    payload: theme
  };
};
var updateTheme = function updateTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_THEME */ .gk || 'UPDATE_THEME',
    payload: theme
  };
};
var removeTheme = function removeTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_THEME */ .D || 'REMOVE_THEME',
    payload: {
      id: themeId
    }
  };
};
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .SET_ACTIVE_THEME */ .wH || 'SET_ACTIVE_THEME',
    payload: themeId
  };
};

// WebSocket actions
var websocketConnected = function websocketConnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_CONNECTED */ .Kg || 'WEBSOCKET_CONNECTED'
  };
};
var websocketDisconnected = function websocketDisconnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_DISCONNECTED */ .co || 'WEBSOCKET_DISCONNECTED'
  };
};
var websocketMessageReceived = function websocketMessageReceived(message) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WS_MESSAGE_RECEIVED */ .ZH || 'WEBSOCKET_MESSAGE_RECEIVED',
    payload: message
  };
};

// UI actions
var toggleSidebar = function toggleSidebar() {
  return {
    type: 'TOGGLE_SIDEBAR'
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: 'SET_CURRENT_VIEW',
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: 'TOGGLE_PREVIEW_MODE'
  };
};

// Re-export all actions for backward compatibility


// Export a dummy store for backward compatibility
var dummyStore = {
  getState: function getState() {
    return {};
  },
  dispatch: function dispatch() {},
  subscribe: function subscribe() {
    return function () {};
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dummyStore);

/***/ })

}]);