import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import store from './redux/store';
import { BrowserRouter as Router } from 'react-router-dom';
import Routes from './Routes';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './components/theme/ThemeManager';
import EnhancedErrorBoundary from './components/common/EnhancedErrorBoundary';
import { register as registerServiceWorker } from './serviceWorkerRegistration';
import { disableMockWebSocketServer } from './utils/mockWebSocketServer';

// Expose React to global scope for debugging and verification - EARLY EXPOSURE
window.React = React;
window.ReactDOM = { createRoot };

// Immediate verification
console.log('🚀 React loaded successfully:', React.version);
console.log('🔧 React available globally:', typeof window.React);
console.log('🏗️ ReactDOM createRoot available:', typeof createRoot);

// Additional global exposure for testing
if (typeof window !== 'undefined') {
  // Safely set React DevTools hook - check if it's already defined and writable
  try {
    if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = {};
    }
  } catch (error) {
    // React DevTools is already installed and the property is read-only
    console.log('React DevTools already installed, skipping hook setup');
  }

  window.__REACT_VERSION__ = React.version;
  window.__REACT_LOADED__ = true;

  // Ensure globals are accessible
  Object.defineProperty(window, 'React', {
    value: React,
    writable: false,
    configurable: false
  });

  Object.defineProperty(window, 'ReactDOM', {
    value: { createRoot },
    writable: false,
    configurable: false
  });

  console.log('✅ React globals locked and verified');
}

// Simple CSS for basic styling
const styles = `
  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Add styles to document
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

// Get root element
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found');
}

// Create React root
const root = createRoot(rootElement);

// Simple App component that bypasses complex initialization
const SimpleApp = () => {
  return (
    <Provider store={store}>
      <AuthProvider>
        <EnhancedErrorBoundary>
          <ThemeProvider initialTheme="light">
            <Router>
              <Routes />
            </Router>
          </ThemeProvider>
        </EnhancedErrorBoundary>
      </AuthProvider>
    </Provider>
  );
};

// FORCE DISABLE MOCK WEBSOCKET - Multiple aggressive checks
console.log('🔌 FORCING REAL WEBSOCKET MODE - Disabling all mock WebSocket functionality...');

// Check if mock WebSocket is active and disable it
if (window.WebSocket !== window._originalWebSocket && window._originalWebSocket) {
  console.log('🔌 Mock WebSocket detected - restoring real WebSocket...');
  disableMockWebSocketServer();
}

// Additional check - if WebSocket class has mock characteristics, restore native
if (window.WebSocket && window.WebSocket.name === 'MockWebSocket') {
  console.log('🔌 MockWebSocket class detected - forcing native WebSocket...');
  if (window._originalWebSocket) {
    window.WebSocket = window._originalWebSocket;
  }
}

// Set global flags to ensure real API usage
window.USE_REAL_API = true;
window.MOCK_SERVERS_ENABLED = false;
window.FORCE_REAL_WEBSOCKET = true;

// Log current WebSocket class for debugging
console.log('🔌 Current WebSocket class:', window.WebSocket?.name || 'WebSocket');
console.log('🔌 Real API mode:', window.USE_REAL_API);
console.log('🔌 Mock servers disabled:', !window.MOCK_SERVERS_ENABLED);

// Render the app
try {
  console.log('🚀 Starting App Builder 201...');
  console.log('🔌 Using real WebSocket connections to backend');

  // Set loading flag
  window.__APP_LOADING__ = true;

  root.render(
    <React.StrictMode>
      <SimpleApp />
    </React.StrictMode>
  );

  // Mark app as loaded
  window.__APP_LOADED__ = true;
  window.__APP_LOADING__ = false;

  console.log('✅ App Builder 201 loaded successfully!');
  console.log('🔍 Final React global check:', {
    React: typeof window.React,
    ReactDOM: typeof window.ReactDOM,
    version: window.__REACT_VERSION__,
    loaded: window.__REACT_LOADED__
  });

  // Register service worker for offline support and caching
  registerServiceWorker({
    onSuccess: (registration) => {
      console.log('✅ Service Worker registered successfully:', registration);
    },
    onUpdate: (registration) => {
      console.log('🔄 Service Worker updated:', registration);
      // You could show a notification to the user here
    },
    onWaiting: () => {
      console.log('⏳ Service Worker waiting for activation');
    }
  });
} catch (error) {
  console.error('❌ Failed to load App Builder 201:', error);

  // Fallback UI
  root.render(
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      background: '#f8fafc',
      color: '#1f2937',
      textAlign: 'center',
      padding: '2rem'
    }}>
      <div style={{
        background: 'white',
        padding: '2rem',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        maxWidth: '500px'
      }}>
        <h1 style={{ margin: '0 0 1rem', color: '#dc2626' }}>App Loading Error</h1>
        <p style={{ margin: '0 0 1rem' }}>
          There was an error loading the App Builder application.
        </p>
        <button
          onClick={() => window.location.reload()}
          style={{
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          Reload Page
        </button>
      </div>
    </div>
  );
}

