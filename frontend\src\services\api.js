/**
 * API Service for App Builder
 * 
 * Provides centralized API communication for all App Builder features
 */

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api';

// Generic API request handler
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return { data, status: response.status };
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
};

// Template Service
export const templateService = {
  // Get all templates
  getTemplates: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = `/templates/${queryString ? `?${queryString}` : ''}`;
    return apiRequest(endpoint);
  },

  // Get template by ID
  getTemplate: async (id) => {
    return apiRequest(`/templates/${id}/`);
  },

  // Create new template
  createTemplate: async (templateData) => {
    return apiRequest('/templates/', {
      method: 'POST',
      body: JSON.stringify(templateData)
    });
  },

  // Update template
  updateTemplate: async (id, templateData) => {
    return apiRequest(`/templates/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(templateData)
    });
  },

  // Delete template
  deleteTemplate: async (id) => {
    return apiRequest(`/templates/${id}/`, {
      method: 'DELETE'
    });
  },

  // Load template into app
  loadTemplate: async (id) => {
    return apiRequest(`/templates/${id}/load/`, {
      method: 'POST'
    });
  },

  // Save current app as template
  saveTemplate: async (templateData) => {
    return apiRequest('/templates/save/', {
      method: 'POST',
      body: JSON.stringify(templateData)
    });
  }
};

// Export Service
export const exportService = {
  // Export app in specified format
  exportApp: async (components, format = 'react', options = {}) => {
    return apiRequest('/export/', {
      method: 'POST',
      body: JSON.stringify({
        components,
        format,
        options
      })
    });
  },

  // Validate generated code
  validateCode: async (code, format) => {
    return apiRequest('/export/validate/', {
      method: 'POST',
      body: JSON.stringify({ code, format })
    });
  },

  // Get export formats
  getFormats: async () => {
    return apiRequest('/export/formats/');
  }
};

// Component Service
export const componentService = {
  // Get available components
  getComponents: async (category = null) => {
    const endpoint = category ? `/components/?category=${category}` : '/components/';
    return apiRequest(endpoint);
  },

  // Get component by ID
  getComponent: async (id) => {
    return apiRequest(`/components/${id}/`);
  },

  // Save custom component
  saveComponent: async (componentData) => {
    return apiRequest('/components/', {
      method: 'POST',
      body: JSON.stringify(componentData)
    });
  },

  // Update component
  updateComponent: async (id, componentData) => {
    return apiRequest(`/components/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(componentData)
    });
  },

  // Delete component
  deleteComponent: async (id) => {
    return apiRequest(`/components/${id}/`, {
      method: 'DELETE'
    });
  }
};

// Layout Service
export const layoutService = {
  // Get layout templates
  getLayouts: async () => {
    return apiRequest('/layouts/');
  },

  // Save layout
  saveLayout: async (layoutData) => {
    return apiRequest('/layouts/', {
      method: 'POST',
      body: JSON.stringify(layoutData)
    });
  },

  // Apply layout to components
  applyLayout: async (layoutId, components) => {
    return apiRequest(`/layouts/${layoutId}/apply/`, {
      method: 'POST',
      body: JSON.stringify({ components })
    });
  }
};

// Theme Service
export const themeService = {
  // Get available themes
  getThemes: async () => {
    return apiRequest('/themes/');
  },

  // Save custom theme
  saveTheme: async (themeData) => {
    return apiRequest('/themes/', {
      method: 'POST',
      body: JSON.stringify(themeData)
    });
  },

  // Apply theme
  applyTheme: async (themeId, components) => {
    return apiRequest(`/themes/${themeId}/apply/`, {
      method: 'POST',
      body: JSON.stringify({ components })
    });
  }
};

// AI Service
export const aiService = {
  // Get AI suggestions
  getSuggestions: async (context) => {
    return apiRequest('/ai/suggestions/', {
      method: 'POST',
      body: JSON.stringify(context)
    });
  },

  // Analyze app structure
  analyzeApp: async (components) => {
    return apiRequest('/ai/analyze/', {
      method: 'POST',
      body: JSON.stringify({ components })
    });
  },

  // Get layout suggestions
  getLayoutSuggestions: async (components) => {
    return apiRequest('/ai/layout-suggestions/', {
      method: 'POST',
      body: JSON.stringify({ components })
    });
  }
};

// Collaboration Service
export const collaborationService = {
  // Get collaborators
  getCollaborators: async (projectId) => {
    return apiRequest(`/collaboration/${projectId}/collaborators/`);
  },

  // Add collaborator
  addCollaborator: async (projectId, collaboratorData) => {
    return apiRequest(`/collaboration/${projectId}/collaborators/`, {
      method: 'POST',
      body: JSON.stringify(collaboratorData)
    });
  },

  // Get comments
  getComments: async (projectId) => {
    return apiRequest(`/collaboration/${projectId}/comments/`);
  },

  // Add comment
  addComment: async (projectId, commentData) => {
    return apiRequest(`/collaboration/${projectId}/comments/`, {
      method: 'POST',
      body: JSON.stringify(commentData)
    });
  }
};

// Project Service
export const projectService = {
  // Get projects
  getProjects: async () => {
    return apiRequest('/projects/');
  },

  // Create project
  createProject: async (projectData) => {
    return apiRequest('/projects/', {
      method: 'POST',
      body: JSON.stringify(projectData)
    });
  },

  // Update project
  updateProject: async (id, projectData) => {
    return apiRequest(`/projects/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(projectData)
    });
  },

  // Delete project
  deleteProject: async (id) => {
    return apiRequest(`/projects/${id}/`, {
      method: 'DELETE'
    });
  },

  // Save project state
  saveProject: async (id, state) => {
    return apiRequest(`/projects/${id}/save/`, {
      method: 'POST',
      body: JSON.stringify(state)
    });
  },

  // Load project state
  loadProject: async (id) => {
    return apiRequest(`/projects/${id}/load/`);
  }
};

// Default export with all services
export default {
  templateService,
  exportService,
  componentService,
  layoutService,
  themeService,
  aiService,
  collaborationService,
  projectService
};
