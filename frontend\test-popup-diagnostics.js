/**
 * Test script to verify popup diagnostics work correctly
 */

const puppeteer = require('puppeteer');

async function testPopupDiagnostics() {
  console.log('🔍 Testing Popup Diagnostics...');
  console.log('================================');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false, // Show browser for visual verification
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Navigate to main app
    console.log('📱 Loading main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for app to load
    await page.waitForFunction(
      () => window.__APP_LOADED__ === true,
      { timeout: 10000 }
    );

    // Check if diagnostic widget exists
    console.log('🔍 Checking for diagnostic widget...');
    const widgetExists = await page.evaluate(() => {
      return !!document.getElementById('react-diagnostic-widget');
    });

    console.log(`Diagnostic Widget Present: ${widgetExists ? '✅' : '❌'}`);

    if (widgetExists) {
      // Test popup diagnostics directly
      console.log('🧪 Testing popup diagnostics directly...');
      
      const popupPage = await browser.newPage();
      await popupPage.goto('http://localhost:3000/popup-diagnostics.html', {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Simulate opener relationship
      await popupPage.evaluate((mainPageUrl) => {
        // Mock the opener to point to main window
        Object.defineProperty(window, 'opener', {
          value: {
            React: window.parent.React || { version: '18.3.1' },
            ReactDOM: window.parent.ReactDOM || { createRoot: function() {} },
            __APP_LOADED__: true,
            __APP_LOADING__: false,
            __REACT_LOADED__: true,
            __REACT_GLOBALS_EXPOSED__: true,
            document: {
              getElementById: (id) => {
                if (id === 'root') {
                  return {
                    innerHTML: '<div>Mock React App Content</div>',
                    tagName: 'DIV'
                  };
                }
                return null;
              },
              querySelectorAll: (selector) => {
                if (selector === 'link[rel="stylesheet"]') {
                  return [
                    { href: 'http://localhost:3000/static/css/main.css' },
                    { href: 'http://localhost:3000/static/css/vendor.css' }
                  ];
                }
                return [];
              }
            }
          },
          configurable: true
        });
      }, page.url());

      // Wait for diagnostics to run
      await popupPage.waitForTimeout(2000);

      // Check diagnostic results
      const diagnosticResults = await popupPage.evaluate(() => {
        const reactStatus = document.getElementById('react-status');
        const appStatus = document.getElementById('app-status');
        const domStatus = document.getElementById('dom-status');
        const cssStatus = document.getElementById('css-status');
        
        return {
          reactStatusText: reactStatus ? reactStatus.textContent : 'Not found',
          appStatusText: appStatus ? appStatus.textContent : 'Not found',
          domStatusText: domStatus ? domStatus.textContent : 'Not found',
          cssStatusText: cssStatus ? cssStatus.textContent : 'Not found',
          title: document.title
        };
      });

      console.log('Diagnostic Results:');
      console.log(`  React Status: ${diagnosticResults.reactStatusText}`);
      console.log(`  App Status: ${diagnosticResults.appStatusText}`);
      console.log(`  DOM Status: ${diagnosticResults.domStatusText}`);
      console.log(`  CSS Status: ${diagnosticResults.cssStatusText}`);
      console.log(`  Page Title: ${diagnosticResults.title}`);

      // Check for success indicators
      const hasSuccessIndicators = 
        diagnosticResults.reactStatusText.includes('✅') ||
        diagnosticResults.appStatusText.includes('✅') ||
        diagnosticResults.domStatusText.includes('✅');

      console.log(`Success Indicators Found: ${hasSuccessIndicators ? '✅' : '❌'}`);

      await popupPage.close();
    }

    // Test widget click functionality
    if (widgetExists) {
      console.log('🖱️ Testing widget click...');
      
      // Listen for new pages (popup)
      let popupOpened = false;
      browser.on('targetcreated', async (target) => {
        if (target.type() === 'page') {
          const newPage = await target.page();
          const url = newPage.url();
          if (url.includes('popup-diagnostics.html')) {
            popupOpened = true;
            console.log('✅ Popup diagnostics window opened');
            await newPage.waitForTimeout(1000);
            await newPage.close();
          }
        }
      });

      // Click the widget
      await page.click('#react-diagnostic-widget');
      
      // Wait to see if popup opens
      await page.waitForTimeout(2000);
      
      console.log(`Popup Opened on Click: ${popupOpened ? '✅' : '❌'}`);
    }

    // Overall assessment
    console.log('\n📊 Test Summary');
    console.log('================');
    
    const tests = [
      widgetExists,
      // Add more test results here as needed
    ];

    const passedTests = tests.filter(test => test).length;
    const totalTests = tests.length;

    console.log(`Tests Passed: ${passedTests}/${totalTests}`);
    console.log(`Overall Status: ${passedTests === totalTests ? '✅ ALL TESTS PASSED' : '⚠️ SOME ISSUES FOUND'}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testPopupDiagnostics().catch(console.error);
