<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template System Demonstration - App Builder Enhanced</title>
    <link href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/reset.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/antd@5.12.8/dist/antd.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .demo-container {
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .demo-header {
            text-align: center;
            margin-bottom: 32px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .demo-header h1 {
            color: #1890ff;
            margin-bottom: 16px;
            font-size: 2.5rem;
            font-weight: 600;
        }

        .demo-header p {
            font-size: 18px;
            color: #666;
            max-width: 800px;
            margin: 0 auto 24px;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin: 24px 0;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 3fr;
            gap: 24px;
            margin-top: 32px;
        }

        .sidebar {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            height: fit-content;
            position: sticky;
            top: 24px;
        }

        .main-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .tab-container {
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 24px;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        .tab-button:hover {
            color: #1890ff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .template-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .template-title {
            font-weight: 600;
            color: #333;
        }

        .template-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-layout {
            background: #f6ffed;
            color: #52c41a;
        }

        .type-app {
            background: #e6f7ff;
            color: #1890ff;
        }

        .type-component {
            background: #f9f0ff;
            color: #722ed1;
        }

        .template-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .template-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #999;
        }

        .json-viewer {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
            overflow-x: auto;
            margin: 16px 0;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin: 24px 0;
        }

        .feature-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature-description {
            color: #666;
            font-size: 14px;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .btn:hover {
            background: #40a9ff;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #d9d9d9;
        }

        .alert {
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
        }

        .alert-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0050b3;
        }

        .hierarchy-tree {
            font-family: monospace;
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
        }

        .tree-node {
            margin: 4px 0;
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .tree-node:hover {
            background: #e9ecef;
        }

        .tree-indent-1 {
            margin-left: 20px;
        }

        .tree-indent-2 {
            margin-left: 40px;
        }

        .tree-indent-3 {
            margin-left: 60px;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="demo-container">
        <!-- Header Section -->
        <div class="demo-header">
            <h1>🗂️ Template System Demonstration</h1>
            <p>
                Comprehensive demonstration of the hierarchical template system featuring LayoutTemplate and AppTemplate
                models,
                their relationships, and integration with the App Builder Enhanced application.
            </p>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-value" id="total-templates">12</div>
                    <div class="stat-label">Total Templates</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏗️</div>
                    <div class="stat-value" id="layout-templates">5</div>
                    <div class="stat-label">Layout Templates</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📱</div>
                    <div class="stat-value" id="app-templates">4</div>
                    <div class="stat-label">App Templates</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🧩</div>
                    <div class="stat-value" id="component-templates">3</div>
                    <div class="stat-label">Component Templates</div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="content-grid">
            <!-- Sidebar -->
            <div class="sidebar">
                <h3>Navigation & Controls</h3>

                <div style="margin-bottom: 20px;">
                    <label><strong>Search Templates:</strong></label>
                    <input type="text" id="search-input" placeholder="Search templates..."
                        style="width: 100%; padding: 8px; margin-top: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                </div>

                <div style="margin-bottom: 20px;">
                    <label><strong>Filter by Type:</strong></label>
                    <select id="type-filter"
                        style="width: 100%; padding: 8px; margin-top: 8px; border: 1px solid #d9d9d9; border-radius: 4px;">
                        <option value="all">All Types</option>
                        <option value="layout">Layout Templates</option>
                        <option value="app">App Templates</option>
                        <option value="component">Component Templates</option>
                    </select>
                </div>

                <div style="margin-bottom: 20px;">
                    <label><strong>Quick Actions:</strong></label>
                    <div style="margin-top: 8px;">
                        <button class="btn" onclick="refreshTemplates()" style="width: 100%; margin-bottom: 8px;">
                            🔄 Refresh Templates
                        </button>
                        <button class="btn btn-secondary" onclick="exportTemplates()"
                            style="width: 100%; margin-bottom: 8px;">
                            📥 Export Templates
                        </button>
                        <button class="btn btn-secondary" onclick="showApiDocs()" style="width: 100%;">
                            📚 API Documentation
                        </button>
                    </div>
                </div>

                <div>
                    <label><strong>View Options:</strong></label>
                    <div style="margin-top: 8px;">
                        <label style="display: block; margin-bottom: 8px;">
                            <input type="radio" name="preview-mode" value="visual" checked> Visual Preview
                        </label>
                        <label style="display: block; margin-bottom: 8px;">
                            <input type="radio" name="preview-mode" value="json"> JSON Structure
                        </label>
                        <label style="display: block;">
                            <input type="checkbox" id="show-hierarchy" checked> Show Hierarchy
                        </label>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Tab Navigation -->
                <div class="tab-container">
                    <button class="tab-button active" onclick="showTab('overview')">📋 System Overview</button>
                    <button class="tab-button" onclick="showTab('gallery')">🎨 Template Gallery</button>
                    <button class="tab-button" onclick="showTab('hierarchy')">🌳 Hierarchy View</button>
                    <button class="tab-button" onclick="showTab('testing')">🧪 Interactive Testing</button>
                    <button class="tab-button" onclick="showTab('integration')">🔗 Integration Demo</button>
                    <button class="tab-button" onclick="showTab('api')">📖 API Documentation</button>
                </div>

                <!-- Tab Contents -->
                <div id="overview-tab" class="tab-content active">
                    <!-- System Overview content will be added here -->
                </div>

                <div id="gallery-tab" class="tab-content">
                    <!-- Template Gallery content will be added here -->
                </div>

                <div id="hierarchy-tab" class="tab-content">
                    <!-- Hierarchy View content will be added here -->
                </div>

                <div id="testing-tab" class="tab-content">
                    <!-- Interactive Testing content will be added here -->
                </div>

                <div id="integration-tab" class="tab-content">
                    <!-- Integration Demo content will be added here -->
                </div>

                <div id="api-tab" class="tab-content">
                    <!-- API Documentation content will be added here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-json.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <script>
        // Sample template data
        const sampleTemplates = {
            layout: [
                {
                    id: 1,
                    name: "[Default] Grid Layout Template",
                    description: "Responsive grid layout with header and sidebar",
                    layout_type: "grid",
                    components: {
                        structure: "grid",
                        areas: ["header", "sidebar", "main", "footer"],
                        columns: "200px 1fr",
                        rows: "auto 1fr auto"
                    },
                    default_props: {
                        gap: "16px",
                        padding: "24px",
                        responsive: true
                    },
                    is_public: true,
                    created_at: "2024-01-01T00:00:00Z"
                },
                {
                    id: 2,
                    name: "Flex Layout Template",
                    description: "Flexible layout with dynamic content areas",
                    layout_type: "flex",
                    components: {
                        structure: "flex",
                        direction: "column",
                        sections: ["header", "main", "footer"]
                    },
                    default_props: {
                        gap: "20px",
                        minHeight: "100vh"
                    },
                    is_public: true,
                    created_at: "2024-01-02T00:00:00Z"
                }
            ],
            app: [
                {
                    id: 1,
                    name: "[Default] Hello World Starter",
                    description: "A simple starter template with basic React components, responsive design, and sample content. Perfect for getting started with the App Builder.",
                    app_category: "other",
                    components: {
                        pages: [{
                            name: "home",
                            title: "Home",
                            components: [{
                                id: "header-1",
                                type: "header",
                                props: {
                                    title: "Hello World!",
                                    subtitle: "Welcome to your new app built with App Builder",
                                    backgroundColor: "#1890ff",
                                    textColor: "#ffffff"
                                }
                            }]
                        }]
                    },
                    required_components: ["header", "container"],
                    preview_image: "/images/hello-world-preview.png",
                    is_public: true,
                    created_at: "2024-01-01T00:00:00Z"
                }
            ],
            component: [
                {
                    id: 1,
                    name: "Button Component Template",
                    description: "Customizable button component with multiple variants",
                    component_type: "button",
                    components: {
                        type: "button",
                        variants: ["primary", "secondary", "danger"],
                        sizes: ["small", "medium", "large"]
                    },
                    default_props: {
                        variant: "primary",
                        size: "medium",
                        disabled: false
                    },
                    is_public: true,
                    created_at: "2024-01-01T00:00:00Z"
                }
            ]
        };

        // Global state
        let currentTemplates = sampleTemplates;
        let selectedTemplate = null;
        let currentTab = 'overview';

        // Initialize the demo
        document.addEventListener('DOMContentLoaded', function () {
            initializeDemo();
        });

        function initializeDemo() {
            updateStats();
            loadOverviewContent();
            loadGalleryContent();
            loadHierarchyContent();
            loadTestingContent();
            loadIntegrationContent();
            loadApiContent();

            // Add event listeners
            document.getElementById('search-input').addEventListener('input', filterTemplates);
            document.getElementById('type-filter').addEventListener('change', filterTemplates);

            // Add preview mode change listeners
            document.querySelectorAll('input[name="preview-mode"]').forEach(radio => {
                radio.addEventListener('change', updatePreviewMode);
            });
        }

        function updateStats() {
            const totalTemplates = Object.values(currentTemplates).reduce((sum, arr) => sum + arr.length, 0);
            document.getElementById('total-templates').textContent = totalTemplates;
            document.getElementById('layout-templates').textContent = currentTemplates.layout.length;
            document.getElementById('app-templates').textContent = currentTemplates.app.length;
            document.getElementById('component-templates').textContent = currentTemplates.component.length;
        }

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');

            currentTab = tabName;
        }

        function loadOverviewContent() {
            const content = `
                <h2>🏗️ Template System Architecture</h2>
                <div class="alert alert-info">
                    <strong>System Overview:</strong> The hierarchical template system provides a comprehensive foundation for building applications with reusable components, layouts, and complete app templates.
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🏗️</div>
                        <div class="feature-title">Layout Templates</div>
                        <div class="feature-description">
                            Define reusable layout structures including grid, flex, sidebar, and navigation layouts.
                            Store component references and configurations in JSONField.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <div class="feature-title">App Templates</div>
                        <div class="feature-description">
                            Complete application templates with full component structures, categorized by business domain.
                            Include required dependencies and preview images.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🧩</div>
                        <div class="feature-title">Component Templates</div>
                        <div class="feature-description">
                            Individual component templates with default properties and configurations.
                            Building blocks for layouts and applications.
                        </div>
                    </div>
                </div>

                <h3>📋 Key Features</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin: 24px 0;">
                    <div>
                        <h4>Template Management</h4>
                        <ul>
                            <li>✅ Hierarchical template organization</li>
                            <li>✅ Public and private template visibility</li>
                            <li>✅ Template categorization and tagging</li>
                            <li>✅ Version control and history tracking</li>
                            <li>✅ Template search and filtering</li>
                            <li>✅ Bulk import/export functionality</li>
                        </ul>
                    </div>
                    <div>
                        <h4>Integration Features</h4>
                        <ul>
                            <li>✅ Component Builder integration</li>
                            <li>✅ Layout Designer compatibility</li>
                            <li>✅ Theme Manager support</li>
                            <li>✅ Real-time collaboration</li>
                            <li>✅ Code export functionality</li>
                            <li>✅ AI-assisted template suggestions</li>
                        </ul>
                    </div>
                </div>

                <h3>🎯 Hello World Template Showcase</h3>
                <div class="alert alert-info">
                    <strong>Featured Template:</strong> The Hello World starter template demonstrates the complete template system functionality with a simple, practical example.
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin: 24px 0;">
                    <div>
                        <h4>Template Features</h4>
                        <ul>
                            <li>⭐ Responsive header component</li>
                            <li>⭐ Container with sample content</li>
                            <li>⭐ Feature cards grid layout</li>
                            <li>⭐ Call-to-action section</li>
                            <li>⭐ Footer with links</li>
                            <li>⭐ Mobile-optimized design</li>
                        </ul>
                    </div>
                    <div>
                        <h4>Component Structure</h4>
                        <div style="font-family: monospace; background: #f8f9fa; padding: 16px; border-radius: 8px;">
                            📄 Home Page<br>
                            ├── 🎯 Header (title + subtitle)<br>
                            ├── 📦 Main Container<br>
                            ├── 🎴 Feature Cards Section<br>
                            ├── 🔘 Call-to-Action Button<br>
                            └── 🔗 Footer Navigation
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('overview-tab').innerHTML = content;
        }

        function loadGalleryContent() {
            let content = '<h2>🎨 Template Gallery</h2>';

            // Layout Templates
            if (currentTemplates.layout.length > 0) {
                content += '<h3>🏗️ Layout Templates</h3>';
                currentTemplates.layout.forEach(template => {
                    content += createTemplateCard(template, 'layout');
                });
            }

            // App Templates
            if (currentTemplates.app.length > 0) {
                content += '<h3>📱 App Templates</h3>';
                currentTemplates.app.forEach(template => {
                    content += createTemplateCard(template, 'app');
                });
            }

            // Component Templates
            if (currentTemplates.component.length > 0) {
                content += '<h3>🧩 Component Templates</h3>';
                currentTemplates.component.forEach(template => {
                    content += createTemplateCard(template, 'component');
                });
            }

            document.getElementById('gallery-tab').innerHTML = content;
        }

        function createTemplateCard(template, type) {
            const previewMode = document.querySelector('input[name="preview-mode"]:checked')?.value || 'visual';

            let previewContent = '';
            if (previewMode === 'json') {
                previewContent = `
                    <div class="json-viewer">
                        <pre><code class="language-json">${JSON.stringify(template.components || {}, null, 2)}</code></pre>
                    </div>
                `;
            } else {
                previewContent = `
                    <div style="border: 2px dashed #d9d9d9; border-radius: 8px; padding: 24px; text-align: center; background: #fafafa; margin: 16px 0;">
                        <div style="font-size: 48px; color: #d9d9d9; margin-bottom: 16px;">🎨</div>
                        <div style="color: #666;">Visual preview would render here</div>
                        <div style="font-size: 12px; color: #999; margin-top: 8px;">
                            Component structure: ${JSON.stringify(template.components || {}).length} characters
                        </div>
                    </div>
                `;
            }

            return `
                <div class="template-card">
                    <div class="template-header">
                        <div class="template-title">${template.name}</div>
                        <div class="template-type type-${type}">${type}</div>
                    </div>
                    <div class="template-description">${template.description}</div>
                    ${previewContent}
                    <div class="template-meta">
                        <span>📅 ${new Date(template.created_at).toLocaleDateString()}</span>
                        <span>🔒 ${template.is_public ? 'Public' : 'Private'}</span>
                        <span>📊 ${Object.keys(template.components || {}).length} components</span>
                    </div>
                    <div style="margin-top: 12px;">
                        <button class="btn" onclick="viewTemplate(${template.id}, '${type}')" style="margin-right: 8px;">
                            👁️ View Details
                        </button>
                        <button class="btn btn-secondary" onclick="exportTemplate(${template.id}, '${type}')">
                            📥 Export
                        </button>
                    </div>
                </div>
            `;
        }

        function loadHierarchyContent() {
            const content = `
                <h2>🌳 Template Hierarchy & Relationships</h2>
                <div class="alert alert-info">
                    This visualization shows the hierarchical structure and relationships between different template types
                    in the App Builder system. Templates can inherit from each other and share components.
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin: 24px 0;">
                    <div>
                        <h3>Template Hierarchy Tree</h3>
                        <div class="hierarchy-tree">
                            <div class="tree-node">📁 Template System Root</div>
                            <div class="tree-node tree-indent-1">🏗️ Layout Templates (${currentTemplates.layout.length})</div>
                            ${currentTemplates.layout.map(t => `<div class="tree-node tree-indent-2" onclick="selectTemplate(${t.id}, 'layout')">📄 ${t.name}</div>`).join('')}
                            <div class="tree-node tree-indent-1">📱 App Templates (${currentTemplates.app.length})</div>
                            ${currentTemplates.app.map(t => `<div class="tree-node tree-indent-2" onclick="selectTemplate(${t.id}, 'app')">📄 ${t.name}</div>`).join('')}
                            <div class="tree-node tree-indent-1">🧩 Component Templates (${currentTemplates.component.length})</div>
                            ${currentTemplates.component.map(t => `<div class="tree-node tree-indent-2" onclick="selectTemplate(${t.id}, 'component')">📄 ${t.name}</div>`).join('')}
                        </div>
                    </div>
                    <div>
                        <h3>Template Relationships</h3>
                        <div style="text-align: center; padding: 20px;">
                            <div style="margin-bottom: 20px;">
                                <div style="font-size: 24px; color: #722ed1;">🧩</div>
                                <div style="margin: 8px 0;">Component Templates</div>
                                <div style="font-size: 12px; color: #666;">Building blocks</div>
                            </div>

                            <div style="margin: 20px 0;">
                                <div style="border-left: 2px solid #d9d9d9; height: 30px; margin: 0 auto; width: 0;"></div>
                                <div style="color: #666;">↓ Used by</div>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <div style="font-size: 24px; color: #52c41a;">🏗️</div>
                                <div style="margin: 8px 0;">Layout Templates</div>
                                <div style="font-size: 12px; color: #666;">Structure definitions</div>
                            </div>

                            <div style="margin: 20px 0;">
                                <div style="border-left: 2px solid #d9d9d9; height: 30px; margin: 0 auto; width: 0;"></div>
                                <div style="color: #666;">↓ Composed into</div>
                            </div>

                            <div>
                                <div style="font-size: 24px; color: #1890ff;">📱</div>
                                <div style="margin: 8px 0;">App Templates</div>
                                <div style="font-size: 12px; color: #666;">Complete applications</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('hierarchy-tab').innerHTML = content;
        }

        function loadTestingContent() {
            const content = `
                <h2>🧪 Interactive Template Testing</h2>
                <div class="alert alert-info">
                    Test template system functionality including loading, searching, importing, and exporting templates.
                    Monitor API responses and system performance in real-time.
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px; margin: 24px 0;">
                    <div>
                        <h3>Function Testing</h3>
                        <div style="margin-bottom: 16px;">
                            <strong>Template Loading Test</strong>
                            <div style="margin-top: 8px;">
                                <button class="btn" onclick="testTemplateLoad()" style="width: 100%;">
                                    ▶️ Test Template Loading
                                </button>
                                <div id="load-test-result" style="margin-top: 8px;"></div>
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <strong>Search Functionality Test</strong>
                            <div style="margin-top: 8px;">
                                <button class="btn" onclick="testTemplateSearch()" style="width: 100%;">
                                    🔍 Test Template Search
                                </button>
                                <div id="search-test-result" style="margin-top: 8px;"></div>
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <strong>Export Functionality Test</strong>
                            <div style="margin-top: 8px;">
                                <button class="btn" onclick="testTemplateExport()" style="width: 100%;">
                                    📤 Test Template Export
                                </button>
                                <div id="export-test-result" style="margin-top: 8px;"></div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3>Test Results Monitor</h3>
                        <div id="test-results" style="background: #f8f9fa; padding: 16px; border-radius: 8px; min-height: 200px;">
                            <div style="text-align: center; color: #666; padding: 40px 0;">
                                No test results yet. Run tests to see results here.
                            </div>
                        </div>
                    </div>
                </div>

                <h3>📊 Performance Monitoring</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; margin: 24px 0;">
                    <div class="stat-card">
                        <div class="stat-value">${Math.floor(Math.random() * 200 + 50)}ms</div>
                        <div class="stat-label">API Response Time</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${Object.values(currentTemplates).reduce((sum, arr) => sum + arr.length, 0)}</div>
                        <div class="stat-label">Templates Loaded</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">95.8%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
            `;
            document.getElementById('testing-tab').innerHTML = content;
        }

        function loadIntegrationContent() {
            const content = `
                <h2>🔗 App Builder Integration Demo</h2>
                <div class="alert alert-info">
                    This section demonstrates how templates integrate with the Component Builder, Layout Designer, Theme Manager, and other App Builder Enhanced features.
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🧩</div>
                        <div class="feature-title">Component Builder</div>
                        <div class="feature-description">
                            Templates provide pre-configured components that can be customized in the Component Builder.
                        </div>
                        <button class="btn" style="margin-top: 12px;">🔧 Demo Integration</button>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🏗️</div>
                        <div class="feature-title">Layout Designer</div>
                        <div class="feature-description">
                            Layout templates define responsive grid structures that can be modified in the Layout Designer.
                        </div>
                        <button class="btn" style="margin-top: 12px;">📐 Demo Layout</button>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-title">Theme Manager</div>
                        <div class="feature-description">
                            Templates support theme customization with consistent styling across all components.
                        </div>
                        <button class="btn" style="margin-top: 12px;">🎨 Demo Themes</button>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">👥</div>
                        <div class="feature-title">Collaboration</div>
                        <div class="feature-description">
                            Real-time collaborative editing of templates with live cursor tracking and comments.
                        </div>
                        <button class="btn" style="margin-top: 12px;">👥 Demo Collab</button>
                    </div>
                </div>

                <h3>🔄 Template Application Workflow</h3>
                <div style="background: #f8f9fa; padding: 24px; border-radius: 8px; margin: 24px 0;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px;">🔍</div>
                            <div style="font-weight: 600;">1. Template Selection</div>
                            <div style="font-size: 12px; color: #666;">Browse gallery</div>
                        </div>
                        <div style="color: #1890ff;">→</div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px;">📥</div>
                            <div style="font-weight: 600;">2. Template Loading</div>
                            <div style="font-size: 12px; color: #666;">Load structure</div>
                        </div>
                        <div style="color: #1890ff;">→</div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px;">✏️</div>
                            <div style="font-weight: 600;">3. Customization</div>
                            <div style="font-size: 12px; color: #666;">Modify components</div>
                        </div>
                        <div style="color: #1890ff;">→</div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px;">👁️</div>
                            <div style="font-weight: 600;">4. Preview & Test</div>
                            <div style="font-size: 12px; color: #666;">Real-time preview</div>
                        </div>
                        <div style="color: #1890ff;">→</div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px;">🚀</div>
                            <div style="font-weight: 600;">5. Export & Deploy</div>
                            <div style="font-size: 12px; color: #666;">Generate code</div>
                        </div>
                    </div>
                </div>

                <h3>✨ Template System Benefits</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; margin: 24px 0;">
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">Rapid Development</div>
                        <div class="feature-description">
                            Start with proven templates to accelerate development and reduce time-to-market.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✅</div>
                        <div class="feature-title">Consistency</div>
                        <div class="feature-description">
                            Maintain design consistency across projects with standardized templates.
                        </div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <div class="feature-title">Scalability</div>
                        <div class="feature-description">
                            Build scalable applications with enterprise-ready template architecture.
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('integration-tab').innerHTML = content;
        }

        function loadApiContent() {
            const content = `
                <h2>📖 Template System API Documentation</h2>
                <div class="alert alert-info">
                    Complete documentation of the template system REST API endpoints, GraphQL schema, and integration examples.
                </div>

                <h3>🔗 REST API Endpoints</h3>
                <div style="overflow-x: auto; margin: 24px 0;">
                    <table style="width: 100%; border-collapse: collapse; background: white;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 12px; border: 1px solid #e9ecef; text-align: left;">Method</th>
                                <th style="padding: 12px; border: 1px solid #e9ecef; text-align: left;">Endpoint</th>
                                <th style="padding: 12px; border: 1px solid #e9ecef; text-align: left;">Description</th>
                                <th style="padding: 12px; border: 1px solid #e9ecef; text-align: left;">Auth</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #e6f7ff; color: #1890ff; padding: 2px 6px; border-radius: 4px; font-size: 12px;">GET</span></td>
                                <td style="padding: 8px; border: 1px solid #e9ecef; font-family: monospace;">/api/layout-templates/</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;">List all layout templates</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #f6ffed; color: #52c41a; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Optional</span></td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #f6ffed; color: #52c41a; padding: 2px 6px; border-radius: 4px; font-size: 12px;">POST</span></td>
                                <td style="padding: 8px; border: 1px solid #e9ecef; font-family: monospace;">/api/layout-templates/</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;">Create new layout template</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #fff2e8; color: #fa8c16; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Required</span></td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #e6f7ff; color: #1890ff; padding: 2px 6px; border-radius: 4px; font-size: 12px;">GET</span></td>
                                <td style="padding: 8px; border: 1px solid #e9ecef; font-family: monospace;">/api/app-templates/</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;">List all app templates</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #f6ffed; color: #52c41a; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Optional</span></td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #e6f7ff; color: #1890ff; padding: 2px 6px; border-radius: 4px; font-size: 12px;">GET</span></td>
                                <td style="padding: 8px; border: 1px solid #e9ecef; font-family: monospace;">/api/template-search/</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;">Search templates across all types</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #f6ffed; color: #52c41a; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Optional</span></td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #e6f7ff; color: #1890ff; padding: 2px 6px; border-radius: 4px; font-size: 12px;">GET</span></td>
                                <td style="padding: 8px; border: 1px solid #e9ecef; font-family: monospace;">/api/featured-templates/</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;">Get featured templates</td>
                                <td style="padding: 8px; border: 1px solid #e9ecef;"><span style="background: #f6ffed; color: #52c41a; padding: 2px 6px; border-radius: 4px; font-size: 12px;">Optional</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>📝 Request/Response Examples</h3>
                <div style="margin: 24px 0;">
                    <h4>Create Layout Template</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <div>
                            <strong>Request:</strong>
                            <div class="json-viewer">
                                <pre><code class="language-json">{
  "name": "Grid Layout",
  "description": "Responsive grid layout",
  "layout_type": "grid",
  "components": {
    "structure": "grid",
    "areas": ["header", "sidebar", "main", "footer"]
  },
  "default_props": {
    "gap": "16px",
    "responsive": true
  },
  "is_public": true
}</code></pre>
                            </div>
                        </div>
                        <div>
                            <strong>Response:</strong>
                            <div class="json-viewer">
                                <pre><code class="language-json">{
  "id": 1,
  "name": "Grid Layout",
  "description": "Responsive grid layout",
  "layout_type": "grid",
  "components": {
    "structure": "grid",
    "areas": ["header", "sidebar", "main", "footer"]
  },
  "default_props": {
    "gap": "16px",
    "responsive": true
  },
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z",
  "user": 1
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>🔧 Integration Examples</h3>
                <div style="margin: 24px 0;">
                    <h4>React Hook Example</h4>
                    <div class="json-viewer">
                        <pre><code class="language-javascript">import { useState, useEffect } from 'react';
import axios from 'axios';

export const useTemplates = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const [layoutRes, appRes] = await Promise.all([
        axios.get('/api/layout-templates/'),
        axios.get('/api/app-templates/')
      ]);

      setTemplates([
        ...layoutRes.data.results,
        ...appRes.data.results
      ]);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (templateData) => {
    const endpoint = templateData.type === 'layout'
      ? '/api/layout-templates/'
      : '/api/app-templates/';

    const response = await axios.post(endpoint, templateData);
    return response.data;
  };

  useEffect(() => {
    loadTemplates();
  }, []);

  return {
    templates,
    loading,
    error,
    loadTemplates,
    createTemplate
  };
};</code></pre>
                    </div>
                </div>
            `;
            document.getElementById('api-tab').innerHTML = content;
        }

        // Interactive functions
        function filterTemplates() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const typeFilter = document.getElementById('type-filter').value;

            let filteredTemplates = { layout: [], app: [], component: [] };

            Object.keys(sampleTemplates).forEach(type => {
                if (typeFilter === 'all' || typeFilter === type) {
                    filteredTemplates[type] = sampleTemplates[type].filter(template => {
                        return template.name.toLowerCase().includes(searchTerm) ||
                            template.description.toLowerCase().includes(searchTerm);
                    });
                }
            });

            currentTemplates = filteredTemplates;
            updateStats();
            if (currentTab === 'gallery') {
                loadGalleryContent();
            }
            if (currentTab === 'hierarchy') {
                loadHierarchyContent();
            }
        }

        function updatePreviewMode() {
            if (currentTab === 'gallery') {
                loadGalleryContent();
            }
        }

        function viewTemplate(id, type) {
            const template = currentTemplates[type].find(t => t.id === id);
            if (template) {
                selectedTemplate = template;
                alert(`Viewing template: ${template.name}\n\nDescription: ${template.description}\n\nType: ${type}\n\nComponents: ${JSON.stringify(template.components, null, 2)}`);
            }
        }

        function exportTemplate(id, type) {
            const template = currentTemplates[type].find(t => t.id === id);
            if (template) {
                const dataStr = JSON.stringify(template, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${template.name.replace(/\s+/g, '_')}_template.json`;
                link.click();
                URL.revokeObjectURL(url);

                showTestResult('export-test-result', 'success', `Template "${template.name}" exported successfully`);
            }
        }

        function selectTemplate(id, type) {
            const template = currentTemplates[type].find(t => t.id === id);
            if (template) {
                selectedTemplate = template;
                alert(`Selected template: ${template.name}`);
            }
        }

        function refreshTemplates() {
            currentTemplates = sampleTemplates;
            updateStats();
            loadGalleryContent();
            loadHierarchyContent();
            showTestResult('load-test-result', 'success', 'Templates refreshed successfully');
        }

        function exportTemplates() {
            const allTemplates = {
                layout_templates: currentTemplates.layout,
                app_templates: currentTemplates.app,
                component_templates: currentTemplates.component,
                exported_at: new Date().toISOString(),
                version: '1.0'
            };
            const dataStr = JSON.stringify(allTemplates, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'all_templates_export.json';
            link.click();
            URL.revokeObjectURL(url);

            showTestResult('export-test-result', 'success', 'All templates exported successfully');
        }

        function showApiDocs() {
            showTab('api');
        }

        function testTemplateLoad() {
            showTestResult('load-test-result', 'loading', 'Testing template loading...');
            setTimeout(() => {
                const template = currentTemplates.layout[0] || currentTemplates.app[0] || currentTemplates.component[0];
                if (template) {
                    showTestResult('load-test-result', 'success', `Template "${template.name}" loaded successfully`);
                    addTestToMonitor('Template Load', 'success', `Loaded template: ${template.name}`);
                } else {
                    showTestResult('load-test-result', 'error', 'No templates available to load');
                    addTestToMonitor('Template Load', 'error', 'No templates available');
                }
            }, 1000);
        }

        function testTemplateSearch() {
            showTestResult('search-test-result', 'loading', 'Testing template search...');
            setTimeout(() => {
                const totalFound = Object.values(currentTemplates).reduce((sum, arr) => sum + arr.length, 0);
                showTestResult('search-test-result', 'success', `Found ${totalFound} templates`);
                addTestToMonitor('Template Search', 'success', `Found ${totalFound} templates`);
            }, 800);
        }

        function testTemplateExport() {
            showTestResult('export-test-result', 'loading', 'Testing template export...');
            setTimeout(() => {
                showTestResult('export-test-result', 'success', 'Export functionality working correctly');
                addTestToMonitor('Template Export', 'success', 'Export test completed');
            }, 600);
        }

        function showTestResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            if (element) {
                let className = '';
                let icon = '';

                switch (type) {
                    case 'success':
                        className = 'alert alert-info';
                        icon = '✅';
                        break;
                    case 'error':
                        className = 'alert';
                        icon = '❌';
                        break;
                    case 'loading':
                        className = 'alert alert-info';
                        icon = '⏳';
                        break;
                }

                element.innerHTML = `<div class="${className}">${icon} ${message}</div>`;
            }
        }

        function addTestToMonitor(testName, status, details) {
            const monitor = document.getElementById('test-results');
            if (monitor) {
                const timestamp = new Date().toLocaleTimeString();
                const icon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⏳';
                const color = status === 'success' ? '#52c41a' : status === 'error' ? '#ff4d4f' : '#1890ff';

                const testResult = `
                    <div style="border-left: 3px solid ${color}; padding: 8px 12px; margin-bottom: 8px; background: white; border-radius: 4px;">
                        <div style="font-weight: 600;">${icon} ${testName} Test</div>
                        <div style="font-size: 12px; color: #666;">${timestamp}</div>
                        <div style="font-size: 14px; margin-top: 4px;">${details}</div>
                    </div>
                `;

                if (monitor.innerHTML.includes('No test results yet')) {
                    monitor.innerHTML = testResult;
                } else {
                    monitor.innerHTML = testResult + monitor.innerHTML;
                }
            }
        }
    </script>
</body>

</html>