import React from 'react';
import { createRoot } from 'react-dom/client';

// Expose React to global scope for debugging and verification - EARLY EXPOSURE
window.React = React;
window.ReactDOM = { createRoot };

// Immediate verification
console.log('🚀 React loaded successfully:', React.version);
console.log('🔧 React available globally:', typeof window.React);

// Additional global exposure for testing
if (typeof window !== 'undefined') {
  // Safely set React DevTools hook - check if it's already defined and writable
  try {
    if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = {};
    }
  } catch (error) {
    // React DevTools is already installed and the property is read-only
    console.log('React DevTools already installed, skipping hook setup');
  }

  window.__REACT_VERSION__ = React.version;
  window.__REACT_LOADED__ = true;

  console.log('✅ React globals locked and verified');
}

// Get the root element
const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('❌ Root element not found!');
  throw new Error('Root element with id "root" not found');
}

console.log('✅ Root element found:', rootElement);

// Create React root
const root = createRoot(rootElement);

// Minimal App component for testing
const MinimalApp = () => {
  const [count, setCount] = React.useState(0);

  return React.createElement('div', {
    style: {
      padding: '40px',
      textAlign: 'center',
      fontFamily: 'Arial, sans-serif',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }
  }, [
    React.createElement('h1', { key: 'title' }, '🎉 React is Working!'),
    React.createElement('p', { key: 'version' }, `React Version: ${React.version}`),
    React.createElement('p', { key: 'time' }, `Loaded at: ${new Date().toLocaleString()}`),
    React.createElement('div', {
      key: 'counter',
      style: {
        margin: '20px 0',
        padding: '20px',
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '10px'
      }
    }, [
      React.createElement('p', { key: 'count-text' }, `Counter: ${count}`),
      React.createElement('button', {
        key: 'increment',
        onClick: () => setCount(count + 1),
        style: {
          padding: '10px 20px',
          fontSize: '16px',
          background: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          margin: '5px'
        }
      }, 'Increment'),
      React.createElement('button', {
        key: 'decrement',
        onClick: () => setCount(count - 1),
        style: {
          padding: '10px 20px',
          fontSize: '16px',
          background: '#dc3545',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          margin: '5px'
        }
      }, 'Decrement')
    ]),
    React.createElement('div', {
      key: 'links',
      style: { marginTop: '20px' }
    }, [
      React.createElement('a', {
        key: 'back-link',
        href: '/',
        style: {
          color: 'white',
          textDecoration: 'underline',
          margin: '0 10px'
        }
      }, '← Back to Main App'),
      React.createElement('a', {
        key: 'debug-link',
        href: '/debug-react.html',
        style: {
          color: 'white',
          textDecoration: 'underline',
          margin: '0 10px'
        }
      }, 'Debug Tool →')
    ])
  ]);
};

// Render the minimal app
try {
  console.log('🚀 Starting Minimal React App...');

  // Set loading flag
  window.__APP_LOADING__ = true;

  root.render(
    React.createElement(React.StrictMode, null,
      React.createElement(MinimalApp, null)
    )
  );

  // Mark app as loaded
  window.__APP_LOADED__ = true;
  window.__APP_LOADING__ = false;

  console.log('✅ Minimal React App loaded successfully!');
  console.log('🔍 Final React global check:', {
    React: typeof window.React,
    ReactDOM: typeof window.ReactDOM,
    version: window.__REACT_VERSION__,
    loaded: window.__REACT_LOADED__
  });

} catch (error) {
  console.error('❌ Failed to load Minimal React App:', error);

  // Mark app as failed
  window.__APP_LOADED__ = false;
  window.__APP_LOADING__ = false;
  window.__APP_ERROR__ = error.message;

  // Fallback UI
  root.render(
    React.createElement('div', {
      style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        fontFamily: 'Arial, sans-serif',
        background: '#f8fafc',
        color: '#1f2937',
        textAlign: 'center',
        padding: '2rem'
      }
    }, [
      React.createElement('div', {
        key: 'error-container',
        style: {
          background: 'white',
          padding: '2rem',
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          maxWidth: '500px'
        }
      }, [
        React.createElement('h1', {
          key: 'error-title',
          style: { margin: '0 0 1rem', color: '#dc2626' }
        }, 'React Loading Error'),
        React.createElement('p', {
          key: 'error-message',
          style: { margin: '0 0 1rem' }
        }, 'There was an error loading the React application.'),
        React.createElement('pre', {
          key: 'error-details',
          style: {
            background: '#f3f4f6',
            padding: '1rem',
            borderRadius: '6px',
            fontSize: '12px',
            textAlign: 'left',
            overflow: 'auto'
          }
        }, error.message),
        React.createElement('button', {
          key: 'reload-button',
          onClick: () => window.location.reload(),
          style: {
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            padding: '0.75rem 1.5rem',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }
        }, 'Reload Page')
      ])
    ])
  );
}
