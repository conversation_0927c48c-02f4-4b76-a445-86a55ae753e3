[{"id": 1, "name": "[Default] Hello World Starter", "description": "A simple starter template with basic React components, responsive design, and sample content. Perfect for getting started with the App Builder.", "app_category": "other", "components": {"pages": [{"name": "home", "title": "Home", "components": [{"id": "header-1", "type": "header", "props": {"title": "Hello World!", "subtitle": "Welcome to your new app built with App Builder", "backgroundColor": "#1890ff", "textColor": "#ffffff", "height": "200px", "textAlign": "center"}, "position": {"x": 0, "y": 0, "width": 12, "height": 3}}, {"id": "container-1", "type": "container", "props": {"padding": "40px 20px", "maxWidth": "1200px", "margin": "0 auto", "backgroundColor": "#ffffff"}, "position": {"x": 0, "y": 3, "width": 12, "height": 8}, "children": [{"id": "text-1", "type": "text", "props": {"content": "This is your first app! You can drag and drop components, edit their properties, and export your creation as working code.", "fontSize": "18px", "lineHeight": "1.6", "color": "#333333", "marginBottom": "30px"}, "position": {"x": 0, "y": 0, "width": 12, "height": 2}}, {"id": "button-1", "type": "button", "props": {"text": "Get Started", "backgroundColor": "#52c41a", "color": "#ffffff", "padding": "12px 24px", "borderRadius": "6px", "fontSize": "16px", "border": "none", "cursor": "pointer", "marginRight": "15px"}, "position": {"x": 0, "y": 2, "width": 3, "height": 1}}, {"id": "button-2", "type": "button", "props": {"text": "Learn More", "backgroundColor": "transparent", "color": "#1890ff", "padding": "12px 24px", "borderRadius": "6px", "fontSize": "16px", "border": "2px solid #1890ff", "cursor": "pointer"}, "position": {"x": 3, "y": 2, "width": 3, "height": 1}}]}, {"id": "footer-1", "type": "footer", "props": {"content": "© 2024 Your App. Built with App Builder.", "backgroundColor": "#f5f5f5", "textAlign": "center", "padding": "20px", "color": "#666666", "fontSize": "14px"}, "position": {"x": 0, "y": 11, "width": 12, "height": 1}}]}], "theme": {"primaryColor": "#1890ff", "secondaryColor": "#52c41a", "backgroundColor": "#ffffff", "textColor": "#333333", "fontFamily": "Inter, -apple-system, BlinkMacSystemFont, sans-serif", "borderRadius": "6px", "spacing": {"small": "8px", "medium": "16px", "large": "24px", "xlarge": "40px"}}, "layout": {"type": "grid", "columns": 12, "gap": "16px", "responsive": true, "breakpoints": {"mobile": "768px", "tablet": "1024px", "desktop": "1200px"}}}, "default_props": {"theme": "modern", "primaryColor": "#1890ff", "secondaryColor": "#52c41a", "fontFamily": "Inter", "responsive": true, "animations": true}, "required_components": ["header", "container", "text", "button", "footer"], "preview_image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjVmNWY1Ii8+CjxyZWN0IHk9IjAiIHdpZHRoPSI0MDAiIGhlaWdodD0iODAiIGZpbGw9IiMxODkwZmYiLz4KPHR5cGUgeD0iMjAwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+SGVsbG8gV29ybGQhPC90ZXh0Pgo8dGV4dCB4PSIyMDAiIHk9IjEzMCIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjE2IiBmaWxsPSIjMzMzIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Zb3VyIGZpcnN0IGFwcCBidWlsdCB3aXRoIEFwcCBCdWlsZGVyPC90ZXh0Pgo8cmVjdCB4PSIxNDAiIHk9IjE1MCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSI0MCIgcng9IjYiIGZpbGw9IiM1MmM0MWEiLz4KPHR5cGUgeD0iMjAwIiB5PSIxNzUiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkdldCBTdGFydGVkPC90ZXh0Pgo8cmVjdCB5PSIyNjAiIHdpZHRoPSI0MDAiIGhlaWdodD0iNDAiIGZpbGw9IiNmNWY1ZjUiLz4KPHR5cGUgeD0iMjAwIiB5PSIyODUiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+wqkgMjAyNCBZb3VyIEFwcC4gQnVpbHQgd2l0aCBBcHAgQnVpbGRlci48L3RleHQ+Cjwvc3ZnPgo=", "is_public": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 2, "name": "[Default] Simple Landing Page", "description": "A clean, modern landing page template with hero section, features, and contact form", "app_category": "landing", "components": {"pages": [{"name": "home", "title": "<PERSON>", "components": [{"id": "hero-1", "type": "hero", "props": {"title": "Build Amazing Apps", "subtitle": "Create beautiful, functional applications with our drag-and-drop builder", "backgroundImage": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "height": "70vh"}}, {"id": "features-1", "type": "features", "props": {"title": "Why Choose Our Platform", "features": [{"title": "Easy to Use", "description": "Drag and drop components to build your app", "icon": "drag"}, {"title": "Export Code", "description": "Generate clean, production-ready code", "icon": "code"}, {"title": "Responsive", "description": "Your apps work perfectly on all devices", "icon": "mobile"}]}}]}]}, "default_props": {"theme": "modern", "primaryColor": "#667eea", "responsive": true}, "required_components": ["hero", "features", "button"], "preview_image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSJ1cmwoI2dyYWQpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNjY3ZWVhO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM3NjRiYTI7c3RvcC1vcGFjaXR5OjEiIC8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHR5cGUgeD0iMjAwIiB5PSIxMDAiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIyOCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkJ1aWxkIEFtYXppbmcgQXBwczwvdGV4dD4KPHR5cGUgeD0iMjAwIiB5PSIxMzAiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkNyZWF0ZSBiZWF1dGlmdWwgYXBwbGljYXRpb25zPC90ZXh0Pgo8cmVjdCB4PSIxNTAiIHk9IjE2MCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSI0MCIgcng9IjgiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMiIvPgo8dGV4dCB4PSIyMDAiIHk9IjE4NSIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+R2V0IFN0YXJ0ZWQ8L3RleHQ+Cjwvc3ZnPgo=", "is_public": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, {"id": 3, "name": "[Default] Dashboard Template", "description": "A basic dashboard template with sidebar navigation and content area", "app_category": "dashboard", "components": {"pages": [{"name": "dashboard", "title": "Dashboard", "layout": "sidebar", "components": [{"id": "sidebar-1", "type": "sidebar", "props": {"width": "250px", "backgroundColor": "#001529", "items": [{"label": "Dashboard", "icon": "dashboard", "active": true}, {"label": "Analytics", "icon": "chart"}, {"label": "Settings", "icon": "settings"}]}}, {"id": "content-1", "type": "content", "props": {"padding": "24px", "backgroundColor": "#f0f2f5"}}]}]}, "default_props": {"theme": "dashboard", "primaryColor": "#1890ff", "layout": "sidebar"}, "required_components": ["sidebar", "content", "card"], "preview_image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjBmMmY1Ii8+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMDAxNTI5Ii8+CjxyZWN0IHg9IjEyMCIgeT0iMjAiIHdpZHRoPSIyNjAiIGhlaWdodD0iODAiIHJ4PSI4IiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIxMjAiIHk9IjEyMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxNjAiIHJ4PSI4IiBmaWxsPSJ3aGl0ZSIvPgo8cmVjdCB4PSIyNjAiIHk9IjEyMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxNjAiIHJ4PSI4IiBmaWxsPSJ3aGl0ZSIvPgo8dGV4dCB4PSI1MCIgeT0iNDAiIGZvbnQtZmFtaWx5PSJJbnRlciIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRhc2hib2FyZDwvdGV4dD4KPHR5cGUgeD0iNTAiIHk9IjgwIiBmb250LWZhbWlseT0iSW50ZXIiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiNhYWEiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkFuYWx5dGljczwvdGV4dD4KPHR5cGUgeD0iNTAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjEyIiBmaWxsPSIjYWFhIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5TZXR0aW5nczwvdGV4dD4KPC9zdmc+Cg==", "is_public": true, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}]