/**
 * Tutorial Hook
 * 
 * Manages tutorial state and progression for the App Builder
 */

import { useState, useCallback, useEffect } from 'react';
import { useLocalStorage } from './useLocalStorage';

const TUTORIAL_STEPS = {
  WELCOME: 'welcome',
  COMPONENT_PALETTE: 'component-palette',
  DRAG_DROP: 'drag-drop',
  PROPERTIES: 'properties',
  PREVIEW: 'preview',
  SAVE: 'save',
  COMPLETE: 'complete'
};

const TUTORIAL_STORAGE_KEY = 'app-builder-tutorial-progress';

export default function useTutorial() {
  const [tutorialProgress, setTutorialProgress] = useLocalStorage(TUTORIAL_STORAGE_KEY, {
    currentStep: TUTORIAL_STEPS.WELCOME,
    completedSteps: [],
    isActive: false,
    hasCompletedTutorial: false
  });

  const [isVisible, setIsVisible] = useState(false);
  const [currentStepData, setCurrentStepData] = useState(null);

  // Tutorial step definitions
  const tutorialSteps = {
    [TUTORIAL_STEPS.WELCOME]: {
      title: 'Welcome to App Builder',
      description: 'Let\'s take a quick tour of the App Builder interface.',
      target: null,
      position: 'center'
    },
    [TUTORIAL_STEPS.COMPONENT_PALETTE]: {
      title: 'Component Palette',
      description: 'Drag components from here to build your app.',
      target: '.component-palette',
      position: 'right'
    },
    [TUTORIAL_STEPS.DRAG_DROP]: {
      title: 'Drag and Drop',
      description: 'Drag components to the canvas to add them to your app.',
      target: '.canvas-area',
      position: 'left'
    },
    [TUTORIAL_STEPS.PROPERTIES]: {
      title: 'Properties Panel',
      description: 'Customize component properties here.',
      target: '.properties-panel',
      position: 'left'
    },
    [TUTORIAL_STEPS.PREVIEW]: {
      title: 'Preview',
      description: 'See how your app looks in real-time.',
      target: '.preview-area',
      position: 'bottom'
    },
    [TUTORIAL_STEPS.SAVE]: {
      title: 'Save Your Work',
      description: 'Don\'t forget to save your progress!',
      target: '.save-button',
      position: 'bottom'
    },
    [TUTORIAL_STEPS.COMPLETE]: {
      title: 'Tutorial Complete!',
      description: 'You\'re ready to build amazing apps!',
      target: null,
      position: 'center'
    }
  };

  // Start tutorial
  const startTutorial = useCallback(() => {
    setTutorialProgress(prev => ({
      ...prev,
      isActive: true,
      currentStep: TUTORIAL_STEPS.WELCOME
    }));
    setIsVisible(true);
    setCurrentStepData(tutorialSteps[TUTORIAL_STEPS.WELCOME]);
  }, [setTutorialProgress]);

  // Next step
  const nextStep = useCallback(() => {
    const stepOrder = Object.values(TUTORIAL_STEPS);
    const currentIndex = stepOrder.indexOf(tutorialProgress.currentStep);
    
    if (currentIndex < stepOrder.length - 1) {
      const nextStep = stepOrder[currentIndex + 1];
      setTutorialProgress(prev => ({
        ...prev,
        currentStep: nextStep,
        completedSteps: [...prev.completedSteps, prev.currentStep]
      }));
      setCurrentStepData(tutorialSteps[nextStep]);
    } else {
      completeTutorial();
    }
  }, [tutorialProgress.currentStep, setTutorialProgress]);

  // Previous step
  const previousStep = useCallback(() => {
    const stepOrder = Object.values(TUTORIAL_STEPS);
    const currentIndex = stepOrder.indexOf(tutorialProgress.currentStep);
    
    if (currentIndex > 0) {
      const prevStep = stepOrder[currentIndex - 1];
      setTutorialProgress(prev => ({
        ...prev,
        currentStep: prevStep,
        completedSteps: prev.completedSteps.filter(step => step !== prevStep)
      }));
      setCurrentStepData(tutorialSteps[prevStep]);
    }
  }, [tutorialProgress.currentStep, setTutorialProgress]);

  // Skip tutorial
  const skipTutorial = useCallback(() => {
    setTutorialProgress(prev => ({
      ...prev,
      isActive: false,
      hasCompletedTutorial: true
    }));
    setIsVisible(false);
    setCurrentStepData(null);
  }, [setTutorialProgress]);

  // Complete tutorial
  const completeTutorial = useCallback(() => {
    setTutorialProgress(prev => ({
      ...prev,
      isActive: false,
      hasCompletedTutorial: true,
      completedSteps: Object.values(TUTORIAL_STEPS)
    }));
    setIsVisible(false);
    setCurrentStepData(null);
  }, [setTutorialProgress]);

  // Reset tutorial
  const resetTutorial = useCallback(() => {
    setTutorialProgress({
      currentStep: TUTORIAL_STEPS.WELCOME,
      completedSteps: [],
      isActive: false,
      hasCompletedTutorial: false
    });
    setIsVisible(false);
    setCurrentStepData(null);
  }, [setTutorialProgress]);

  // Go to specific step
  const goToStep = useCallback((step) => {
    if (tutorialSteps[step]) {
      setTutorialProgress(prev => ({
        ...prev,
        currentStep: step
      }));
      setCurrentStepData(tutorialSteps[step]);
    }
  }, [setTutorialProgress]);

  // Update current step data when step changes
  useEffect(() => {
    if (tutorialProgress.isActive && tutorialProgress.currentStep) {
      setCurrentStepData(tutorialSteps[tutorialProgress.currentStep]);
    }
  }, [tutorialProgress.currentStep, tutorialProgress.isActive]);

  // Calculate progress
  const progress = {
    current: Object.values(TUTORIAL_STEPS).indexOf(tutorialProgress.currentStep) + 1,
    total: Object.values(TUTORIAL_STEPS).length,
    percentage: Math.round(((Object.values(TUTORIAL_STEPS).indexOf(tutorialProgress.currentStep) + 1) / Object.values(TUTORIAL_STEPS).length) * 100)
  };

  return {
    // State
    isActive: tutorialProgress.isActive,
    isVisible,
    currentStep: tutorialProgress.currentStep,
    currentStepData,
    completedSteps: tutorialProgress.completedSteps,
    hasCompletedTutorial: tutorialProgress.hasCompletedTutorial,
    progress,
    
    // Actions
    startTutorial,
    nextStep,
    previousStep,
    skipTutorial,
    completeTutorial,
    resetTutorial,
    goToStep,
    
    // Utilities
    setIsVisible,
    steps: TUTORIAL_STEPS,
    allSteps: tutorialSteps
  };
}
