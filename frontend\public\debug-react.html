<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Debug - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background: #f0f9ff;
            border-color: #10b981;
            color: #065f46;
        }
        .error {
            background: #fef2f2;
            border-color: #ef4444;
            color: #991b1b;
        }
        .info {
            background: #f0f9ff;
            border-color: #3b82f6;
            color: #1e40af;
        }
        .warning {
            background: #fffbeb;
            border-color: #f59e0b;
            color: #92400e;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #10b981; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
        .console-output {
            max-height: 300px;
            overflow-y: auto;
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 React Debug Tool</h1>
        <p>Comprehensive debugging for React app loading issues.</p>
        
        <div id="test-results">
            <div class="test-result info">
                <span class="status-indicator status-pending"></span>
                <strong>Initializing debug session...</strong>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="runFullDiagnostic()">🔍 Run Full Diagnostic</button>
            <button onclick="checkReactGlobals()">⚛️ Check React Globals</button>
            <button onclick="checkConsoleErrors()">🚨 Check Console Errors</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
            <button onclick="window.location.href='/'">🏠 Back to App</button>
        </div>
        
        <div id="console-output" style="margin-top: 20px;">
            <h3>Console Output:</h3>
            <div id="console-log" class="console-output"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        let consoleOutput = [];
        let originalConsole = {};

        // Capture console output
        function setupConsoleCapture() {
            originalConsole.log = console.log;
            originalConsole.error = console.error;
            originalConsole.warn = console.warn;
            originalConsole.info = console.info;

            console.log = function(...args) {
                consoleOutput.push(`[LOG] ${new Date().toLocaleTimeString()} - ${args.join(' ')}`);
                updateConsoleOutput();
                originalConsole.log.apply(console, args);
            };

            console.error = function(...args) {
                consoleOutput.push(`[ERROR] ${new Date().toLocaleTimeString()} - ${args.join(' ')}`);
                updateConsoleOutput();
                originalConsole.error.apply(console, args);
            };

            console.warn = function(...args) {
                consoleOutput.push(`[WARN] ${new Date().toLocaleTimeString()} - ${args.join(' ')}`);
                updateConsoleOutput();
                originalConsole.warn.apply(console, args);
            };

            console.info = function(...args) {
                consoleOutput.push(`[INFO] ${new Date().toLocaleTimeString()} - ${args.join(' ')}`);
                updateConsoleOutput();
                originalConsole.info.apply(console, args);
            };
        }

        function updateConsoleOutput() {
            const consoleLog = document.getElementById('console-log');
            consoleLog.innerHTML = consoleOutput.slice(-50).join('\n');
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }

        function addTestResult(name, passed, message, details = '') {
            testResults.push({ name, passed, message, details, timestamp: new Date().toLocaleTimeString() });
            updateTestResults();
        }

        function updateTestResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = '';

            testResults.forEach(result => {
                const div = document.createElement('div');
                div.className = `test-result ${result.passed ? 'success' : 'error'}`;
                
                const statusClass = result.passed ? 'status-pass' : 'status-fail';
                
                div.innerHTML = `
                    <span class="status-indicator ${statusClass}"></span>
                    <strong>${result.name}</strong> (${result.timestamp}): ${result.message}
                    ${result.details ? `<pre>${result.details}</pre>` : ''}
                `;
                
                container.appendChild(div);
            });
        }

        function clearResults() {
            testResults = [];
            consoleOutput = [];
            updateTestResults();
            updateConsoleOutput();
        }

        async function checkReactGlobals() {
            console.log('🔍 Checking React globals...');
            
            // Test React availability
            const reactAvailable = typeof window.React !== 'undefined';
            addTestResult(
                'React Global',
                reactAvailable,
                reactAvailable ? `Available (v${window.React?.version || 'Unknown'})` : 'Not available',
                reactAvailable ? `Type: ${typeof window.React}\nKeys: ${Object.keys(window.React).slice(0, 10).join(', ')}...` : ''
            );

            // Test ReactDOM availability
            const reactDOMAvailable = typeof window.ReactDOM !== 'undefined';
            addTestResult(
                'ReactDOM Global',
                reactDOMAvailable,
                reactDOMAvailable ? 'Available' : 'Not available',
                reactDOMAvailable ? `Keys: ${Object.keys(window.ReactDOM).join(', ')}` : ''
            );

            // Test app loading flags
            addTestResult(
                'App Loading Flags',
                true,
                'Status check',
                `__APP_LOADED__: ${window.__APP_LOADED__}\n__APP_LOADING__: ${window.__APP_LOADING__}\n__REACT_LOADED__: ${window.__REACT_LOADED__}`
            );
        }

        async function checkConsoleErrors() {
            console.log('🚨 Checking for console errors...');
            
            // Check for JavaScript errors
            const errors = [];
            
            // Override error handler temporarily
            const originalErrorHandler = window.onerror;
            window.onerror = function(message, source, lineno, colno, error) {
                errors.push(`${message} at ${source}:${lineno}:${colno}`);
                if (originalErrorHandler) {
                    originalErrorHandler.apply(this, arguments);
                }
            };

            // Check for unhandled promise rejections
            const rejections = [];
            const originalRejectionHandler = window.onunhandledrejection;
            window.onunhandledrejection = function(event) {
                rejections.push(event.reason);
                if (originalRejectionHandler) {
                    originalRejectionHandler.apply(this, arguments);
                }
            };

            // Wait a bit to catch any immediate errors
            await new Promise(resolve => setTimeout(resolve, 2000));

            addTestResult(
                'JavaScript Errors',
                errors.length === 0,
                errors.length === 0 ? 'No errors detected' : `${errors.length} errors found`,
                errors.length > 0 ? errors.join('\n') : ''
            );

            addTestResult(
                'Promise Rejections',
                rejections.length === 0,
                rejections.length === 0 ? 'No rejections detected' : `${rejections.length} rejections found`,
                rejections.length > 0 ? rejections.map(r => r.toString()).join('\n') : ''
            );

            // Restore original handlers
            window.onerror = originalErrorHandler;
            window.onunhandledrejection = originalRejectionHandler;
        }

        async function runFullDiagnostic() {
            clearResults();
            console.log('🚀 Starting full diagnostic...');

            // Test 1: Basic environment
            addTestResult(
                'Environment',
                true,
                'Environment check',
                `URL: ${window.location.href}\nUser Agent: ${navigator.userAgent}\nTimestamp: ${new Date().toISOString()}`
            );

            // Test 2: DOM readiness
            const rootElement = document.getElementById('root');
            addTestResult(
                'Root Element',
                !!rootElement,
                rootElement ? 'Found' : 'Not found',
                rootElement ? `Content length: ${rootElement.innerHTML.length} chars\nHas children: ${rootElement.children.length > 0}` : ''
            );

            // Test 3: Script loading
            const scripts = Array.from(document.querySelectorAll('script[src]'));
            const mainScripts = scripts.filter(s => s.src.includes('main'));
            addTestResult(
                'Script Loading',
                mainScripts.length > 0,
                `${scripts.length} total scripts, ${mainScripts.length} main scripts`,
                mainScripts.map(s => s.src.split('/').pop()).join('\n')
            );

            // Test 4: React globals
            await checkReactGlobals();

            // Test 5: Console errors
            await checkConsoleErrors();

            // Test 6: Network connectivity
            try {
                const response = await fetch('/api-offline.json');
                addTestResult(
                    'Network Connectivity',
                    response.ok,
                    response.ok ? `API reachable (${response.status})` : `API error (${response.status})`,
                    `Status: ${response.status} ${response.statusText}`
                );
            } catch (error) {
                addTestResult(
                    'Network Connectivity',
                    false,
                    'Network error',
                    error.message
                );
            }

            // Test 7: WebSocket connectivity
            try {
                const ws = new WebSocket('ws://localhost:8000/ws/test/');
                let wsConnected = false;
                
                ws.onopen = () => {
                    wsConnected = true;
                    addTestResult(
                        'WebSocket Connectivity',
                        true,
                        'WebSocket connected successfully',
                        'Connection established to ws://localhost:8000/ws/test/'
                    );
                    ws.close();
                };

                ws.onerror = (error) => {
                    addTestResult(
                        'WebSocket Connectivity',
                        false,
                        'WebSocket connection failed',
                        error.toString()
                    );
                };

                // Timeout after 5 seconds
                setTimeout(() => {
                    if (!wsConnected) {
                        addTestResult(
                            'WebSocket Connectivity',
                            false,
                            'WebSocket connection timeout',
                            'No response within 5 seconds'
                        );
                        ws.close();
                    }
                }, 5000);

            } catch (error) {
                addTestResult(
                    'WebSocket Connectivity',
                    false,
                    'WebSocket error',
                    error.message
                );
            }

            console.log('✅ Full diagnostic completed!');
        }

        // Initialize console capture when page loads
        window.addEventListener('load', () => {
            setupConsoleCapture();
            console.log('🐛 React Debug Tool initialized');
            
            // Auto-run basic checks after a short delay
            setTimeout(() => {
                console.log('🔄 Running initial checks...');
                checkReactGlobals();
            }, 1000);
        });
    </script>
</body>
</html>
