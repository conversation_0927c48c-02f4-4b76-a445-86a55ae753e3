import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, List, Typography, Space, Button, Select, DatePicker, Tooltip } from 'antd';
import {
  BarChartOutlined,
  EyeOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
  BgColorsOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const AnalyticsContainer = styled.div`
  padding: 16px;
  background: #f5f5f5;
  height: 100%;
  overflow-y: auto;
`;

const MetricCard = styled(Card)`
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .ant-card-body {
    padding: 16px;
  }
`;

const TrendIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: ${props => props.trend === 'up' ? '#52c41a' : props.trend === 'down' ? '#ff4d4f' : '#666'};
  font-size: 12px;
  margin-top: 4px;
`;

const ActivityItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
`;

const AnalyticsPanel = ({
  projectId = null,
  timeRange = '7d',
  onExport = () => { },
  realTime = false
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [metrics, setMetrics] = useState({});
  const [activities, setActivities] = useState([]);
  const [componentStats, setComponentStats] = useState([]);

  // Mock analytics data
  const generateMockData = () => {
    return {
      metrics: {
        totalViews: { value: 1247, trend: 'up', change: 12.5 },
        uniqueUsers: { value: 89, trend: 'up', change: 8.3 },
        avgSessionTime: { value: 245, trend: 'down', change: -3.2 },
        bounceRate: { value: 34.2, trend: 'down', change: -5.1 },
        conversionRate: { value: 12.8, trend: 'up', change: 15.7 },
        errorRate: { value: 2.1, trend: 'down', change: -12.3 }
      },
      activities: [
        {
          id: 1,
          type: 'component_added',
          description: 'Button component added to main layout',
          user: 'John Doe',
          timestamp: new Date(Date.now() - 300000),
          impact: 'positive'
        },
        {
          id: 2,
          type: 'layout_changed',
          description: 'Grid layout applied to dashboard',
          user: 'Jane Smith',
          timestamp: new Date(Date.now() - 600000),
          impact: 'neutral'
        },
        {
          id: 3,
          type: 'theme_updated',
          description: 'Color scheme changed to dark mode',
          user: 'Mike Johnson',
          timestamp: new Date(Date.now() - 900000),
          impact: 'positive'
        },
        {
          id: 4,
          type: 'component_removed',
          description: 'Unused input field removed',
          user: 'Sarah Wilson',
          timestamp: new Date(Date.now() - 1200000),
          impact: 'negative'
        },
        {
          id: 5,
          type: 'export_generated',
          description: 'React code exported successfully',
          user: 'Tom Brown',
          timestamp: new Date(Date.now() - 1800000),
          impact: 'positive'
        }
      ],
      componentStats: [
        { name: 'Button', usage: 85, performance: 92, errors: 2 },
        { name: 'Input', usage: 78, performance: 88, errors: 5 },
        { name: 'Card', usage: 65, performance: 95, errors: 1 },
        { name: 'Table', usage: 45, performance: 82, errors: 8 },
        { name: 'Modal', usage: 32, performance: 90, errors: 3 }
      ]
    };
  };

  // Load analytics data
  useEffect(() => {
    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      const data = generateMockData();
      setMetrics(data.metrics);
      setActivities(data.activities);
      setComponentStats(data.componentStats);
      setLoading(false);
    }, 1000);
  }, [selectedTimeRange, projectId]);

  // Real-time updates
  useEffect(() => {
    if (!realTime) return;

    const interval = setInterval(() => {
      const data = generateMockData();
      setMetrics(data.metrics);
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [realTime]);

  const formatTime = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    return `${hours}h ago`;
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'component_added': return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'component_removed': return <FallOutlined style={{ color: '#ff4d4f' }} />;
      case 'layout_changed': return <BarChartOutlined style={{ color: '#1890ff' }} />;
      case 'theme_updated': return <BgColorsOutlined style={{ color: '#722ed1' }} />;
      case 'export_generated': return <DownloadOutlined style={{ color: '#fa8c16' }} />;
      default: return <ClockCircleOutlined />;
    }
  };

  const renderMetricCard = (key, metric, title, suffix = '') => (
    <MetricCard key={key}>
      <Statistic
        title={title}
        value={metric.value}
        suffix={suffix}
        valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
      />
      <TrendIndicator trend={metric.change > 0 ? 'up' : 'down'}>
        {metric.change > 0 ? <RiseOutlined /> : <FallOutlined />}
        {Math.abs(metric.change)}% vs last period
      </TrendIndicator>
    </MetricCard>
  );

  return (
    <AnalyticsContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Title level={4} style={{ margin: 0 }}>
          <BarChartOutlined style={{ marginRight: 8 }} />
          Analytics Dashboard
        </Title>

        <Space>
          <Select
            value={selectedTimeRange}
            onChange={setSelectedTimeRange}
            style={{ width: 120 }}
          >
            <Option value="1d">Last 24h</Option>
            <Option value="7d">Last 7 days</Option>
            <Option value="30d">Last 30 days</Option>
            <Option value="90d">Last 90 days</Option>
          </Select>

          <Tooltip title="Refresh Data">
            <Button
              icon={<ReloadOutlined />}
              loading={loading}
              onClick={() => {
                setLoading(true);
                setTimeout(() => setLoading(false), 1000);
              }}
            />
          </Tooltip>

          <Tooltip title="Export Report">
            <Button
              icon={<DownloadOutlined />}
              onClick={onExport}
            />
          </Tooltip>
        </Space>
      </div>

      {/* Key Metrics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={8}>
          {renderMetricCard('views', metrics.totalViews || {}, 'Total Views')}
        </Col>
        <Col xs={24} sm={12} lg={8}>
          {renderMetricCard('users', metrics.uniqueUsers || {}, 'Unique Users')}
        </Col>
        <Col xs={24} sm={12} lg={8}>
          {renderMetricCard('session', metrics.avgSessionTime || {}, 'Avg Session', 's')}
        </Col>
        <Col xs={24} sm={12} lg={8}>
          {renderMetricCard('bounce', metrics.bounceRate || {}, 'Bounce Rate', '%')}
        </Col>
        <Col xs={24} sm={12} lg={8}>
          {renderMetricCard('conversion', metrics.conversionRate || {}, 'Conversion', '%')}
        </Col>
        <Col xs={24} sm={12} lg={8}>
          {renderMetricCard('errors', metrics.errorRate || {}, 'Error Rate', '%')}
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Component Performance */}
        <Col xs={24} lg={12}>
          <Card title="Component Performance" style={{ height: 400 }}>
            <List
              dataSource={componentStats}
              renderItem={(item) => (
                <List.Item>
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                      <Text strong>{item.name}</Text>
                      <Space>
                        <Text type="secondary">Usage: {item.usage}%</Text>
                        <Text type={item.errors > 5 ? 'danger' : 'secondary'}>
                          Errors: {item.errors}
                        </Text>
                      </Space>
                    </div>
                    <Progress
                      percent={item.performance}
                      size="small"
                      status={item.performance > 90 ? 'success' : item.performance > 70 ? 'normal' : 'exception'}
                    />
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Recent Activity */}
        <Col xs={24} lg={12}>
          <Card title="Recent Activity" style={{ height: 400 }}>
            <div style={{ maxHeight: 320, overflowY: 'auto' }}>
              {activities.map((activity) => (
                <ActivityItem key={activity.id}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    {getActivityIcon(activity.type)}
                    <div>
                      <div style={{ fontSize: '14px' }}>{activity.description}</div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        by {activity.user} • {formatTime(activity.timestamp)}
                      </Text>
                    </div>
                  </div>
                </ActivityItem>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </AnalyticsContainer>
  );
};

export default AnalyticsPanel;
