import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import SharedEditor from '../../../components/SharedEditor';

// Mock the services
jest.mock('../../../services/SharedEditingService', () => ({
  initialized: false,
  init: jest.fn(),
  joinDocument: jest.fn(() => ({ content: '', collaborators: {} })),
  leaveDocument: jest.fn(),
  getDocument: jest.fn(() => ({ content: '', collaborators: {} })),
  updateContent: jest.fn(),
  updateCursor: jest.fn(),
  updateSelection: jest.fn(),
  sendDocumentMessage: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
}));

jest.mock('../../../services/UserPresenceService', () => ({
  initialized: false,
  init: jest.fn(),
  getUser: jest.fn(() => ({ username: 'testuser', status: 'online' })),
  isUserTyping: jest.fn(() => false),
  on: jest.fn(),
  off: jest.fn(),
}));

// Mock ReactQuill
jest.mock('react-quill', () => {
  const mockReact = require('react');
  return mockReact.forwardRef((props, ref) => {
    const { value, onChange, readOnly, modules } = props;

    return mockReact.createElement('div', { 'data-testid': 'quill-editor' }, [
      mockReact.createElement('div', { 'data-testid': 'quill-toolbar', key: 'toolbar' }, [
        mockReact.createElement('button', { 'data-testid': 'bold-button', key: 'bold' }, 'Bold'),
        mockReact.createElement('button', { 'data-testid': 'italic-button', key: 'italic' }, 'Italic'),
        mockReact.createElement('button', { 'data-testid': 'underline-button', key: 'underline' }, 'Underline'),
        mockReact.createElement('button', { 'data-testid': 'list-button', key: 'list' }, 'List')
      ]),
      mockReact.createElement('textarea', {
        ref: ref,
        'data-testid': 'quill-content',
        value: value || '',
        onChange: (e) => onChange && onChange(e.target.value),
        readOnly: readOnly,
        placeholder: 'Enter text...',
        key: 'content'
      })
    ]);
  });
});

describe('Quill.js Integration Tests', () => {
  const defaultProps = {
    documentId: 'test-doc-1',
    userId: 'user-1',
    username: 'testuser',
    title: 'Test Document',
    height: 300,
    readOnly: false,
    onContentChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Installation and Import Verification', () => {
    test('should render SharedEditor component with Quill editor', () => {
      render(<SharedEditor {...defaultProps} />);

      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();
      expect(screen.getByTestId('quill-content')).toBeInTheDocument();
      expect(screen.getByTestId('quill-toolbar')).toBeInTheDocument();
    });

    test('should import Quill CSS styles', () => {
      // Check if Quill CSS is imported by looking for the import statement
      const SharedEditorModule = require('../../../components/SharedEditor');
      expect(SharedEditorModule).toBeDefined();
    });
  });

  describe('UI Integration', () => {
    test('should display Quill editor with proper styling', () => {
      render(<SharedEditor {...defaultProps} />);

      const editor = screen.getByTestId('quill-editor');
      expect(editor).toBeInTheDocument();

      // Check toolbar is present
      expect(screen.getByTestId('quill-toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('bold-button')).toBeInTheDocument();
      expect(screen.getByTestId('italic-button')).toBeInTheDocument();
    });

    test('should integrate well with Ant Design Card component', () => {
      render(<SharedEditor {...defaultProps} />);

      // Check that the editor is wrapped in a Card
      expect(screen.getByText('Test Document')).toBeInTheDocument();
      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();
    });

    test('should show read-only indicator when readOnly is true', () => {
      render(<SharedEditor {...defaultProps} readOnly={true} />);

      expect(screen.getByText('Read-only mode')).toBeInTheDocument();
      expect(screen.getByTestId('quill-content')).toHaveAttribute('readOnly');
    });
  });

  describe('Functionality Testing', () => {
    test('should handle text input and formatting', async () => {
      const onContentChange = jest.fn();

      render(<SharedEditor {...defaultProps} onContentChange={onContentChange} />);

      const editor = screen.getByTestId('quill-content');

      // Use fireEvent for better JSDOM compatibility
      fireEvent.change(editor, { target: { value: 'Hello World' } });

      expect(onContentChange).toHaveBeenCalled();
    });

    test('should handle toolbar interactions', async () => {
      render(<SharedEditor {...defaultProps} />);

      // Click toolbar buttons
      const boldButton = screen.getByTestId('bold-button');
      const italicButton = screen.getByTestId('italic-button');

      // Use fireEvent for better JSDOM compatibility
      fireEvent.click(boldButton);
      fireEvent.click(italicButton);

      // Verify buttons are clickable
      expect(boldButton).toBeInTheDocument();
      expect(italicButton).toBeInTheDocument();
    });

    test('should save and load content properly', () => {
      const { rerender } = render(<SharedEditor {...defaultProps} />);

      // Simulate content change
      const editor = screen.getByTestId('quill-content');
      fireEvent.change(editor, { target: { value: 'Test content' } });

      // Rerender with new content
      rerender(<SharedEditor {...defaultProps} />);

      // Content should persist
      expect(editor).toBeInTheDocument();
    });

    test('should handle onChange events correctly', async () => {
      const onContentChange = jest.fn();

      render(<SharedEditor {...defaultProps} onContentChange={onContentChange} />);

      const editor = screen.getByTestId('quill-content');

      // Use fireEvent for better JSDOM compatibility
      fireEvent.change(editor, { target: { value: 'New content' } });

      expect(onContentChange).toHaveBeenCalledWith('New content');
    });

    test('should handle onBlur events', () => {
      render(<SharedEditor {...defaultProps} />);

      const editor = screen.getByTestId('quill-content');
      fireEvent.blur(editor);

      // Should not throw any errors
      expect(editor).toBeInTheDocument();
    });
  });

  describe('Styling Consistency', () => {
    test('should not conflict with Ant Design styles', () => {
      render(<SharedEditor {...defaultProps} />);

      // Check that Ant Design components render properly
      expect(screen.getByText('Test Document')).toBeInTheDocument();

      // Check that Quill editor is present
      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();
    });

    test('should apply custom height properly', () => {
      render(<SharedEditor {...defaultProps} height={400} />);

      const editor = screen.getByTestId('quill-editor');
      expect(editor).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should handle missing documentId gracefully', () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => { });

      render(<SharedEditor {...defaultProps} documentId={null} />);

      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();

      consoleError.mockRestore();
    });

    test('should handle service initialization errors', () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => { });

      render(<SharedEditor {...defaultProps} />);

      // Should render without crashing
      expect(screen.getByTestId('quill-editor')).toBeInTheDocument();

      consoleError.mockRestore();
    });
  });

  describe('Performance', () => {
    test('should not cause performance issues with large content', async () => {
      const largeContent = 'A'.repeat(10000);
      const onContentChange = jest.fn();

      render(<SharedEditor {...defaultProps} onContentChange={onContentChange} />);

      const editor = screen.getByTestId('quill-content');

      // Simulate large content input
      fireEvent.change(editor, { target: { value: largeContent } });

      expect(onContentChange).toHaveBeenCalledWith(largeContent);
    });

    test('should handle rapid content changes efficiently', async () => {
      const onContentChange = jest.fn();

      render(<SharedEditor {...defaultProps} onContentChange={onContentChange} />);

      const editor = screen.getByTestId('quill-content');

      // Rapid typing simulation using fireEvent
      fireEvent.change(editor, { target: { value: 'Quick typing test' } });

      // Should handle all changes
      expect(onContentChange).toHaveBeenCalled();
    });
  });
});
