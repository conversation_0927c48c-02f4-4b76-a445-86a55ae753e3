<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Diagnostics - Popup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 16px;
            background: #f8fafc;
            color: #1f2937;
            font-size: 14px;
        }
        .header {
            background: #3b82f6;
            color: white;
            padding: 12px 16px;
            margin: -16px -16px 16px -16px;
            font-weight: 600;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 4px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.info { background: #dbeafe; color: #1e40af; }
        .section {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e5e7eb;
        }
        .section h3 {
            margin: 0 0 8px 0;
            color: #374151;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 12px;
        }
        button:hover { background: #2563eb; }
        .timestamp {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        🔍 React Diagnostics - Live Monitor
    </div>

    <div class="timestamp" id="timestamp"></div>

    <div class="section">
        <h3>⚛️ React Status</h3>
        <div id="react-status"></div>
    </div>

    <div class="section">
        <h3>📊 App State</h3>
        <div id="app-status"></div>
    </div>

    <div class="section">
        <h3>🏗️ DOM Structure</h3>
        <div id="dom-status"></div>
    </div>

    <div class="section">
        <h3>🎨 CSS & Resources</h3>
        <div id="css-status"></div>
    </div>

    <div>
        <button onclick="runDiagnostics()">🔄 Refresh</button>
        <button onclick="toggleAutoRefresh()">⏱️ Auto Refresh</button>
        <button onclick="window.close()">❌ Close</button>
    </div>

    <script>
        let autoRefreshInterval;
        let isAutoRefresh = false;

        function updateTimestamp() {
            document.getElementById('timestamp').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
        }

        function runDiagnostics() {
            updateTimestamp();
            
            // Get main app context (opener window)
            const mainApp = window.opener;
            
            if (!mainApp) {
                document.body.innerHTML = '<div class="status error">❌ No connection to main application</div>';
                return;
            }

            try {
                // Test React globals in main app
                const reactStatus = {
                    reactAvailable: typeof mainApp.React !== 'undefined',
                    reactDOMAvailable: typeof mainApp.ReactDOM !== 'undefined',
                    reactVersion: mainApp.React?.version || null,
                    globalsExposed: mainApp.__REACT_GLOBALS_EXPOSED__ || false
                };

                let reactHtml = '';
                reactHtml += `<div class="status ${reactStatus.reactAvailable ? 'success' : 'error'}">
                    ${reactStatus.reactAvailable ? '✅' : '❌'} React: ${reactStatus.reactAvailable ? reactStatus.reactVersion : 'Not Available'}
                </div>`;
                reactHtml += `<div class="status ${reactStatus.reactDOMAvailable ? 'success' : 'error'}">
                    ${reactStatus.reactDOMAvailable ? '✅' : '❌'} ReactDOM: ${reactStatus.reactDOMAvailable ? 'Available' : 'Not Available'}
                </div>`;
                reactHtml += `<div class="status ${reactStatus.globalsExposed ? 'success' : 'error'}">
                    ${reactStatus.globalsExposed ? '✅' : '❌'} Globals Exposed: ${reactStatus.globalsExposed}
                </div>`;

                document.getElementById('react-status').innerHTML = reactHtml;

                // Test app state in main app
                const appStatus = {
                    appLoaded: mainApp.__APP_LOADED__,
                    appLoading: mainApp.__APP_LOADING__,
                    reactLoaded: mainApp.__REACT_LOADED__
                };

                let appHtml = '';
                appHtml += `<div class="status ${appStatus.appLoaded ? 'success' : 'error'}">
                    ${appStatus.appLoaded ? '✅' : '❌'} App Loaded: ${appStatus.appLoaded}
                </div>`;
                appHtml += `<div class="status ${!appStatus.appLoading ? 'success' : 'error'}">
                    ${!appStatus.appLoading ? '✅' : '❌'} App Loading: ${appStatus.appLoading} (should be false)
                </div>`;
                appHtml += `<div class="status ${appStatus.reactLoaded ? 'success' : 'error'}">
                    ${appStatus.reactLoaded ? '✅' : '❌'} React Loaded Flag: ${appStatus.reactLoaded}
                </div>`;

                document.getElementById('app-status').innerHTML = appHtml;

                // Test DOM structure in main app
                const rootElement = mainApp.document.getElementById('root');
                const domStatus = {
                    rootExists: !!rootElement,
                    rootHasContent: rootElement ? rootElement.innerHTML.trim().length > 0 : false,
                    rootContentLength: rootElement ? rootElement.innerHTML.length : 0
                };

                let domHtml = '';
                domHtml += `<div class="status ${domStatus.rootExists ? 'success' : 'error'}">
                    ${domStatus.rootExists ? '✅' : '❌'} Root Element: ${domStatus.rootExists ? 'Found' : 'Not Found'}
                </div>`;
                domHtml += `<div class="status ${domStatus.rootHasContent ? 'success' : 'error'}">
                    ${domStatus.rootHasContent ? '✅' : '❌'} Root Content: ${domStatus.rootHasContent ? `${domStatus.rootContentLength} chars` : 'Empty'}
                </div>`;

                if (rootElement && domStatus.rootHasContent) {
                    const preview = rootElement.innerHTML.substring(0, 100).replace(/</g, '&lt;').replace(/>/g, '&gt;');
                    domHtml += `<div class="status info">📋 Content Preview: ${preview}...</div>`;
                }

                document.getElementById('dom-status').innerHTML = domHtml;

                // Test CSS and resources in main app
                const cssLinks = mainApp.document.querySelectorAll('link[rel="stylesheet"]');
                const cssStatus = {
                    cssLinksCount: cssLinks.length,
                    cssFiles: Array.from(cssLinks).map(link => link.href.split('/').pop())
                };

                let cssHtml = '';
                cssHtml += `<div class="status ${cssStatus.cssLinksCount > 0 ? 'success' : 'error'}">
                    ${cssStatus.cssLinksCount > 0 ? '✅' : '❌'} CSS Links: ${cssStatus.cssLinksCount}
                </div>`;
                if (cssStatus.cssFiles.length > 0) {
                    cssHtml += `<div class="status info">📄 Files: ${cssStatus.cssFiles.join(', ')}</div>`;
                }

                document.getElementById('css-status').innerHTML = cssHtml;

                // Overall status
                const allChecks = [
                    reactStatus.reactAvailable,
                    reactStatus.reactDOMAvailable,
                    appStatus.appLoaded,
                    !appStatus.appLoading,
                    domStatus.rootExists,
                    domStatus.rootHasContent,
                    cssStatus.cssLinksCount > 0
                ];
                const passedChecks = allChecks.filter(check => check).length;
                const totalChecks = allChecks.length;

                document.title = `React Diagnostics - ${passedChecks}/${totalChecks} ✅`;

            } catch (error) {
                document.getElementById('react-status').innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
            }
        }

        function toggleAutoRefresh() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                document.querySelector('button[onclick="toggleAutoRefresh()"]').textContent = '⏱️ Auto Refresh';
            } else {
                autoRefreshInterval = setInterval(runDiagnostics, 2000);
                isAutoRefresh = true;
                document.querySelector('button[onclick="toggleAutoRefresh()"]').textContent = '⏹️ Stop Auto';
            }
        }

        // Run initial diagnostics
        window.addEventListener('load', () => {
            runDiagnostics();
            // Start auto-refresh by default
            toggleAutoRefresh();
        });

        // Handle window closing
        window.addEventListener('beforeunload', () => {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
