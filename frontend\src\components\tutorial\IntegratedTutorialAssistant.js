/**
 * Integrated Tutorial Assistant Component
 * 
 * Enhanced tutorial system specifically designed for the App Builder with
 * interactive overlays, context-aware help, and seamless integration.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Progress,
  Badge,
  Tooltip,
  Modal,
  Drawer,
  List,
  Tag,
  Avatar,
  Divider,
  Row,
  Col,
  Switch,
  notification,
  FloatButton,
  Steps
} from 'antd';
import {
  BookOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepForwardOutlined,
  StepBackwardOutlined,
  CloseOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  CheckCircleOutlined,
  TrophyOutlined,
  BulbOutlined,
  RocketOutlined,
  EyeOutlined,
  AppstoreOutlined,
  LayoutOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// Styled Components
const TutorialContainer = styled.div`
  position: relative;
`;

const TutorialCard = styled(Card)`
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 380px;
  z-index: 1000;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  border: none;
  
  .ant-card-head {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border: none;
  }
  
  .ant-card-head-title {
    color: white;
    font-weight: 600;
  }
  
  .ant-card-body {
    padding: 20px;
  }
`;

const ProgressContainer = styled.div`
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
`;

const StepIndicator = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const ContextualHint = styled.div`
  position: absolute;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  z-index: 999;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  animation: fadeInBounce 0.5s ease-out;
  
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -6px;
    border-width: 6px;
    border-style: solid;
    border-color: #1890ff transparent transparent transparent;
  }
  
  @keyframes fadeInBounce {
    0% { opacity: 0; transform: translateY(-10px) scale(0.8); }
    100% { opacity: 1; transform: translateY(0) scale(1); }
  }
`;

const TutorialOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 998;
  pointer-events: ${props => props.allowInteraction ? 'none' : 'auto'};
  backdrop-filter: blur(2px);
`;

const Spotlight = styled.div`
  position: absolute;
  border: 3px solid #1890ff;
  border-radius: 12px;
  box-shadow: 
    0 0 0 4px rgba(24, 144, 255, 0.3),
    0 0 0 9999px rgba(0, 0, 0, 0.6);
  pointer-events: none;
  z-index: 999;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { 
      box-shadow: 
        0 0 0 4px rgba(24, 144, 255, 0.3),
        0 0 0 8px rgba(24, 144, 255, 0.1),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
    50% { 
      box-shadow: 
        0 0 0 8px rgba(24, 144, 255, 0.2),
        0 0 0 16px rgba(24, 144, 255, 0.05),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
    100% { 
      box-shadow: 
        0 0 0 4px rgba(24, 144, 255, 0.3),
        0 0 0 8px rgba(24, 144, 255, 0.1),
        0 0 0 9999px rgba(0, 0, 0, 0.6);
    }
  }
`;

// App Builder specific tutorial steps
const APP_BUILDER_TUTORIALS = {
  getting_started: {
    id: 'getting_started',
    title: 'Getting Started with App Builder',
    description: 'Learn the basics of creating applications',
    category: 'beginner',
    estimatedDuration: 5,
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to App Builder!',
        content: 'App Builder lets you create applications visually using drag-and-drop components. Let\'s start with a quick tour of the interface.',
        target: null,
        position: 'center'
      },
      {
        id: 'component-palette',
        title: 'Component Palette',
        content: 'This is where you\'ll find all available components. Drag components from here to the canvas to start building your app.',
        target: '[data-tutorial="component-palette"]',
        position: 'right'
      },
      {
        id: 'canvas-area',
        title: 'Canvas Area',
        content: 'This is your workspace where you can drop and arrange components. You can also select components here to edit their properties.',
        target: '[data-tutorial="canvas-area"]',
        position: 'left'
      },
      {
        id: 'property-editor',
        title: 'Property Editor',
        content: 'When you select a component, you can customize its properties here. Change colors, text, sizes, and more.',
        target: '[data-tutorial="property-editor"]',
        position: 'left'
      },
      {
        id: 'preview',
        title: 'Preview Your App',
        content: 'Click the preview button to see how your app will look to users. You can test interactions and responsiveness.',
        target: '[data-tutorial="preview-button"]',
        position: 'bottom'
      }
    ]
  },
  advanced_features: {
    id: 'advanced_features',
    title: 'Advanced Features',
    description: 'Explore powerful features for complex applications',
    category: 'advanced',
    estimatedDuration: 8,
    steps: [
      {
        id: 'testing-tools',
        title: 'Testing Tools',
        content: 'Use the testing tools to validate your components and ensure they work correctly across different scenarios.',
        target: '[data-tutorial="testing-tools"]',
        position: 'top'
      },
      {
        id: 'data-management',
        title: 'Data Management',
        content: 'Manage your application data with our powerful data binding and state management tools.',
        target: '[data-tutorial="data-management"]',
        position: 'top'
      },
      {
        id: 'performance-monitor',
        title: 'Performance Monitoring',
        content: 'Monitor your application\'s performance and get optimization suggestions to improve user experience.',
        target: '[data-tutorial="performance-monitor"]',
        position: 'top'
      },
      {
        id: 'code-export',
        title: 'Code Export',
        content: 'Export your application to multiple frameworks including React, Vue, Angular, and more with TypeScript support.',
        target: '[data-tutorial="code-export"]',
        position: 'top'
      }
    ]
  }
};

/**
 * IntegratedTutorialAssistant Component
 */
const IntegratedTutorialAssistant = ({
  enableAutoStart = false,
  showContextualHelp = true,
  onTutorialComplete,
  onTutorialSkip,
  features = []
}) => {
  // State
  const [isActive, setIsActive] = useState(false);
  const [currentTutorial, setCurrentTutorial] = useState(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [showTutorialList, setShowTutorialList] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [contextualHints, setContextualHints] = useState([]);
  const [spotlightPosition, setSpotlightPosition] = useState(null);
  const [completedTutorials, setCompletedTutorials] = useState(new Set());
  const [assistantSettings, setAssistantSettings] = useState({
    autoAdvance: false,
    showHints: true,
    playSound: false,
    animationSpeed: 'normal'
  });

  // Get current tutorial step
  const currentStep = useMemo(() => {
    if (!currentTutorial || currentStepIndex >= currentTutorial.steps.length) return null;
    return currentTutorial.steps[currentStepIndex];
  }, [currentTutorial, currentStepIndex]);

  // Calculate progress percentage
  const progressPercentage = useMemo(() => {
    if (!currentTutorial) return 0;
    return Math.round(((currentStepIndex + 1) / currentTutorial.steps.length) * 100);
  }, [currentTutorial, currentStepIndex]);

  // Update spotlight position when step changes
  useEffect(() => {
    if (!currentStep?.target || !isActive) {
      setSpotlightPosition(null);
      return;
    }

    const updateSpotlight = () => {
      const element = document.querySelector(currentStep.target);
      if (element) {
        const rect = element.getBoundingClientRect();
        setSpotlightPosition({
          top: rect.top - 12,
          left: rect.left - 12,
          width: rect.width + 24,
          height: rect.height + 24
        });
      }
    };

    // Delay to allow for DOM updates
    setTimeout(updateSpotlight, 100);
    window.addEventListener('resize', updateSpotlight);
    return () => window.removeEventListener('resize', updateSpotlight);
  }, [currentStep, isActive]);

  // Handle contextual hints
  useEffect(() => {
    if (!showContextualHelp || isActive || !assistantSettings.showHints) {
      setContextualHints([]);
      return;
    }

    const hints = [];
    const elements = document.querySelectorAll('[data-tutorial]');

    elements.forEach(element => {
      const tutorialId = element.getAttribute('data-tutorial');
      const rect = element.getBoundingClientRect();

      if (rect.top >= 0 && rect.bottom <= window.innerHeight && rect.width > 0 && rect.height > 0) {
        hints.push({
          id: tutorialId,
          element,
          position: {
            top: rect.top - 45,
            left: rect.left + rect.width / 2 - 75
          },
          text: getHintText(tutorialId)
        });
      }
    });

    setContextualHints(hints.slice(0, 2)); // Limit to 2 hints to avoid clutter
  }, [showContextualHelp, isActive, assistantSettings.showHints]);

  // Get hint text for tutorial elements
  const getHintText = useCallback((tutorialId) => {
    const hintMap = {
      'component-palette': '🎨 Drag components from here',
      'property-editor': '⚙️ Customize properties here',
      'canvas-area': '🎯 Drop components here',
      'preview-button': '👁️ Preview your app',
      'export-button': '📤 Export your code',
      'testing-tools': '🧪 Test your components',
      'data-management': '📊 Manage your data',
      'performance-monitor': '⚡ Monitor performance',
      'code-export': '💻 Export to frameworks'
    };
    return hintMap[tutorialId] || '💡 Click for help';
  }, []);

  // Handle tutorial start
  const handleStartTutorial = useCallback((tutorialId) => {
    const tutorial = APP_BUILDER_TUTORIALS[tutorialId];
    if (!tutorial) return;

    setCurrentTutorial(tutorial);
    setCurrentStepIndex(0);
    setIsActive(true);
    setIsPaused(false);
    setShowTutorialList(false);

    notification.success({
      message: 'Tutorial Started',
      description: `Starting "${tutorial.title}" tutorial`,
      duration: 3,
      icon: <BookOutlined style={{ color: '#1890ff' }} />
    });
  }, []);

  // Handle next step
  const handleNextStep = useCallback(() => {
    if (!currentTutorial) return;

    if (currentStepIndex < currentTutorial.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      // Tutorial completed
      handleTutorialComplete();
    }
  }, [currentTutorial, currentStepIndex]);

  // Handle previous step
  const handlePreviousStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  }, [currentStepIndex]);

  // Handle tutorial completion
  const handleTutorialComplete = useCallback(() => {
    if (!currentTutorial) return;

    setCompletedTutorials(prev => new Set([...prev, currentTutorial.id]));
    setIsActive(false);
    setCurrentTutorial(null);
    setCurrentStepIndex(0);

    if (onTutorialComplete) {
      onTutorialComplete(currentTutorial);
    }

    notification.success({
      message: 'Tutorial Completed! 🎉',
      description: 'Great job! You\'ve mastered this feature.',
      duration: 5,
      icon: <TrophyOutlined style={{ color: '#faad14' }} />
    });
  }, [currentTutorial, onTutorialComplete]);

  // Handle tutorial skip
  const handleTutorialSkip = useCallback(() => {
    if (!currentTutorial) return;

    setIsActive(false);
    setCurrentTutorial(null);
    setCurrentStepIndex(0);

    if (onTutorialSkip) {
      onTutorialSkip(currentTutorial);
    }

    notification.info({
      message: 'Tutorial Skipped',
      description: 'You can restart it anytime from the tutorial list.',
      duration: 3
    });
  }, [currentTutorial, onTutorialSkip]);

  // Handle pause/resume
  const handlePauseResume = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Auto-start tutorial for new users
  useEffect(() => {
    if (enableAutoStart && !isActive && completedTutorials.size === 0) {
      setTimeout(() => {
        handleStartTutorial('getting_started');
      }, 2000);
    }
  }, [enableAutoStart, isActive, completedTutorials.size, handleStartTutorial]);

  return (
    <TutorialContainer>
      {/* Tutorial Overlay */}
      {isActive && !isPaused && (
        <TutorialOverlay allowInteraction={currentStep?.type === 'interactive'}>
          {spotlightPosition && (
            <Spotlight
              style={{
                top: spotlightPosition.top,
                left: spotlightPosition.left,
                width: spotlightPosition.width,
                height: spotlightPosition.height
              }}
            />
          )}
        </TutorialOverlay>
      )}

      {/* Contextual Hints */}
      {contextualHints.map(hint => (
        <ContextualHint
          key={hint.id}
          style={{
            top: hint.position.top,
            left: hint.position.left
          }}
        >
          {hint.text}
        </ContextualHint>
      ))}

      {/* Active Tutorial Card */}
      {isActive && currentStep && (
        <TutorialCard
          title={
            <Space>
              <BookOutlined />
              {currentTutorial.title}
              <Badge
                count={`${currentStepIndex + 1}/${currentTutorial.steps.length}`}
                style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
              />
            </Space>
          }
          extra={
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleTutorialSkip}
              style={{ color: 'white' }}
              size="small"
            />
          }
        >
          <div>
            <Title level={5} style={{ marginBottom: 8, color: '#1890ff' }}>
              {currentStep.title}
            </Title>
            <Paragraph style={{ marginBottom: 16, color: '#666' }}>
              {currentStep.content}
            </Paragraph>

            <ProgressContainer>
              <StepIndicator>
                <Text type="secondary" style={{ fontSize: 12 }}>Progress</Text>
                <Text strong style={{ color: '#1890ff' }}>{progressPercentage}%</Text>
              </StepIndicator>
              <Progress
                percent={progressPercentage}
                size="small"
                strokeColor="#1890ff"
                trailColor="#f0f0f0"
                showInfo={false}
              />
            </ProgressContainer>

            <Row justify="space-between" align="middle">
              <Col>
                <Space size="small">
                  <Button
                    icon={<StepBackwardOutlined />}
                    onClick={handlePreviousStep}
                    disabled={currentStepIndex === 0}
                    size="small"
                  >
                    Previous
                  </Button>
                  <Button
                    icon={isPaused ? <PlayCircleOutlined /> : <PauseCircleOutlined />}
                    onClick={handlePauseResume}
                    size="small"
                  >
                    {isPaused ? 'Resume' : 'Pause'}
                  </Button>
                </Space>
              </Col>
              <Col>
                <Space size="small">
                  <Button
                    icon={<StepForwardOutlined />}
                    onClick={handleTutorialSkip}
                    size="small"
                  >
                    Skip
                  </Button>
                  {currentStepIndex === currentTutorial.steps.length - 1 ? (
                    <Button
                      type="primary"
                      icon={<CheckCircleOutlined />}
                      onClick={handleTutorialComplete}
                      size="small"
                    >
                      Complete
                    </Button>
                  ) : (
                    <Button
                      type="primary"
                      icon={<StepForwardOutlined />}
                      onClick={handleNextStep}
                      size="small"
                    >
                      Next
                    </Button>
                  )}
                </Space>
              </Col>
            </Row>
          </div>
        </TutorialCard>
      )}

      {/* Floating Tutorial Button */}
      {!isActive && (
        <FloatButton.Group
          trigger="click"
          type="primary"
          style={{ right: 24, bottom: 24 }}
          icon={<QuestionCircleOutlined />}
          tooltip="Tutorial Assistant"
        >
          <FloatButton
            icon={<BookOutlined />}
            tooltip="Browse Tutorials"
            onClick={() => setShowTutorialList(true)}
          />
          <FloatButton
            icon={<BulbOutlined />}
            tooltip="Toggle Hints"
            onClick={() => setAssistantSettings(prev => ({ ...prev, showHints: !prev.showHints }))}
          />
          <FloatButton
            icon={<SettingOutlined />}
            tooltip="Tutorial Settings"
            onClick={() => setShowSettings(true)}
          />
        </FloatButton.Group>
      )}

      {/* Tutorial List Modal */}
      <Modal
        title="Available Tutorials"
        open={showTutorialList}
        onCancel={() => setShowTutorialList(false)}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            Choose a tutorial to learn about App Builder features. Completed tutorials are marked with a trophy.
          </Text>
        </div>

        <List
          dataSource={Object.values(APP_BUILDER_TUTORIALS)}
          renderItem={tutorial => (
            <List.Item
              actions={[
                completedTutorials.has(tutorial.id) ? (
                  <Tag color="gold" icon={<TrophyOutlined />}>Completed</Tag>
                ) : (
                  <Button
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleStartTutorial(tutorial.id)}
                  >
                    Start
                  </Button>
                )
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={tutorial.category === 'beginner' ? <BookOutlined /> : <RocketOutlined />}
                    style={{
                      backgroundColor: tutorial.category === 'beginner' ? '#52c41a' : '#1890ff'
                    }}
                  />
                }
                title={
                  <Space>
                    {tutorial.title}
                    <Tag color={tutorial.category === 'beginner' ? 'green' : 'blue'}>
                      {tutorial.category}
                    </Tag>
                  </Space>
                }
                description={
                  <div>
                    <Paragraph style={{ margin: 0, marginBottom: 4 }}>
                      {tutorial.description}
                    </Paragraph>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      ⏱️ {tutorial.estimatedDuration} minutes • {tutorial.steps.length} steps
                    </Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Modal>

      {/* Settings Modal */}
      <Modal
        title="Tutorial Settings"
        open={showSettings}
        onCancel={() => setShowSettings(false)}
        footer={[
          <Button key="close" onClick={() => setShowSettings(false)}>
            Close
          </Button>
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Display Options</Text>
            <div style={{ marginTop: 8 }}>
              <Row gutter={[16, 8]}>
                <Col span={24}>
                  <Switch
                    checked={assistantSettings.showHints}
                    onChange={(checked) => setAssistantSettings(prev => ({ ...prev, showHints: checked }))}
                  />
                  <Text style={{ marginLeft: 8 }}>Show Contextual Hints</Text>
                </Col>
                <Col span={24}>
                  <Switch
                    checked={assistantSettings.autoAdvance}
                    onChange={(checked) => setAssistantSettings(prev => ({ ...prev, autoAdvance: checked }))}
                  />
                  <Text style={{ marginLeft: 8 }}>Auto-advance Steps</Text>
                </Col>
                <Col span={24}>
                  <Switch
                    checked={assistantSettings.playSound}
                    onChange={(checked) => setAssistantSettings(prev => ({ ...prev, playSound: checked }))}
                  />
                  <Text style={{ marginLeft: 8 }}>Play Sound Effects</Text>
                </Col>
              </Row>
            </div>
          </div>

          <Divider />

          <div>
            <Text strong>Tutorial Progress</Text>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                Completed: {completedTutorials.size} / {Object.keys(APP_BUILDER_TUTORIALS).length} tutorials
              </Text>
              <Progress
                percent={Math.round((completedTutorials.size / Object.keys(APP_BUILDER_TUTORIALS).length) * 100)}
                size="small"
                style={{ marginTop: 8 }}
              />
            </div>
          </div>

          <Divider />

          <div>
            <Text strong>Quick Actions</Text>
            <div style={{ marginTop: 8 }}>
              <Space wrap>
                <Button
                  icon={<BookOutlined />}
                  onClick={() => {
                    setShowSettings(false);
                    handleStartTutorial('getting_started');
                  }}
                >
                  Restart Getting Started
                </Button>
                <Button
                  icon={<RocketOutlined />}
                  onClick={() => {
                    setShowSettings(false);
                    handleStartTutorial('advanced_features');
                  }}
                >
                  Advanced Features
                </Button>
                <Button
                  icon={<CloseOutlined />}
                  onClick={() => {
                    setCompletedTutorials(new Set());
                    notification.success({
                      message: 'Progress Reset',
                      description: 'All tutorial progress has been reset.',
                      duration: 3
                    });
                  }}
                >
                  Reset Progress
                </Button>
              </Space>
            </div>
          </div>
        </Space>
      </Modal>
    </TutorialContainer>
  );
};

export default IntegratedTutorialAssistant;
