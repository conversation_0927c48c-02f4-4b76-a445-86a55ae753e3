/**
 * Test script to verify in-app diagnostic panel works correctly
 */

const puppeteer = require('puppeteer');

async function testInAppDiagnostics() {
  console.log('🔍 Testing In-App Diagnostic Panel...');
  console.log('=====================================');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false, // Show browser for visual verification
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture console messages
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`[${msg.type()}] ${msg.text()}`);
    });

    // Navigate to main app
    console.log('📱 Loading main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for app to load
    await page.waitForFunction(
      () => window.__APP_LOADED__ === true,
      { timeout: 10000 }
    );

    // Check if diagnostic widget exists
    console.log('🔍 Checking for diagnostic widget...');
    const widgetExists = await page.evaluate(() => {
      return !!document.getElementById('react-diagnostic-widget');
    });

    console.log(`Diagnostic Widget Present: ${widgetExists ? '✅' : '❌'}`);

    if (widgetExists) {
      // Test widget status
      const widgetInfo = await page.evaluate(() => {
        const widget = document.getElementById('react-diagnostic-widget');
        return {
          text: widget.textContent,
          title: widget.title,
          backgroundColor: window.getComputedStyle(widget).backgroundColor,
          visible: widget.offsetWidth > 0 && widget.offsetHeight > 0
        };
      });

      console.log(`Widget Text: ${widgetInfo.text}`);
      console.log(`Widget Title: ${widgetInfo.title}`);
      console.log(`Widget Visible: ${widgetInfo.visible ? '✅' : '❌'}`);
      console.log(`Widget Color: ${widgetInfo.backgroundColor} (green=success, red=issues)`);

      // Click the widget to open diagnostic panel
      console.log('🖱️ Clicking diagnostic widget to open panel...');
      await page.click('#react-diagnostic-widget');
      
      // Wait for panel to appear
      await page.waitForTimeout(1000);

      // Check if diagnostic panel opened
      const panelExists = await page.evaluate(() => {
        return !!document.getElementById('diagnostic-panel');
      });

      console.log(`Diagnostic Panel Opened: ${panelExists ? '✅' : '❌'}`);

      if (panelExists) {
        // Get panel content
        const panelContent = await page.evaluate(() => {
          const panel = document.getElementById('diagnostic-panel');
          return {
            visible: panel.offsetWidth > 0 && panel.offsetHeight > 0,
            content: panel.textContent,
            hasReactStatus: panel.textContent.includes('React Status'),
            hasAppState: panel.textContent.includes('App State'),
            hasDOMStructure: panel.textContent.includes('DOM Structure'),
            hasCSSResources: panel.textContent.includes('CSS & Resources'),
            hasSuccessIndicators: panel.textContent.includes('✅'),
            hasErrorIndicators: panel.textContent.includes('❌')
          };
        });

        console.log('\n📊 Diagnostic Panel Analysis:');
        console.log(`  Panel Visible: ${panelContent.visible ? '✅' : '❌'}`);
        console.log(`  Has React Status: ${panelContent.hasReactStatus ? '✅' : '❌'}`);
        console.log(`  Has App State: ${panelContent.hasAppState ? '✅' : '❌'}`);
        console.log(`  Has DOM Structure: ${panelContent.hasDOMStructure ? '✅' : '❌'}`);
        console.log(`  Has CSS Resources: ${panelContent.hasCSSResources ? '✅' : '❌'}`);
        console.log(`  Has Success Indicators: ${panelContent.hasSuccessIndicators ? '✅' : '❌'}`);
        console.log(`  Has Error Indicators: ${panelContent.hasErrorIndicators ? '✅' : '❌'}`);

        // Extract specific diagnostic results
        const diagnosticResults = await page.evaluate(() => {
          const panel = document.getElementById('diagnostic-panel');
          const content = panel.textContent;
          
          return {
            reactAvailable: content.includes('React: 18.') || content.includes('React: Available'),
            reactDOMAvailable: content.includes('ReactDOM: Available'),
            appLoaded: content.includes('App Loaded: true'),
            rootExists: content.includes('Root Element: Found'),
            rootHasContent: content.includes('Root Content:') && !content.includes('Root Content: Empty'),
            cssLinks: content.includes('CSS Links:') && !content.includes('CSS Links: 0')
          };
        });

        console.log('\n🧪 Specific Diagnostic Results:');
        console.log(`  React Available: ${diagnosticResults.reactAvailable ? '✅' : '❌'}`);
        console.log(`  ReactDOM Available: ${diagnosticResults.reactDOMAvailable ? '✅' : '❌'}`);
        console.log(`  App Loaded: ${diagnosticResults.appLoaded ? '✅' : '❌'}`);
        console.log(`  Root Element Found: ${diagnosticResults.rootExists ? '✅' : '❌'}`);
        console.log(`  Root Has Content: ${diagnosticResults.rootHasContent ? '✅' : '❌'}`);
        console.log(`  CSS Links Found: ${diagnosticResults.cssLinks ? '✅' : '❌'}`);

        // Test closing the panel
        console.log('\n🖱️ Testing panel close functionality...');
        await page.click('#diagnostic-panel button'); // Close button
        await page.waitForTimeout(500);

        const panelClosed = await page.evaluate(() => {
          return !document.getElementById('diagnostic-panel');
        });

        console.log(`Panel Closed Successfully: ${panelClosed ? '✅' : '❌'}`);

        // Overall assessment
        console.log('\n📊 Overall Assessment:');
        const allChecks = [
          diagnosticResults.reactAvailable,
          diagnosticResults.reactDOMAvailable,
          diagnosticResults.appLoaded,
          diagnosticResults.rootExists,
          diagnosticResults.rootHasContent,
          diagnosticResults.cssLinks
        ];
        const passedChecks = allChecks.filter(check => check).length;
        const totalChecks = allChecks.length;

        console.log(`Diagnostic Checks: ${passedChecks}/${totalChecks} passed`);
        
        if (passedChecks === totalChecks) {
          console.log('🎉 ALL DIAGNOSTIC CHECKS PASSED!');
          console.log('✅ React globals are properly detected');
          console.log('✅ App state is correctly tracked');
          console.log('✅ Root element is found and has content');
          console.log('✅ CSS resources are loaded');
          console.log('✅ In-app diagnostic panel is fully functional');
        } else {
          console.log('⚠️ Some diagnostic issues found:');
          if (!diagnosticResults.reactAvailable) console.log('   ❌ React not detected');
          if (!diagnosticResults.reactDOMAvailable) console.log('   ❌ ReactDOM not detected');
          if (!diagnosticResults.appLoaded) console.log('   ❌ App not marked as loaded');
          if (!diagnosticResults.rootExists) console.log('   ❌ Root element not found');
          if (!diagnosticResults.rootHasContent) console.log('   ❌ Root element has no content');
          if (!diagnosticResults.cssLinks) console.log('   ❌ CSS links not found');
        }
      }
    }

    // Check console output
    const relevantMessages = consoleMessages.filter(msg => 
      msg.includes('React') || msg.includes('Diagnostic') || msg.includes('✅') || msg.includes('❌')
    );
    
    if (relevantMessages.length > 0) {
      console.log('\n📝 Relevant Console Messages:');
      relevantMessages.slice(0, 5).forEach(msg => console.log(`   ${msg}`));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testInAppDiagnostics().catch(console.error);
