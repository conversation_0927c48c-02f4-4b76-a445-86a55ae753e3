import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Space, Typography, Slider, Switch, Tooltip, Badge } from 'antd';
import {
  MobileOutlined,
  TabletOutlined,
  DesktopOutlined,
  ExpandOutlined,
  CompressOutlined,
  EyeOutlined,
  BugOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Text, Title } = Typography;

const ResponsiveContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
`;

const ControlPanel = styled.div`
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
`;

const PreviewArea = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;
`;

const ViewportFrame = styled.div`
  background: white;
  border: 2px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  ${props => props.breakpoint === 'mobile' && `
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  `}
  
  ${props => props.breakpoint === 'tablet' && `
    border-color: #52c41a;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
  `}
  
  ${props => props.breakpoint === 'desktop' && `
    border-color: #722ed1;
    box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.2);
  `}
`;

const ViewportContent = styled.div`
  width: ${props => props.width}px;
  height: ${props => props.height}px;
  overflow: auto;
  position: relative;
`;

const BreakpointIndicator = styled.div`
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: ${props => {
    switch(props.breakpoint) {
      case 'mobile': return '#1890ff';
      case 'tablet': return '#52c41a';
      case 'desktop': return '#722ed1';
      default: return '#666';
    }
  }};
`;

const RulerOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 1px;
    height: 100%;
    background: rgba(255, 0, 0, 0.5);
    transform: translateX(-50%);
  }
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
    background: rgba(255, 0, 0, 0.5);
    transform: translateY(-50%);
  }
`;

// Responsive breakpoints
const BREAKPOINTS = {
  mobile: { min: 320, max: 767, default: 375 },
  tablet: { min: 768, max: 1023, default: 768 },
  desktop: { min: 1024, max: 1920, default: 1200 }
};

const ResponsivePreview = ({
  children,
  onBreakpointChange = () => {},
  onSizeChange = () => {},
  showControls = true,
  defaultBreakpoint = 'desktop',
  enableRuler = false
}) => {
  const [currentBreakpoint, setCurrentBreakpoint] = useState(defaultBreakpoint);
  const [customWidth, setCustomWidth] = useState(BREAKPOINTS[defaultBreakpoint].default);
  const [customHeight, setCustomHeight] = useState(600);
  const [showRuler, setShowRuler] = useState(enableRuler);
  const [autoHeight, setAutoHeight] = useState(true);
  const [showBreakpointInfo, setShowBreakpointInfo] = useState(true);
  const containerRef = useRef(null);

  // Get current breakpoint based on width
  const getBreakpointFromWidth = (width) => {
    if (width <= BREAKPOINTS.mobile.max) return 'mobile';
    if (width <= BREAKPOINTS.tablet.max) return 'tablet';
    return 'desktop';
  };

  // Handle breakpoint change
  const handleBreakpointChange = (breakpoint) => {
    setCurrentBreakpoint(breakpoint);
    const newWidth = BREAKPOINTS[breakpoint].default;
    setCustomWidth(newWidth);
    onBreakpointChange(breakpoint, newWidth);
  };

  // Handle width change
  const handleWidthChange = (width) => {
    setCustomWidth(width);
    const detectedBreakpoint = getBreakpointFromWidth(width);
    if (detectedBreakpoint !== currentBreakpoint) {
      setCurrentBreakpoint(detectedBreakpoint);
    }
    onSizeChange(width, customHeight);
  };

  // Handle height change
  const handleHeightChange = (height) => {
    setCustomHeight(height);
    onSizeChange(customWidth, height);
  };

  // Auto-adjust container size
  useEffect(() => {
    if (autoHeight && containerRef.current) {
      const containerHeight = containerRef.current.clientHeight - 100; // Account for padding
      setCustomHeight(Math.max(400, containerHeight));
    }
  }, [autoHeight]);

  const renderBreakpointButtons = () => (
    <Space>
      <Text strong>Breakpoint:</Text>
      {Object.keys(BREAKPOINTS).map(breakpoint => {
        const isActive = currentBreakpoint === breakpoint;
        const Icon = breakpoint === 'mobile' ? MobileOutlined : 
                   breakpoint === 'tablet' ? TabletOutlined : DesktopOutlined;
        
        return (
          <Tooltip key={breakpoint} title={`${breakpoint} (${BREAKPOINTS[breakpoint].min}-${BREAKPOINTS[breakpoint].max}px)`}>
            <Button
              type={isActive ? 'primary' : 'default'}
              icon={<Icon />}
              onClick={() => handleBreakpointChange(breakpoint)}
              size="small"
            >
              {breakpoint.charAt(0).toUpperCase() + breakpoint.slice(1)}
            </Button>
          </Tooltip>
        );
      })}
    </Space>
  );

  const renderSizeControls = () => (
    <Space wrap>
      <Space>
        <Text>Width:</Text>
        <Slider
          min={BREAKPOINTS[currentBreakpoint].min}
          max={BREAKPOINTS[currentBreakpoint].max}
          value={customWidth}
          onChange={handleWidthChange}
          style={{ width: 120 }}
        />
        <Text style={{ minWidth: 50 }}>{customWidth}px</Text>
      </Space>
      
      {!autoHeight && (
        <Space>
          <Text>Height:</Text>
          <Slider
            min={300}
            max={1200}
            value={customHeight}
            onChange={handleHeightChange}
            style={{ width: 120 }}
          />
          <Text style={{ minWidth: 50 }}>{customHeight}px</Text>
        </Space>
      )}
    </Space>
  );

  const renderUtilityControls = () => (
    <Space wrap>
      <Tooltip title="Auto Height">
        <Space>
          <Text>Auto Height:</Text>
          <Switch
            checked={autoHeight}
            onChange={setAutoHeight}
            size="small"
          />
        </Space>
      </Tooltip>
      
      <Tooltip title="Show Ruler">
        <Button
          type={showRuler ? 'primary' : 'default'}
          icon={<EyeOutlined />}
          onClick={() => setShowRuler(!showRuler)}
          size="small"
        />
      </Tooltip>
      
      <Tooltip title="Breakpoint Info">
        <Button
          type={showBreakpointInfo ? 'primary' : 'default'}
          icon={<BugOutlined />}
          onClick={() => setShowBreakpointInfo(!showBreakpointInfo)}
          size="small"
        />
      </Tooltip>
    </Space>
  );

  return (
    <ResponsiveContainer>
      {showControls && (
        <ControlPanel>
          {renderBreakpointButtons()}
          {renderSizeControls()}
          {renderUtilityControls()}
        </ControlPanel>
      )}
      
      <PreviewArea ref={containerRef}>
        <div style={{ position: 'relative' }}>
          {showBreakpointInfo && (
            <BreakpointIndicator breakpoint={currentBreakpoint}>
              <Badge
                color={currentBreakpoint === 'mobile' ? 'blue' : 
                       currentBreakpoint === 'tablet' ? 'green' : 'purple'}
                text={`${currentBreakpoint.toUpperCase()} - ${customWidth} × ${customHeight}px`}
              />
            </BreakpointIndicator>
          )}
          
          <ViewportFrame breakpoint={currentBreakpoint}>
            <ViewportContent
              width={customWidth}
              height={autoHeight ? 'auto' : customHeight}
            >
              {showRuler && <RulerOverlay />}
              {children}
            </ViewportContent>
          </ViewportFrame>
        </div>
      </PreviewArea>
    </ResponsiveContainer>
  );
};

export default ResponsivePreview;
