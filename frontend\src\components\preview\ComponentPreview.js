import React, { useState, useEffect } from 'react';
import { <PERSON>, Tabs, <PERSON>ton, Space, Typography, Select, Divider, Switch, Tooltip } from 'antd';
import {
  EyeOutlined,
  CodeOutlined,
  CopyOutlined,
  DownloadOutlined,
  SettingOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * ComponentPreview component
 * Displays a preview of a component with code and options
 */
const ComponentPreview = ({
  component,
  code,
  title = 'Component Preview',
  description = '',
  defaultTab = 'preview'
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [theme, setTheme] = useState('light');
  const [size, setSize] = useState('default');
  const [fullscreen, setFullscreen] = useState(false);
  const [showBorder, setShowBorder] = useState(true);

  // Handle copy code
  const handleCopyCode = () => {
    navigator.clipboard.writeText(code);
  };

  // Handle download code
  const handleDownloadCode = () => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.jsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Handle fullscreen toggle
  const handleFullscreenToggle = () => {
    setFullscreen(!fullscreen);
  };

  // Handle theme change
  const handleThemeChange = (value) => {
    setTheme(value);
  };

  // Handle size change
  const handleSizeChange = (value) => {
    setSize(value);
  };

  // Handle border toggle
  const handleBorderToggle = (checked) => {
    setShowBorder(checked);
  };

  // Reset options
  const handleResetOptions = () => {
    setTheme('light');
    setSize('default');
    setShowBorder(true);
  };

  // Render the component with the current options
  const renderComponent = () => {
    // Clone the component with the current options
    return React.cloneElement(component, {
      size,
      className: `theme-${theme}`,
      style: {
        ...(component.props.style || {}),
        border: showBorder ? '1px dashed #d9d9d9' : 'none',
        padding: showBorder ? '16px' : '0'
      }
    });
  };

  // If fullscreen, render only the component
  if (fullscreen) {
    return (
      <div className="component-preview-fullscreen">
        <div className="component-preview-fullscreen-header">
          <Title level={5}>{title}</Title>
          <Button
            icon={<FullscreenExitOutlined />}
            onClick={handleFullscreenToggle}
            type="text"
          />
        </div>
        <div className="component-preview-fullscreen-content">
          {renderComponent()}
        </div>
        <style>{`
          .component-preview-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: ${theme === 'dark' ? '#1f1f1f' : '#ffffff'};
            z-index: 1000;
            display: flex;
            flex-direction: column;
          }

          .component-preview-fullscreen-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #d9d9d9;
          }

          .component-preview-fullscreen-content {
            flex: 1;
            padding: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .theme-dark {
            background-color: #1f1f1f;
            color: #ffffff;
          }
        `}</style>
      </div>
    );
  }

  return (
    <Card
      title={title}
      extra={
        <Space>
          <Tooltip title="Copy Code">
            <Button
              icon={<CopyOutlined />}
              onClick={handleCopyCode}
              type="text"
            />
          </Tooltip>
          <Tooltip title="Download Code">
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadCode}
              type="text"
            />
          </Tooltip>
          <Tooltip title="Fullscreen">
            <Button
              icon={<FullscreenOutlined />}
              onClick={handleFullscreenToggle}
              type="text"
            />
          </Tooltip>
        </Space>
      }
      className="component-preview-card"
    >
      {description && (
        <div className="component-preview-description">
          <Text type="secondary">{description}</Text>
        </div>
      )}

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        tabBarExtraContent={
          activeTab === 'preview' && (
            <Space>
              <Select
                value={theme}
                onChange={handleThemeChange}
                dropdownMatchSelectWidth={false}
              >
                <Option value="light">Light</Option>
                <Option value="dark">Dark</Option>
              </Select>
              <Select
                value={size}
                onChange={handleSizeChange}
                dropdownMatchSelectWidth={false}
              >
                <Option value="small">Small</Option>
                <Option value="default">Default</Option>
                <Option value="large">Large</Option>
              </Select>
              <Tooltip title="Show Border">
                <Switch
                  checked={showBorder}
                  onChange={handleBorderToggle}
                  size="small"
                />
              </Tooltip>
              <Tooltip title="Reset Options">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleResetOptions}
                  type="text"
                  size="small"
                />
              </Tooltip>
            </Space>
          )
        }
      >
        <TabPane
          tab={
            <span>
              <EyeOutlined />
              Preview
            </span>
          }
          key="preview"
        >
          <div
            className={`component-preview-content ${theme === 'dark' ? 'theme-dark' : ''}`}
          >
            {renderComponent()}
          </div>
        </TabPane>
        <TabPane
          tab={
            <span>
              <CodeOutlined />
              Code
            </span>
          }
          key="code"
        >
          <SyntaxHighlighter
            language="jsx"
            style={vscDarkPlus}
            showLineNumbers
            wrapLines
          >
            {code}
          </SyntaxHighlighter>
        </TabPane>
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              Props
            </span>
          }
          key="props"
        >
          <div className="component-preview-props">
            <Title level={5}>Component Props</Title>
            <Divider />
            <table className="props-table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Type</th>
                  <th>Default</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(component.type.propTypes || {}).map(([name, type]) => (
                  <tr key={name}>
                    <td>{name}</td>
                    <td>{type.type?.name || 'any'}</td>
                    <td>{component.type.defaultProps?.[name]?.toString() || '-'}</td>
                    <td>{type.description || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </TabPane>
      </Tabs>

      <style>{`
        .component-preview-card {
          margin-bottom: 24px;
        }

        .component-preview-description {
          margin-bottom: 16px;
        }

        .component-preview-content {
          padding: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 200px;
          border-radius: 4px;
          transition: background-color 0.3s;
        }

        .theme-dark {
          background-color: #1f1f1f;
          color: #ffffff;
        }

        .component-preview-props {
          padding: 16px;
        }

        .props-table {
          width: 100%;
          border-collapse: collapse;
        }

        .props-table th,
        .props-table td {
          padding: 8px;
          text-align: left;
          border-bottom: 1px solid #d9d9d9;
        }

        .props-table th {
          font-weight: bold;
          background-color: #f5f5f5;
        }
      `}</style>
    </Card>
  );
};

export default ComponentPreview;
