"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[9578],{

/***/ 11398:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(70572);




var _templateObject, _templateObject2, _templateObject3;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Title = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Paragraph;
var Panel = antd__WEBPACK_IMPORTED_MODULE_6__/* .Collapse */ .SD.Panel;

// Styled components
var MonitorContainer = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  bottom: 20px;\n  left: 20px;\n  z-index: 1000;\n  max-width: 400px;\n  transition: all 0.3s ease;\n  transform: ", ";\n"])), function (props) {
  return props.minimized ? 'translateY(calc(100% - 40px))' : 'translateY(0)';
});
var MinimizeButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1001;\n"])));
var StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  margin-right: 8px;\n  background-color: ", ";\n"])), function (props) {
  return props.status === 'good' ? '#52c41a' : props.status === 'warning' ? '#faad14' : '#f5222d';
});

/**
 * EnhancedPerformanceMonitor component
 * 
 * This component monitors various performance metrics of the application
 * and displays them in a user-friendly dashboard.
 */
var EnhancedPerformanceMonitor = function EnhancedPerformanceMonitor(_ref) {
  var _ref$enabled = _ref.enabled,
    enabled = _ref$enabled === void 0 ? true : _ref$enabled,
    _ref$initiallyMinimiz = _ref.initiallyMinimized,
    initiallyMinimized = _ref$initiallyMinimiz === void 0 ? true : _ref$initiallyMinimiz,
    _ref$refreshInterval = _ref.refreshInterval,
    refreshInterval = _ref$refreshInterval === void 0 ? 5000 : _ref$refreshInterval,
    _ref$wsUrl = _ref.wsUrl,
    wsUrl = _ref$wsUrl === void 0 ? 'ws://localhost:8000/ws' : _ref$wsUrl,
    _ref$showNetworkStatu = _ref.showNetworkStatus,
    showNetworkStatus = _ref$showNetworkStatu === void 0 ? true : _ref$showNetworkStatu,
    _ref$showMemoryUsage = _ref.showMemoryUsage,
    showMemoryUsage = _ref$showMemoryUsage === void 0 ? true : _ref$showMemoryUsage,
    _ref$showRenderCounts = _ref.showRenderCounts,
    showRenderCounts = _ref$showRenderCounts === void 0 ? true : _ref$showRenderCounts,
    _ref$showWebSocketSta = _ref.showWebSocketStatus,
    showWebSocketStatus = _ref$showWebSocketSta === void 0 ? true : _ref$showWebSocketSta;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(initiallyMinimized),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    minimized = _useState2[0],
    setMinimized = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      fps: 0,
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      network: {
        latency: 0,
        status: 'unknown'
      },
      websocket: {
        connected: false,
        latency: 0
      },
      renderCounts: {}
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    metrics = _useState4[0],
    setMetrics = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(enabled),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isActive = _useState6[0],
    setIsActive = _useState6[1];
  var rafId = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);
  var lastFrameTime = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(performance.now());
  var frameCount = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(0);
  var wsRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);
  var pingInterval = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);

  // Toggle minimized state
  var toggleMinimized = function toggleMinimized() {
    setMinimized(function (prev) {
      return !prev;
    });
  };

  // Toggle active state
  var toggleActive = function toggleActive(checked) {
    setIsActive(checked);
  };

  // Measure FPS
  var measureFPS = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    if (!isActive) return;
    var now = performance.now();
    frameCount.current += 1;

    // Update FPS every second
    if (now - lastFrameTime.current >= 1000) {
      setMetrics(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          fps: Math.round(frameCount.current * 1000 / (now - lastFrameTime.current))
        });
      });
      frameCount.current = 0;
      lastFrameTime.current = now;
    }
    rafId.current = requestAnimationFrame(measureFPS);
  }, [isActive]);

  // Measure memory usage
  var measureMemory = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
    var _performance$memory, usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!(!isActive || !showMemoryUsage)) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return");
        case 1:
          try {
            // Use performance.memory if available (Chrome only)
            if (performance.memory) {
              _performance$memory = performance.memory, usedJSHeapSize = _performance$memory.usedJSHeapSize, totalJSHeapSize = _performance$memory.totalJSHeapSize, jsHeapSizeLimit = _performance$memory.jsHeapSizeLimit;
              setMetrics(function (prev) {
                return _objectSpread(_objectSpread({}, prev), {}, {
                  memory: {
                    used: Math.round(usedJSHeapSize / (1024 * 1024)),
                    total: Math.round(totalJSHeapSize / (1024 * 1024)),
                    limit: Math.round(jsHeapSizeLimit / (1024 * 1024))
                  }
                });
              });
            }
          } catch (error) {
            console.error('Error measuring memory:', error);
          }
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [isActive, showMemoryUsage]);

  // Measure network status
  var measureNetwork = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {
    var startTime, response, endTime, _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!(!isActive || !showNetworkStatus)) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return");
        case 1:
          _context2.prev = 1;
          startTime = performance.now();
          _context2.next = 2;
          return fetch('/api/health/');
        case 2:
          response = _context2.sent;
          endTime = performance.now();
          setMetrics(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              network: {
                latency: Math.round(endTime - startTime),
                status: response.ok ? 'good' : 'error'
              }
            });
          });
          _context2.next = 4;
          break;
        case 3:
          _context2.prev = 3;
          _t = _context2["catch"](1);
          setMetrics(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              network: {
                latency: 0,
                status: 'error'
              }
            });
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 3]]);
  })), [isActive, showNetworkStatus]);

  // Initialize WebSocket connection
  var initWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    if (!isActive || !showWebSocketStatus) return;
    try {
      // Close existing connection if any
      if (wsRef.current) {
        wsRef.current.close();
      }

      // Create new connection
      wsRef.current = new WebSocket(wsUrl);

      // Set up event handlers
      wsRef.current.onopen = function () {
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
              connected: true
            })
          });
        });

        // Start ping interval
        pingInterval.current = setInterval(function () {
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            var pingTime = performance.now();
            wsRef.current.send(JSON.stringify({
              type: 'ping',
              time: pingTime
            }));
          }
        }, 5000);
      };
      wsRef.current.onclose = function () {
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
              connected: false
            })
          });
        });

        // Clear ping interval
        if (pingInterval.current) {
          clearInterval(pingInterval.current);
        }
      };
      wsRef.current.onmessage = function (event) {
        try {
          var data = JSON.parse(event.data);
          if (data.type === 'pong' && data.time) {
            var pongTime = performance.now();
            var latency = Math.round(pongTime - data.time);
            setMetrics(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, {
                websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
                  latency: latency
                })
              });
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      wsRef.current.onerror = function (error) {
        console.error('WebSocket error:', error);
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            websocket: {
              connected: false,
              latency: 0
            }
          });
        });
      };
    } catch (error) {
      console.error('Error initializing WebSocket:', error);
    }
  }, [isActive, showWebSocketStatus, wsUrl]);

  // Initialize performance monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (isActive) {
      // Start FPS measurement
      rafId.current = requestAnimationFrame(measureFPS);

      // Start memory measurement
      var memoryInterval = setInterval(measureMemory, refreshInterval);

      // Start network measurement
      var networkInterval = setInterval(measureNetwork, refreshInterval);

      // Initialize WebSocket
      initWebSocket();

      // Clean up
      return function () {
        cancelAnimationFrame(rafId.current);
        clearInterval(memoryInterval);
        clearInterval(networkInterval);
        if (pingInterval.current) {
          clearInterval(pingInterval.current);
        }
        if (wsRef.current) {
          wsRef.current.close();
        }
      };
    }
  }, [isActive, measureFPS, measureMemory, measureNetwork, initWebSocket, refreshInterval]);

  // Don't render if not enabled
  if (!enabled) {
    return null;
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(MonitorContainer, {
    minimized: minimized
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .DashboardOutlined */ .zpd, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, "Performance Monitor")),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Switch */ .dO, {
      checked: isActive,
      onChange: toggleActive,
      size: "small",
      checkedChildren: "On",
      unCheckedChildren: "Off"
    }),
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(MinimizeButton, {
    type: "text",
    icon: minimized ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LineChartOutlined */ .BdS, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LineChartOutlined */ .BdS, null),
    onClick: toggleMinimized,
    size: "small"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Statistic */ .jL, {
    title: "FPS",
    value: metrics.fps,
    suffix: "fps",
    valueStyle: {
      color: metrics.fps > 30 ? '#3f8600' : metrics.fps > 15 ? '#faad14' : '#cf1322'
    }
  })), showMemoryUsage && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Statistic */ .jL, {
    title: "Memory",
    value: metrics.memory.used,
    suffix: "MB",
    valueStyle: {
      color: metrics.memory.used < metrics.memory.limit * 0.8 ? '#3f8600' : '#cf1322'
    }
  }), metrics.memory.limit > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Progress */ .ke, {
    percent: Math.round(metrics.memory.used / metrics.memory.limit * 100),
    size: "small",
    status: metrics.memory.used < metrics.memory.limit * 0.8 ? 'normal' : 'exception'
  }))), showNetworkStatus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(StatusIndicator, {
    status: metrics.network.status
  }), "Network: ", metrics.network.status === 'good' ? 'Connected' : 'Disconnected', metrics.network.latency > 0 && " (".concat(metrics.network.latency, "ms)"))), showWebSocketStatus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(StatusIndicator, {
    status: metrics.websocket.connected ? 'good' : 'error'
  }), "WebSocket: ", metrics.websocket.connected ? 'Connected' : 'Disconnected', metrics.websocket.connected && metrics.websocket.latency > 0 && " (".concat(metrics.websocket.latency, "ms)")))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedPerformanceMonitor);

/***/ }),

/***/ 18952:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(70572);
/* harmony import */ var _hooks_useDataManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7535);
/* harmony import */ var _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(50263);
/* harmony import */ var _redux_minimal_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(34816);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;








var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var DemoContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 20px;\n"])));
var DemoCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 20px;\n"])));
var FormGroup = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  \n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"])));
var ButtonGroup = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"])));
var InfoBox = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n"])));

/**
 * DataManagementDemo component
 * Demonstrates the use of data management utilities
 */
var DataManagementDemo = function DataManagementDemo() {
  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    inputValue = _useState2[0],
    setInputValue = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('temporary'),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    cacheType = _useState4[0],
    setCacheType = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    useCache = _useState6[0],
    setUseCache = _useState6[1];

  // Use our custom hook to manage data
  var _useDataManager = (0,_hooks_useDataManager__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)({
      selector: function selector(state) {
        var _state$ui;
        return (_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.currentView;
      },
      action: _redux_minimal_store__WEBPACK_IMPORTED_MODULE_10__.setCurrentView,
      cacheKey: 'current_view',
      cacheType: cacheType,
      useCache: useCache,
      defaultValue: 'components'
    }),
    currentView = _useDataManager.data,
    loading = _useDataManager.loading,
    error = _useDataManager.error,
    updateData = _useDataManager.updateData,
    refreshData = _useDataManager.refreshData,
    clearCache = _useDataManager.clearCache;

  // Handle saving data
  var handleSave = /*#__PURE__*/function () {
    var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
      var _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (inputValue.trim()) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            _context.prev = 1;
            _context.next = 2;
            return updateData(inputValue);
          case 2:
            setInputValue('');
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            console.error('Error saving data:', _t);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3]]);
    }));
    return function handleSave() {
      return _ref.apply(this, arguments);
    };
  }();

  // Handle refreshing data
  var handleRefresh = /*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2() {
      var _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 1;
            return refreshData();
          case 1:
            _context2.next = 3;
            break;
          case 2:
            _context2.prev = 2;
            _t2 = _context2["catch"](0);
            console.error('Error refreshing data:', _t2);
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 2]]);
    }));
    return function handleRefresh() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle clearing cache
  var handleClearCache = function handleClearCache() {
    clearCache();
  };

  // Handle direct cache operations
  var handleSetDirectCache = function handleSetDirectCache() {
    if (!inputValue.trim()) return;
    _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__/* ["default"].setCache */ .Ay.setCache('direct_cache_demo', inputValue, {
      type: cacheType,
      expiresIn: 3600000 // 1 hour
    });
    setInputValue('');
  };
  var handleGetDirectCache = function handleGetDirectCache() {
    var cachedValue = _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__/* ["default"].getCache */ .Ay.getCache('direct_cache_demo', cacheType);
    setInputValue(cachedValue || '');
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DemoContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 3
  }, "Data Management Demo"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, null, "This demo shows how to use the data management utilities to optimize Redux usage and handle data operations."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DemoCard, {
    title: "Current View Data"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(InfoBox, {
    type: "info",
    message: "Redux + Cache Integration",
    description: "This example shows how to use the useDataManager hook to manage data with Redux and caching.",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, null)
  }), loading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      marginTop: '10px'
    }
  }, "Loading data...")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(react__WEBPACK_IMPORTED_MODULE_4__.Fragment, null, error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    type: "error",
    message: "Error",
    description: error.message || 'An error occurred while managing data',
    style: {
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Current View:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    strong: true
  }, currentView)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "New View:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    value: inputValue,
    onChange: function onChange(e) {
      return setInputValue(e.target.value);
    },
    placeholder: "Enter a new view name"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Cache Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: cacheType,
    onChange: setCacheType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "temporary"
  }, "Temporary (Memory only)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "persistent"
  }, "Persistent (LocalStorage)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "expiring"
  }, "Expiring (Time-based)"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Use Cache:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: useCache,
    onChange: setUseCache
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ButtonGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SaveOutlined */ .ylI, null),
    onClick: handleSave,
    disabled: !inputValue.trim()
  }, "Save to Redux"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ReloadOutlined */ .KF4, null),
    onClick: handleRefresh
  }, "Refresh Data"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    danger: true,
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ClearOutlined */ .ohj, null),
    onClick: handleClearCache
  }, "Clear Cache")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(DemoCard, {
    title: "Direct Cache Operations"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(InfoBox, {
    type: "info",
    message: "Direct Cache API",
    description: "This example shows how to use the dataManager utility directly for caching operations.",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Cache Value:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    value: inputValue,
    onChange: function onChange(e) {
      return setInputValue(e.target.value);
    },
    placeholder: "Enter a value to cache"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FormGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    className: "label"
  }, "Cache Type:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
    value: cacheType,
    onChange: setCacheType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "temporary"
  }, "Temporary (Memory only)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "persistent"
  }, "Persistent (LocalStorage)"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
    value: "expiring"
  }, "Expiring (Time-based)"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ButtonGroup, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    onClick: handleSetDirectCache,
    disabled: !inputValue.trim()
  }, "Set Cache"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    onClick: handleGetDirectCache
  }, "Get Cache"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    danger: true,
    onClick: function onClick() {
      return _utils_dataManager__WEBPACK_IMPORTED_MODULE_9__/* ["default"].clearCache */ .Ay.clearCache('direct_cache_demo', cacheType);
    }
  }, "Clear Cache"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataManagementDemo);

/***/ }),

/***/ 49697:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(35346);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71468);
/* harmony import */ var _redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(41533);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(70572);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;






var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Paragraph;
var Panel = antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD.Panel;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd.TextArea;

// Styled components
var StatusBadge = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Badge */ .Ex)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  .ant-badge-status-dot {\n    width: 10px;\n    height: 10px;\n  }\n"])));
var MessageContainer = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  max-height: 300px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  padding: 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background-color: ", ";\n"])), function (props) {
  return props.theme === 'dark' ? '#1f1f1f' : '#f5f5f5';
});
var MessageItem = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 8px;\n  margin-bottom: 8px;\n  border-radius: 4px;\n  background-color: ", ";\n  border-left: 4px solid ", ";\n  color: ", ";\n  word-break: break-word;\n"])), function (props) {
  return props.type === 'sent' ? props.theme === 'dark' ? '#177ddc' : '#e6f7ff' : props.theme === 'dark' ? '#2b2b2b' : '#ffffff';
}, function (props) {
  return props.type === 'sent' ? '#1890ff' : props.status === 'error' ? '#ff4d4f' : props.status === 'warning' ? '#faad14' : '#52c41a';
}, function (props) {
  return props.theme === 'dark' ? '#ffffff' : '#000000';
});
var TimeStamp = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(Text)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-left: 8px;\n"])), function (props) {
  return props.theme === 'dark' ? '#8c8c8c' : '#8c8c8c';
});
var ConnectionStatus = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n"])));

/**
 * WebSocketManager component
 * 
 * This component provides a user interface for managing WebSocket connections
 * and viewing WebSocket messages.
 */
var WebSocketManager = function WebSocketManager(_ref) {
  var _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible,
    _ref$onClose = _ref.onClose,
    onClose = _ref$onClose === void 0 ? function () {} : _ref$onClose,
    _ref$placement = _ref.placement,
    placement = _ref$placement === void 0 ? 'right' : _ref$placement,
    _ref$width = _ref.width,
    width = _ref$width === void 0 ? 600 : _ref$width;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useDispatch */ .wA)();
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _Form$useForm3 = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm3, 1),
    messageForm = _Form$useForm4[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    autoScroll = _useState2[0],
    setAutoScroll = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    filterText = _useState4[0],
    setFilterText = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    showSettings = _useState6[0],
    setShowSettings = _useState6[1];

  // Get WebSocket state from Redux store with error handling
  var connected = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var connecting = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket2;
    return (state === null || state === void 0 || (_state$websocket2 = state.websocket) === null || _state$websocket2 === void 0 ? void 0 : _state$websocket2.connecting) || false;
  });
  var error = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket3;
    return (state === null || state === void 0 || (_state$websocket3 = state.websocket) === null || _state$websocket3 === void 0 ? void 0 : _state$websocket3.error) || null;
  });
  var messages = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket4;
    return (state === null || state === void 0 || (_state$websocket4 = state.websocket) === null || _state$websocket4 === void 0 ? void 0 : _state$websocket4.messages) || [];
  });
  var url = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket5;
    return (state === null || state === void 0 || (_state$websocket5 = state.websocket) === null || _state$websocket5 === void 0 ? void 0 : _state$websocket5.url) || null;
  });
  var reconnectAttempts = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket6;
    return (state === null || state === void 0 || (_state$websocket6 = state.websocket) === null || _state$websocket6 === void 0 ? void 0 : _state$websocket6.reconnectAttempts) || 0;
  });
  var reconnectInterval = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket7;
    return (state === null || state === void 0 || (_state$websocket7 = state.websocket) === null || _state$websocket7 === void 0 ? void 0 : _state$websocket7.reconnectInterval) || 5000;
  });

  // Get theme from Redux store
  var isDarkMode = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$themes, _state$themes2;
    var activeThemeId = (_state$themes = state.themes) === null || _state$themes === void 0 ? void 0 : _state$themes.activeTheme;
    var themes = ((_state$themes2 = state.themes) === null || _state$themes2 === void 0 ? void 0 : _state$themes2.themes) || [];
    var activeTheme = themes.find(function (theme) {
      return theme.id === activeThemeId;
    });
    return (activeTheme === null || activeTheme === void 0 ? void 0 : activeTheme.isDark) || false;
  });

  // Auto-scroll to bottom of message container
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (autoScroll) {
      var container = document.getElementById('ws-message-container');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [messages, autoScroll]);

  // Connect to WebSocket
  var handleConnect = function handleConnect() {
    form.validateFields().then(function (values) {
      var url = values.url,
        reconnectAttempts = values.reconnectAttempts,
        reconnectInterval = values.reconnectInterval,
        protocols = values.protocols;
      dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsConnect */ .Lw)(url, {
        reconnectAttempts: reconnectAttempts,
        reconnectInterval: reconnectInterval,
        protocols: protocols ? protocols.split(',').map(function (p) {
          return p.trim();
        }) : undefined
      }));
    });
  };

  // Disconnect from WebSocket
  var handleDisconnect = function handleDisconnect() {
    dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsDisconnect */ .rD)());
  };

  // Send message
  var handleSendMessage = function handleSendMessage() {
    messageForm.validateFields().then(function (values) {
      var message = values.message;
      try {
        // Try to parse as JSON
        var jsonMessage = JSON.parse(message);
        dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsSendMessage */ .EZ)(jsonMessage));
      } catch (error) {
        // Send as plain text
        dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsSendMessage */ .EZ)(message));
      }

      // Clear message input
      messageForm.resetFields();
    });
  };

  // Clear messages
  var handleClearMessages = function handleClearMessages() {
    dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsClearMessages */ .G2)());
  };

  // Toggle settings drawer
  var toggleSettings = function toggleSettings() {
    setShowSettings(!showSettings);
  };

  // Filter messages
  var filteredMessages = messages.filter(function (message) {
    if (!filterText) return true;
    var messageStr = (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message.data) === 'object' ? JSON.stringify(message.data) : String(message.data);
    return messageStr.toLowerCase().includes(filterText.toLowerCase());
  });

  // Format message for display
  var formatMessage = function formatMessage(message) {
    if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message) === 'object') {
      try {
        return JSON.stringify(message, null, 2);
      } catch (error) {
        return String(message);
      }
    }
    return String(message);
  };

  // Get connection status
  var getConnectionStatus = function getConnectionStatus() {
    if (connected) {
      return {
        status: 'success',
        text: 'Connected',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CheckCircleOutlined */ .hWy, null)
      };
    } else if (connecting) {
      return {
        status: 'processing',
        text: 'Connecting',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Spin */ .tK, {
          size: "small"
        })
      };
    } else if (error) {
      return {
        status: 'error',
        text: 'Error',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseCircleOutlined */ .bBN, null)
      };
    } else {
      return {
        status: 'default',
        text: 'Disconnected',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null)
      };
    }
  };
  var connectionStatus = getConnectionStatus();

  // Initialize form with current values
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    form.setFieldsValue({
      url: url || 'ws://localhost:8000/ws',
      reconnectAttempts: reconnectAttempts || 5,
      reconnectInterval: reconnectInterval || 3000,
      protocols: ''
    });
  }, [form, url, reconnectAttempts, reconnectInterval]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ApiOutlined */ .bfv, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "WebSocket Manager")),
    placement: placement,
    width: width,
    onClose: onClose,
    open: visible,
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
      onClick: toggleSettings
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ConnectionStatus, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(StatusBadge, {
    status: connectionStatus.status
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, connectionStatus.text), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: error.message
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .WarningOutlined */ .v7y, {
    style: {
      marginLeft: 8,
      color: '#ff4d4f'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginLeft: 'auto'
    }
  }, connected ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null),
    onClick: handleDisconnect,
    danger: true
  }, "Disconnect") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ApiOutlined */ .bfv, null),
    onClick: handleConnect,
    type: "primary",
    loading: connecting
  }, "Connect"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD, {
    defaultActiveKey: ['1', '2']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Panel, {
    header: "Connection Settings",
    key: "1",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ReloadOutlined */ .KF4, null),
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        form.resetFields();
      }
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: form,
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "url",
    label: "WebSocket URL",
    rules: [{
      required: true,
      message: 'Please enter WebSocket URL'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "ws://localhost:8000/ws",
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "protocols",
    label: "Protocols (comma-separated)"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "protocol1, protocol2",
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "reconnectAttempts",
    label: "Reconnect Attempts",
    rules: [{
      required: true,
      message: 'Please enter reconnect attempts'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 0,
    max: 10,
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "reconnectInterval",
    label: "Reconnect Interval (ms)",
    rules: [{
      required: true,
      message: 'Please enter reconnect interval'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 1000,
    max: 10000,
    disabled: connected || connecting
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Panel, {
    header: "Messages",
    key: "2",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Auto-scroll to bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      checked: autoScroll,
      onChange: setAutoScroll,
      size: "small"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ClearOutlined */ .ohj, null),
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleClearMessages();
      }
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "Filter messages",
    value: filterText,
    onChange: function onChange(e) {
      return setFilterText(e.target.value);
    },
    allowClear: true,
    style: {
      marginBottom: 16
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .InfoCircleOutlined */ .rUN, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MessageContainer, {
    id: "ws-message-container",
    theme: isDarkMode ? 'dark' : 'light'
  }, filteredMessages.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "No messages") : filteredMessages.map(function (message, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MessageItem, {
      key: index,
      type: message.type,
      status: message.status,
      theme: isDarkMode ? 'dark' : 'light'
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        marginBottom: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
      color: message.type === 'sent' ? 'blue' : 'green'
    }, message.type === 'sent' ? 'Sent' : 'Received'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TimeStamp, {
      theme: isDarkMode ? 'dark' : 'light'
    }, new Date(message.timestamp).toLocaleTimeString())), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("pre", {
      style: {
        margin: 0,
        whiteSpace: 'pre-wrap'
      }
    }, formatMessage(message.data)));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: messageForm,
    layout: "inline",
    style: {
      display: 'flex',
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "message",
    style: {
      flex: 1,
      marginRight: 8
    },
    rules: [{
      required: true,
      message: 'Please enter a message'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
    placeholder: "Enter message (plain text or JSON)",
    autoSize: {
      minRows: 1,
      maxRows: 6
    },
    disabled: !connected
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SendOutlined */ .jnF, null),
    onClick: handleSendMessage,
    disabled: !connected
  }, "Send"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: "Advanced Settings",
    placement: "right",
    closable: true,
    onClose: toggleSettings,
    open: showSettings,
    width: 400
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "These settings allow you to configure the WebSocket connection behavior."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Ping Interval (ms)",
    name: "pingInterval",
    initialValue: 30000
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 1000,
    max: 60000
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Ping Message",
    name: "pingMessage",
    initialValue: "{\"type\":\"ping\"}"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Auto Reconnect",
    name: "autoReconnect",
    valuePropName: "checked",
    initialValue: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Debug Mode",
    name: "debugMode",
    valuePropName: "checked",
    initialValue: false
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, null)))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketManager);

/***/ }),

/***/ 99578:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(1807);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(35346);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(70572);





var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





// Import the core feature components with fallbacks
var ComponentBuilder, LayoutDesigner, ThemeManager, WebSocketManager;

// Import the new MVP feature components with fallbacks
var IntegratedTutorialAssistant, TestingTools, DataManagementDemo, EnhancedPerformanceMonitor, EnhancedCodeExporter;
try {
  ComponentBuilder = (__webpack_require__(16030)["default"]);
} catch (error) {
  console.warn('ComponentBuilder not available, using fallback');
  ComponentBuilder = function ComponentBuilder() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Component Builder"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component Builder is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Add Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Add Text"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Add Input"));
  };
}
try {
  LayoutDesigner = (__webpack_require__(95505)["default"]);
} catch (error) {
  console.warn('LayoutDesigner not available, using fallback');
  LayoutDesigner = function LayoutDesigner() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Layout Designer"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Layout Designer is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Grid Layout"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Flex Layout"));
  };
}
try {
  ThemeManager = (__webpack_require__(71667)["default"]);
} catch (error) {
  console.warn('ThemeManager not available, using fallback');
  ThemeManager = function ThemeManager() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Theme Manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Theme Manager is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Primary Color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Typography"));
  };
}
try {
  WebSocketManager = (__webpack_require__(49697)/* ["default"] */ .A);
} catch (error) {
  console.warn('WebSocketManager not available, using fallback');
  WebSocketManager = function WebSocketManager(_ref) {
    var onConnectionChange = _ref.onConnectionChange;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "WebSocket Manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Real-time collaboration features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary",
      onClick: function onClick() {
        console.log('WebSocket connection simulated');
        if (onConnectionChange) onConnectionChange(true);
      }
    }, "Connect"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      onClick: function onClick() {
        console.log('WebSocket disconnection simulated');
        if (onConnectionChange) onConnectionChange(false);
      }
    }, "Disconnect")));
  };
}

// Import Tutorial Assistant
try {
  IntegratedTutorialAssistant = (__webpack_require__(9771)/* ["default"] */ .A);
} catch (error) {
  console.warn('IntegratedTutorialAssistant not available, using fallback');
  IntegratedTutorialAssistant = function IntegratedTutorialAssistant() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Tutorial Assistant",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Interactive tutorials and context-aware help system"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Start Tutorial"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "View Help"));
  };
}

// Import Testing Tools
try {
  TestingTools = (__webpack_require__(11937)/* ["default"] */ .A);
} catch (error) {
  console.warn('TestingTools not available, using fallback');
  TestingTools = function TestingTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Testing Tools",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component testing, validation, and accessibility checks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Run Tests"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Accessibility Check"));
  };
}

// Import Data Management
try {
  DataManagementDemo = (__webpack_require__(18952)["default"]);
} catch (error) {
  console.warn('DataManagementDemo not available, using fallback');
  DataManagementDemo = function DataManagementDemo() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Data Management",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Data binding, state management, and flow visualization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Manage Data"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "View Flow"));
  };
}

// Import Performance Monitor
try {
  EnhancedPerformanceMonitor = (__webpack_require__(11398)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedPerformanceMonitor not available, using fallback');
  EnhancedPerformanceMonitor = function EnhancedPerformanceMonitor() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Performance Monitor",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Bundle size tracking and optimization suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Analyze Performance"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "View Metrics"));
  };
}

// Import Enhanced Code Exporter
try {
  EnhancedCodeExporter = (__webpack_require__(4090)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedCodeExporter not available, using fallback');
  EnhancedCodeExporter = function EnhancedCodeExporter() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Enhanced Export",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Multi-framework export with TypeScript generation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Export React"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Export Vue"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Export Angular"));
  };
}
var Title = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Text;
var IntegratedContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100vh;\n  background: #f5f5f5;\n  overflow: hidden;\n"])));
var HeaderBar = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  background: #fff;\n  padding: 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.06);\n"])));
var ContentArea = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: calc(100vh - 80px);\n  overflow: auto;\n  padding: 24px;\n"])));
var FeatureGrid = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  grid-template-rows: 1fr 1fr 1fr;\n  gap: 16px;\n  height: 100%;\n  min-height: 800px;\n"])));
var FeatureCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    height: calc(100% - 57px);\n    overflow: auto;\n  }\n"])));
var DemoArea = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  grid-column: 1 / -1;\n  margin-top: 24px;\n  \n  .demo-canvas {\n    min-height: 300px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    padding: 20px;\n    background: #fafafa;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n  }\n"])));

/**
 * AppBuilderIntegrated - Comprehensive integrated app builder with all core features
 */
var AppBuilderIntegrated = function AppBuilderIntegrated() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    activeFeature = _useState2[0],
    setActiveFeature = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      components: [],
      layout: {
        type: 'grid',
        columns: 3
      },
      theme: {
        primaryColor: '#1890ff',
        fontFamily: 'Inter'
      },
      websocket: {
        connected: false,
        collaborators: []
      }
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    projectData = _useState4[0],
    setProjectData = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      components: [],
      layout: null,
      theme: null,
      isBuilding: false
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    demoApp = _useState6[0],
    setDemoApp = _useState6[1];

  // Sample app creation workflow
  var createSampleApp = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee() {
    var sampleComponents, sampleLayout, sampleTheme;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setDemoApp(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              isBuilding: true
            });
          });

          // Step 1: Add components
          sampleComponents = [{
            id: 'btn1',
            type: 'button',
            props: {
              text: 'Get Started',
              type: 'primary'
            },
            position: {
              x: 50,
              y: 50
            }
          }, {
            id: 'txt1',
            type: 'text',
            props: {
              text: 'Welcome to App Builder',
              size: 'large'
            },
            position: {
              x: 50,
              y: 100
            }
          }, {
            id: 'card1',
            type: 'card',
            props: {
              title: 'Feature Card',
              content: 'This is a sample card'
            },
            position: {
              x: 200,
              y: 50
            }
          }, {
            id: 'input1',
            type: 'input',
            props: {
              placeholder: 'Enter your name'
            },
            position: {
              x: 50,
              y: 200
            }
          }]; // Step 2: Apply layout
          sampleLayout = {
            type: 'grid',
            columns: 2,
            gap: '16px',
            responsive: true
          }; // Step 3: Apply theme
          sampleTheme = {
            primaryColor: '#52c41a',
            fontFamily: 'Inter, sans-serif',
            borderRadius: '8px',
            spacing: '16px'
          }; // Simulate building process
          _context.next = 1;
          return new Promise(function (resolve) {
            return setTimeout(resolve, 1000);
          });
        case 1:
          setDemoApp({
            components: sampleComponents,
            layout: sampleLayout,
            theme: sampleTheme,
            isBuilding: false
          });
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              components: sampleComponents,
              layout: sampleLayout,
              theme: sampleTheme
            });
          });
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  var tabItems = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var _demoApp$layout, _demoApp$layout2;
    return [{
      key: 'overview',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .RocketOutlined */ .PKb, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "Integrated Builder")),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
        message: "App Builder Enhanced - All Features Integrated",
        description: "This integrated view demonstrates all four core features working together: Component Builder, Layout Designer, Theme Manager, and WebSocket Manager.",
        type: "success",
        showIcon: true,
        style: {
          marginBottom: '24px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('components');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ComponentBuilder, {
        onComponentAdd: function onComponentAdd(component) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              components: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.components), [component])
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('layouts');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(LayoutDesigner, {
        components: projectData.components,
        onLayoutChange: function onLayoutChange(layout) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: layout
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('themes');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ThemeManager, {
        currentTheme: projectData.theme,
        onThemeChange: function onThemeChange(theme) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              theme: theme
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ApiOutlined */ .bfv, null), "WebSocket Manager"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('websocket');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(WebSocketManager, {
        onConnectionChange: function onConnectionChange(status) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
                connected: status
              })
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .QuestionCircleOutlined */ .faO, null), "Tutorial Assistant"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('tutorial');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Interactive tutorials and context-aware help system"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Start Tutorial"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null), "Testing Tools"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('testing');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component testing, validation, and accessibility checks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Run Tests"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DatabaseOutlined */ .ose, null), "Data Management"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('data');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Data binding, state management, and flow visualization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Manage Data"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DashboardOutlined */ .zpd, null), "Performance Monitor"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('performance');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Bundle size tracking and optimization suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Analyze"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExportOutlined */ .PZg, null), "Enhanced Export"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('export');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Multi-framework export with TypeScript generation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Export Code")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(DemoArea, {
        title: "Sample App Demonstration"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
        gutter: [24, 24]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
        xs: 24,
        md: 12
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
        direction: "vertical",
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Create Sample App"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Demonstrate the complete workflow from component creation to styled, collaborative application."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "large",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
        onClick: createSampleApp,
        loading: demoApp.isBuilding
      }, "Build Sample App"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
        xs: 24,
        md: 12
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        className: "demo-canvas"
      }, demoApp.isBuilding ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Building Sample App..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Adding components, applying layout, styling theme...")) : demoApp.components.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Sample App Preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: "repeat(".concat(((_demoApp$layout = demoApp.layout) === null || _demoApp$layout === void 0 ? void 0 : _demoApp$layout.columns) || 2, ", 1fr)"),
          gap: ((_demoApp$layout2 = demoApp.layout) === null || _demoApp$layout2 === void 0 ? void 0 : _demoApp$layout2.gap) || '16px',
          marginTop: '16px'
        }
      }, demoApp.components.map(function (component) {
        var _demoApp$theme;
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
          key: component.id,
          style: {
            padding: '12px',
            border: '1px solid #d9d9d9',
            borderRadius: ((_demoApp$theme = demoApp.theme) === null || _demoApp$theme === void 0 ? void 0 : _demoApp$theme.borderRadius) || '4px',
            background: '#fff'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
          strong: true
        }, component.type, ": "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, null, component.props.text || component.props.title || component.props.placeholder));
      }))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4,
        type: "secondary"
      }, "Ready to Build"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Click \"Build Sample App\" to see all features in action")))))))
    }, {
      key: 'components',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ComponentBuilder, null)
    }, {
      key: 'layouts',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(LayoutDesigner, null)
    }, {
      key: 'themes',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ThemeManager, null)
    }, {
      key: 'websocket',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ApiOutlined */ .bfv, null), "WebSocket Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(WebSocketManager, null)
    }, {
      key: 'tutorial',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .QuestionCircleOutlined */ .faO, null), "Tutorial Assistant"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(IntegratedTutorialAssistant, {
        enableAutoStart: false,
        showContextualHelp: true,
        onTutorialComplete: function onTutorialComplete(tutorialId) {
          console.log('Tutorial completed:', tutorialId);
        },
        onTutorialSkip: function onTutorialSkip(tutorialId) {
          console.log('Tutorial skipped:', tutorialId);
        },
        features: ['components', 'layouts', 'themes', 'websocket', 'testing', 'data', 'performance', 'export']
      })
    }, {
      key: 'testing',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null), "Testing Tools"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestingTools, {
        components: projectData.components || [],
        onTestComplete: function onTestComplete(testType, results) {
          console.log('Test completed:', testType, results);
        },
        onTestStart: function onTestStart(testType) {
          console.log('Test started:', testType);
        },
        enabledTests: ['component', 'accessibility', 'performance', 'responsive'],
        autoRun: false,
        showMetrics: true,
        compact: false
      })
    }, {
      key: 'data',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DatabaseOutlined */ .ose, null), "Data Management"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(DataManagementDemo, null)
    }, {
      key: 'performance',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DashboardOutlined */ .zpd, null), "Performance Monitor"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(EnhancedPerformanceMonitor, {
        enabled: true,
        initiallyMinimized: false,
        refreshInterval: 5000,
        wsUrl: "ws://localhost:8000/ws",
        showNetworkStatus: true,
        showMemoryUsage: true,
        showRenderCounts: true,
        showWebSocketStatus: true
      })
    }, {
      key: 'export',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExportOutlined */ .PZg, null), "Enhanced Export"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(EnhancedCodeExporter, {
        components: projectData.components || [],
        layouts: projectData.layout ? [projectData.layout] : [],
        theme: projectData.theme || {},
        onExport: function onExport(exportData) {
          console.log('Code exported:', exportData);
        },
        onPreview: function onPreview(previewData) {
          console.log('Code preview:', previewData);
        },
        compact: false
      })
    }];
  }, [projectData, demoApp, createSampleApp]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(HeaderBar, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 3,
    style: {
      margin: 0,
      color: '#1890ff'
    }
  }, "App Builder Enhanced"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    type: "secondary"
  }, "Integrated Development Environment")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SaveOutlined */ .ylI, null)
  }, "Save Project"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ShareAltOutlined */ .f5H, null)
  }, "Share"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null)
  }, "Preview"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ContentArea, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU, {
    activeKey: activeFeature,
    onChange: setActiveFeature,
    type: "card",
    size: "large",
    items: tabItems
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppBuilderIntegrated);

/***/ })

}]);