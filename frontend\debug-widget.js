/**
 * Debug script to check why diagnostic widget isn't appearing
 */

const puppeteer = require('puppeteer');

async function debugWidget() {
  console.log('🔍 Debugging Diagnostic Widget...');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture ALL console messages
    page.on('console', msg => {
      console.log(`[BROWSER] ${msg.text()}`);
    });

    // Navigate to main app
    console.log('📱 Loading main application...');
    await page.goto('http://localhost:3000', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait longer for widget to appear
    await page.waitForTimeout(5000);

    // Check environment and widget status
    const status = await page.evaluate(() => {
      return {
        hostname: window.location.hostname,
        port: window.location.port,
        widgetExists: !!document.getElementById('react-diagnostic-widget'),
        bodyChildren: document.body ? Array.from(document.body.children).map(el => el.id || el.tagName) : [],
        reactLoaded: typeof window.React !== 'undefined',
        appLoaded: window.__APP_LOADED__
      };
    });

    console.log('\nStatus Check:');
    console.log('  Hostname:', status.hostname);
    console.log('  Port:', status.port);
    console.log('  Widget Exists:', status.widgetExists);
    console.log('  React Loaded:', status.reactLoaded);
    console.log('  App Loaded:', status.appLoaded);
    console.log('  Body Children:', status.bodyChildren);

    // Try to manually add widget for testing
    console.log('\n🔧 Manually adding widget for testing...');
    await page.evaluate(() => {
      if (!document.getElementById('manual-test-widget')) {
        const testWidget = document.createElement('div');
        testWidget.id = 'manual-test-widget';
        testWidget.style.cssText = `
          position: fixed;
          bottom: 60px;
          right: 20px;
          background: orange;
          color: white;
          padding: 10px;
          z-index: 10000;
          border-radius: 6px;
          cursor: pointer;
        `;
        testWidget.textContent = '🧪 MANUAL TEST';
        document.body.appendChild(testWidget);
        console.log('Manual test widget added');
      }
    });

    await page.waitForTimeout(2000);

    const finalStatus = await page.evaluate(() => {
      return {
        diagnosticWidget: !!document.getElementById('react-diagnostic-widget'),
        manualWidget: !!document.getElementById('manual-test-widget')
      };
    });

    console.log('\nFinal Status:');
    console.log('  Diagnostic Widget:', finalStatus.diagnosticWidget);
    console.log('  Manual Widget:', finalStatus.manualWidget);

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

debugWidget().catch(console.error);
