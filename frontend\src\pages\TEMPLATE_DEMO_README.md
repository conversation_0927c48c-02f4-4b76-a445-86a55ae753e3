# Template System Demonstration

This directory contains comprehensive demonstration interfaces for the hierarchical template system in the App Builder Enhanced application.

## Overview

The template system demonstration showcases:

- **LayoutTemplate and AppTemplate models** with their hierarchical relationships
- **Template metadata** including name, description, category, and component storage
- **Interactive elements** for testing template loading, selection, and application
- **Template hierarchy visualization** and relationships between different template types
- **Integration examples** with Component Builder, Layout Designer, and Theme Manager
- **Hello World starter template** and other default templates
- **Template JSON structure** and component storage format
- **Import/export capabilities** for template management
- **API documentation** with complete endpoint reference
- **Accessibility and responsive design** using Ant Design components

## Available Demonstrations

### 1. React Component Demo (`TemplateSystemDemo.js`)

A full-featured React component integrated into the App Builder application.

**Access:** Navigate to `/template-system-demo` in the running React application.

**Features:**
- Real-time template loading from backend APIs
- Interactive template gallery with search and filtering
- Hierarchical template visualization
- Live template testing and performance monitoring
- Complete API integration examples
- Responsive design with Ant Design components

**Usage:**
```bash
# Start the React application
cd frontend
npm start

# Navigate to http://localhost:3000/template-system-demo
```

### 2. Standalone HTML Demo (`template-system-demo.html`)

A self-contained HTML demonstration that works without the React application.

**Access:** Open `frontend/public/template-system-demo.html` directly in a web browser.

**Features:**
- Complete template system overview
- Sample template data for demonstration
- Interactive testing interface
- Template export functionality
- API documentation and examples
- Mobile-responsive design

**Usage:**
```bash
# Option 1: Open directly in browser
open frontend/public/template-system-demo.html

# Option 2: Serve via local server
cd frontend/public
python -m http.server 8000
# Navigate to http://localhost:8000/template-system-demo.html
```

## Template System Features Demonstrated

### 1. System Overview
- Template system architecture explanation
- Key features and capabilities
- Hello World template showcase
- Integration benefits

### 2. Template Gallery
- Layout templates (grid, flex, sidebar layouts)
- App templates (business, e-commerce, portfolio apps)
- Component templates (buttons, forms, charts)
- Template metadata and preview modes

### 3. Hierarchy View
- Template relationship visualization
- Hierarchical tree structure
- Template dependencies table
- Interactive template selection

### 4. Interactive Testing
- Template loading tests
- Search functionality tests
- Import/export testing
- Performance monitoring
- Real-time test results

### 5. Integration Demo
- Component Builder integration
- Layout Designer compatibility
- Theme Manager support
- Collaboration features
- Template application workflow

### 6. API Documentation
- Complete REST API endpoint reference
- Request/response examples
- GraphQL schema documentation
- Integration code examples (React hooks, Python client)

## Template Data Structure

### LayoutTemplate
```json
{
  "id": 1,
  "name": "Grid Layout Template",
  "description": "Responsive grid layout with header and sidebar",
  "layout_type": "grid",
  "components": {
    "structure": "grid",
    "areas": ["header", "sidebar", "main", "footer"],
    "columns": "200px 1fr",
    "rows": "auto 1fr auto"
  },
  "default_props": {
    "gap": "16px",
    "padding": "24px",
    "responsive": true
  },
  "is_public": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### AppTemplate
```json
{
  "id": 1,
  "name": "Hello World Starter",
  "description": "Simple starter template with basic components",
  "app_category": "other",
  "components": {
    "pages": [{
      "name": "home",
      "title": "Home",
      "components": [{
        "id": "header-1",
        "type": "header",
        "props": {
          "title": "Hello World!",
          "backgroundColor": "#1890ff"
        }
      }]
    }]
  },
  "required_components": ["header", "container"],
  "preview_image": "/images/hello-world-preview.png",
  "is_public": true
}
```

## API Endpoints Demonstrated

- `GET /api/layout-templates/` - List layout templates
- `POST /api/layout-templates/` - Create layout template
- `GET /api/app-templates/` - List app templates
- `POST /api/app-templates/` - Create app template
- `GET /api/component-templates/` - List component templates
- `GET /api/template-search/` - Search across all template types
- `GET /api/featured-templates/` - Get featured templates
- `GET /api/template-categories/` - Get template categories
- `POST /api/layout-templates/import_template/` - Import template
- `GET /api/layout-templates/{id}/export_template/` - Export template

## Integration Examples

### React Hook Usage
```javascript
import { useTemplates } from './hooks/useTemplates';

const MyComponent = () => {
  const { templates, loading, loadTemplates, createTemplate } = useTemplates();
  
  // Use templates in your component
  return (
    <div>
      {templates.map(template => (
        <TemplateCard key={template.id} template={template} />
      ))}
    </div>
  );
};
```

### API Integration
```javascript
// Load templates
const response = await axios.get('/api/layout-templates/');
const templates = response.data.results;

// Create template
const newTemplate = await axios.post('/api/layout-templates/', {
  name: 'My Template',
  description: 'Custom template',
  layout_type: 'grid',
  components: { /* template structure */ },
  is_public: false
});
```

## Development Notes

- The React component requires the backend API to be running for full functionality
- The HTML demo uses sample data and simulates API interactions
- Both demonstrations are fully responsive and accessible
- Error handling and loading states are implemented throughout
- Template export functionality generates downloadable JSON files

## Testing

Both demonstrations include comprehensive testing interfaces:

- Template loading and validation
- Search and filtering functionality
- Import/export operations
- API endpoint testing
- Performance monitoring
- Error handling verification

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Progressive enhancement for older browsers
- Accessibility compliance (WCAG AA)

## Future Enhancements

- Real-time collaboration demonstration
- AI-assisted template suggestions
- Advanced template versioning
- Template marketplace integration
- Enhanced visual preview capabilities
