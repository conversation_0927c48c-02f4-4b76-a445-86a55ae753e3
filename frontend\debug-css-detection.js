/**
 * Debug script to specifically check CSS detection issue
 */

const puppeteer = require('puppeteer');

async function debugCSSDetection() {
  console.log('🔍 Debugging CSS Detection...');

  let browser;
  try {
    browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Capture ALL console messages
    page.on('console', msg => {
      console.log(`[BROWSER] ${msg.text()}`);
    });

    // Navigate to diagnostics page
    console.log('📱 Loading comprehensive diagnostics page...');
    await page.goto('http://localhost:3000/comprehensive-react-diagnostics.html', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Wait for diagnostics to complete
    await page.waitForTimeout(10000);

    // Check CSS status specifically
    const cssDebugInfo = await page.evaluate(() => {
      const cssContainer = document.getElementById('css-status');
      const logContainer = document.getElementById('diagnostic-log');
      
      // Try to access main app context manually
      let mainAppContext = null;
      let contextType = 'none';
      
      if (window.opener && typeof window.opener.React !== 'undefined') {
        mainAppContext = window.opener;
        contextType = 'opener';
      } else if (window.parent && window.parent !== window && typeof window.parent.React !== 'undefined') {
        mainAppContext = window.parent;
        contextType = 'parent';
      } else if (window.__MAIN_APP_DOCUMENT__) {
        mainAppContext = { document: window.__MAIN_APP_DOCUMENT__ };
        contextType = 'stored';
      }
      
      let cssTestResults = {};
      
      // Test different CSS access methods
      try {
        // Method 1: Current document
        const currentCSS = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
        cssTestResults.currentDocument = currentCSS.length;
        
        // Method 2: Main app context
        if (mainAppContext && mainAppContext.document) {
          const mainAppCSS = Array.from(mainAppContext.document.querySelectorAll('link[rel="stylesheet"]'));
          cssTestResults.mainAppDocument = mainAppCSS.length;
        }
        
        // Method 3: Stored document
        if (window.__MAIN_APP_DOCUMENT__) {
          const storedCSS = Array.from(window.__MAIN_APP_DOCUMENT__.querySelectorAll('link[rel="stylesheet"]'));
          cssTestResults.storedDocument = storedCSS.length;
        }
        
        // Method 4: Direct opener access
        if (window.opener && window.opener.document) {
          const openerCSS = Array.from(window.opener.document.querySelectorAll('link[rel="stylesheet"]'));
          cssTestResults.openerDocument = openerCSS.length;
        }
        
      } catch (error) {
        cssTestResults.error = error.message;
      }
      
      return {
        cssStatusHTML: cssContainer ? cssContainer.innerHTML : 'No CSS status container',
        logText: logContainer ? logContainer.textContent : 'No log',
        contextType: contextType,
        hasMainAppDocument: !!window.__MAIN_APP_DOCUMENT__,
        cssTestResults: cssTestResults,
        windowKeys: Object.keys(window).filter(k => k.includes('MAIN') || k.includes('APP'))
      };
    });

    console.log('\n🔍 CSS Debug Information:');
    console.log('  Context Type:', cssDebugInfo.contextType);
    console.log('  Has Main App Document:', cssDebugInfo.hasMainAppDocument);
    console.log('  Window Keys:', cssDebugInfo.windowKeys);
    console.log('  CSS Test Results:', cssDebugInfo.cssTestResults);
    
    console.log('\n📋 CSS Status HTML:');
    console.log(cssDebugInfo.cssStatusHTML);
    
    // Look for CSS-related log messages
    const cssLogs = cssDebugInfo.logText.split('\n').filter(line => 
      line.includes('CSS') || line.includes('css') || line.includes('link')
    );
    
    if (cssLogs.length > 0) {
      console.log('\n📝 CSS-related Log Messages:');
      cssLogs.forEach(log => console.log(`   ${log}`));
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

debugCSSDetection().catch(console.error);
