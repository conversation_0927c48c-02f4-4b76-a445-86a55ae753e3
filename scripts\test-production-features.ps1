# PowerShell script to test production features and identify issues
# This script performs comprehensive testing of the production environment

param(
    [switch]$Verbose,
    [switch]$SkipBrowser,
    [int]$Timeout = 30
)

$ErrorActionPreference = "Continue"

function Write-Status {
    param($Message, $Color = "Yellow")
    Write-Host "🔧 $Message" -ForegroundColor $Color
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param($Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Cyan
}

function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Name,
        [int]$ExpectedStatus = 200,
        [string]$ExpectedContent = $null
    )
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $Timeout -UseBasicParsing
        
        if ($response.StatusCode -eq $ExpectedStatus) {
            if ($ExpectedContent -and $response.Content -notmatch $ExpectedContent) {
                Write-Error "$Name: Expected content not found"
                return $false
            }
            Write-Success "$Name: OK (Status: $($response.StatusCode))"
            return $true
        } else {
            Write-Error "$Name: Unexpected status code $($response.StatusCode)"
            return $false
        }
    } catch {
        Write-Error "$Name: $($_.Exception.Message)"
        return $false
    }
}

function Test-WebSocket {
    param([string]$Url, [string]$Name)
    
    Write-Status "Testing $Name WebSocket connection..."
    
    # Create a simple WebSocket test using Node.js if available
    $testScript = @"
const WebSocket = require('ws');
const ws = new WebSocket('$Url');

ws.on('open', function open() {
    console.log('WebSocket connected');
    ws.close();
    process.exit(0);
});

ws.on('error', function error(err) {
    console.error('WebSocket error:', err.message);
    process.exit(1);
});

setTimeout(() => {
    console.error('WebSocket connection timeout');
    process.exit(1);
}, 5000);
"@
    
    try {
        $testScript | Out-File -FilePath "temp_ws_test.js" -Encoding UTF8
        $result = node temp_ws_test.js 2>&1
        Remove-Item "temp_ws_test.js" -ErrorAction SilentlyContinue
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$Name WebSocket: Connected successfully"
            return $true
        } else {
            Write-Error "$Name WebSocket: Connection failed - $result"
            return $false
        }
    } catch {
        Write-Warning "$Name WebSocket: Could not test (Node.js not available)"
        Remove-Item "temp_ws_test.js" -ErrorAction SilentlyContinue
        return $null
    }
}

function Test-StaticAssets {
    Write-Status "Testing static asset delivery..."
    
    $staticTests = @{
        "CSS Assets" = "http://localhost/static/admin/css/base.css"
        "JS Assets" = "http://localhost/static/admin/js/core.js"
    }
    
    $passed = 0
    foreach ($test in $staticTests.GetEnumerator()) {
        if (Test-Endpoint $test.Value $test.Key) {
            $passed++
        }
    }
    
    return $passed -eq $staticTests.Count
}

function Test-APIEndpoints {
    Write-Status "Testing API endpoints..."
    
    $apiTests = @{
        "API Root" = "http://localhost/api/"
        "Health Check" = "http://localhost/health"
    }
    
    $passed = 0
    foreach ($test in $apiTests.GetEnumerator()) {
        if (Test-Endpoint $test.Value $test.Key) {
            $passed++
        }
    }
    
    return $passed -eq $apiTests.Count
}

function Test-SecurityHeaders {
    Write-Status "Testing security headers..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec $Timeout -UseBasicParsing
        
        $securityHeaders = @{
            "X-Frame-Options" = "DENY"
            "X-Content-Type-Options" = "nosniff"
            "X-XSS-Protection" = "1; mode=block"
        }
        
        $passed = 0
        foreach ($header in $securityHeaders.GetEnumerator()) {
            if ($response.Headers[$header.Key] -eq $header.Value) {
                Write-Success "Security header $($header.Key): Present"
                $passed++
            } else {
                Write-Warning "Security header $($header.Key): Missing or incorrect"
            }
        }
        
        return $passed -eq $securityHeaders.Count
    } catch {
        Write-Error "Security headers test failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-Compression {
    Write-Status "Testing gzip compression..."
    
    try {
        $headers = @{
            "Accept-Encoding" = "gzip, deflate"
        }
        
        $response = Invoke-WebRequest -Uri "http://localhost" -Headers $headers -TimeoutSec $Timeout -UseBasicParsing
        
        if ($response.Headers["Content-Encoding"] -eq "gzip") {
            Write-Success "Gzip compression: Enabled"
            return $true
        } else {
            Write-Warning "Gzip compression: Not enabled"
            return $false
        }
    } catch {
        Write-Error "Compression test failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-DatabaseOperations {
    Write-Status "Testing database operations..."
    
    try {
        # Test database migration status
        $migrationResult = docker-compose -f docker-compose.prod.yml exec -T backend python manage.py showmigrations --plan
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Database migrations: OK"
            
            # Test database query
            $queryResult = docker-compose -f docker-compose.prod.yml exec -T backend python manage.py shell -c "from django.contrib.auth.models import User; print(f'Users: {User.objects.count()}')"
            
            if ($LASTEXITCODE -eq 0) {
                Write-Success "Database queries: OK"
                return $true
            } else {
                Write-Error "Database queries: Failed"
                return $false
            }
        } else {
            Write-Error "Database migrations: Failed"
            return $false
        }
    } catch {
        Write-Error "Database operations test failed: $($_.Exception.Message)"
        return $false
    }
}

function Test-CacheOperations {
    Write-Status "Testing cache operations..."
    
    try {
        # Test Redis connection and operations
        $cacheTest = docker-compose -f docker-compose.prod.yml exec -T redis redis-cli SET test_key "test_value"
        
        if ($LASTEXITCODE -eq 0) {
            $getTest = docker-compose -f docker-compose.prod.yml exec -T redis redis-cli GET test_key
            
            if ($getTest -match "test_value") {
                Write-Success "Cache operations: OK"
                
                # Clean up test key
                docker-compose -f docker-compose.prod.yml exec -T redis redis-cli DEL test_key | Out-Null
                return $true
            } else {
                Write-Error "Cache operations: Read failed"
                return $false
            }
        } else {
            Write-Error "Cache operations: Write failed"
            return $false
        }
    } catch {
        Write-Error "Cache operations test failed: $($_.Exception.Message)"
        return $false
    }
}

function Generate-TestReport {
    param($Results)
    
    Write-Status "=== PRODUCTION TEST REPORT ===" "Magenta"
    
    $totalTests = $Results.Count
    $passedTests = ($Results.Values | Where-Object { $_ -eq $true }).Count
    $failedTests = ($Results.Values | Where-Object { $_ -eq $false }).Count
    $skippedTests = ($Results.Values | Where-Object { $_ -eq $null }).Count
    
    Write-Info "Total Tests: $totalTests"
    Write-Success "Passed: $passedTests"
    Write-Error "Failed: $failedTests"
    if ($skippedTests -gt 0) {
        Write-Warning "Skipped: $skippedTests"
    }
    
    Write-Host ""
    Write-Status "Test Results:" "Cyan"
    
    foreach ($test in $Results.GetEnumerator()) {
        $status = switch ($test.Value) {
            $true { "✅ PASS" }
            $false { "❌ FAIL" }
            $null { "⚠️  SKIP" }
        }
        Write-Host "  $($test.Key): $status"
    }
    
    if ($failedTests -eq 0) {
        Write-Success "`n🎉 All tests passed! Production environment is working correctly."
    } else {
        Write-Warning "`n⚠️  Some tests failed. Check the issues above and run troubleshooting."
        Write-Info "Run: .\scripts\troubleshoot-production.ps1 -FixIssues"
    }
}

# Main execution
Write-Status "App Builder 201 Production Feature Testing" "Magenta"
Write-Info "Testing production environment functionality..."

# Check if production environment is running
$containers = docker-compose -f docker-compose.prod.yml ps -q
if ($containers.Count -eq 0) {
    Write-Error "Production environment is not running."
    Write-Info "Start with: .\scripts\setup-production-local.ps1"
    exit 1
}

# Wait for services to be ready
Write-Status "Waiting for services to be ready..."
$ready = $false
$attempts = 0
$maxAttempts = 10

while (-not $ready -and $attempts -lt $maxAttempts) {
    $attempts++
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/health" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            $ready = $true
        }
    } catch {
        Start-Sleep -Seconds 3
    }
}

if (-not $ready) {
    Write-Error "Services are not ready. Check container status."
    exit 1
}

Write-Success "Services are ready. Starting tests..."

# Run all tests
$testResults = @{}

$testResults["Frontend Access"] = Test-Endpoint "http://localhost" "Frontend"
$testResults["API Endpoints"] = Test-APIEndpoints
$testResults["Static Assets"] = Test-StaticAssets
$testResults["Security Headers"] = Test-SecurityHeaders
$testResults["Gzip Compression"] = Test-Compression
$testResults["Database Operations"] = Test-DatabaseOperations
$testResults["Cache Operations"] = Test-CacheOperations
$testResults["WebSocket Connection"] = Test-WebSocket "ws://localhost/ws/" "App Builder"

# Generate report
Generate-TestReport $testResults

# Open browser if requested
if (-not $SkipBrowser -and $testResults["Frontend Access"]) {
    Write-Info "Opening application in browser..."
    Start-Process "http://localhost"
}

Write-Info "`nProduction testing completed."
