/**
 * Simple HTTP test to check if the React app is loading properly
 * This test uses basic HTTP requests instead of Puppeteer
 */

const http = require('http');

async function testReactApp() {
  console.log('🚀 Starting Simple React App Test...');

  try {
    // Test 1: Check if the server is responding
    console.log('📱 Testing server response...');
    const response = await makeRequest('http://localhost:3000');

    if (response.statusCode === 200) {
      console.log('✅ Server is responding (200 OK)');
      console.log(`📊 Content length: ${response.data.length} bytes`);
    } else {
      console.log(`❌ Server error: ${response.statusCode}`);
      return false;
    }

    // Test 2: Check for React bundle scripts
    console.log('🔍 Checking for React bundle scripts...');
    const html = response.data;

    // Updated regex to handle various main bundle patterns
    const bundleMatches = html.match(/static\/js\/main\.[a-zA-Z0-9_]+\.[a-f0-9]+\.js/g);

    if (bundleMatches && bundleMatches.length > 0) {
      console.log(`✅ Found ${bundleMatches.length} main bundle(s):`);
      bundleMatches.forEach(match => console.log(`   - ${match}`));
    } else {
      console.log('❌ No main bundle scripts found');
      return false;
    }

    // Test 3: Check for root element
    console.log('🏗️ Checking for root element...');
    if (html.includes('<div id="root">')) {
      console.log('✅ Root element found in HTML');
    } else {
      console.log('❌ Root element not found in HTML');
      return false;
    }

    // Test 4: Check for React-related scripts
    console.log('⚛️ Checking for React-related content...');
    const scriptTags = html.match(/<script[^>]*src="[^"]*"[^>]*>/g) || [];
    const reactScripts = scriptTags.filter(tag =>
      tag.includes('react') ||
      tag.includes('main') ||
      tag.includes('vendor') ||
      tag.includes('chunk')
    );

    console.log(`📦 Found ${scriptTags.length} total script tags`);
    console.log(`⚛️ Found ${reactScripts.length} React-related scripts`);

    if (reactScripts.length > 0) {
      console.log('✅ React-related scripts found');
    } else {
      console.log('❌ No React-related scripts found');
    }

    // Test 5: Check for compilation errors in HTML
    console.log('🔍 Checking for compilation errors...');
    if (html.includes('Compilation failed') || html.includes('Module not found')) {
      console.log('❌ Compilation errors detected in HTML');
      return false;
    } else {
      console.log('✅ No compilation errors detected');
    }

    console.log('\n🎯 Test Summary:');
    console.log('================');
    console.log('✅ Server responding');
    console.log('✅ Bundle scripts found');
    console.log('✅ Root element present');
    console.log('✅ No compilation errors');

    console.log('\n💡 Next Steps:');
    console.log('- Open http://localhost:3000 in browser');
    console.log('- Check browser console for React globals');
    console.log('- Look for "React loaded successfully" messages');
    console.log('- Test React globals with: typeof window.React');

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const request = http.get(url, (response) => {
      let data = '';

      response.on('data', (chunk) => {
        data += chunk;
      });

      response.on('end', () => {
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data
        });
      });
    });

    request.on('error', (error) => {
      reject(error);
    });

    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

// Run the test if this script is executed directly
if (require.main === module) {
  testReactApp()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = testReactApp;
