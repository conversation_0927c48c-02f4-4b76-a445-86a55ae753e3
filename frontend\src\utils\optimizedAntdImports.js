/**
 * Optimized Ant Design Imports Utility
 * 
 * This utility provides optimized imports for Ant Design components
 * to enable better tree-shaking and reduce bundle sizes.
 */

// Core Components - Import from specific paths for better tree-shaking
import ButtonComponent from 'antd/es/button';
import CardComponent from 'antd/es/card';
import TypographyComponent from 'antd/es/typography';
import LayoutComponent from 'antd/es/layout';
import MenuComponent from 'antd/es/menu';
import { Row as RowComponent, Col as ColComponent } from 'antd/es/grid';
import SpaceComponent from 'antd/es/space';
import StatisticComponent from 'antd/es/statistic';
import ListComponent from 'antd/es/list';
import BadgeComponent from 'antd/es/badge';
import CheckboxComponent from 'antd/es/checkbox';
import RadioComponent from 'antd/es/radio';
import TableComponent from 'antd/es/table';
import PaginationComponent from 'antd/es/pagination';
import EmptyComponent from 'antd/es/empty';
import DrawerComponent from 'antd/es/drawer';
import DropdownComponent from 'antd/es/dropdown';
import AvatarComponent from 'antd/es/avatar';

export { default as Button } from 'antd/es/button';
export { default as Card } from 'antd/es/card';
export { default as Typography } from 'antd/es/typography';
export { default as Layout } from 'antd/es/layout';
export { default as Menu } from 'antd/es/menu';
export { Row, Col } from 'antd/es/grid';
export { default as Space } from 'antd/es/space';
export { default as Divider } from 'antd/es/divider';
export { default as Alert } from 'antd/es/alert';
export { default as Spin } from 'antd/es/spin';
export { default as Modal } from 'antd/es/modal';
export { default as Drawer } from 'antd/es/drawer';
export { default as Tooltip } from 'antd/es/tooltip';
export { default as Popover } from 'antd/es/popover';
export { default as Badge } from 'antd/es/badge';
export { default as Tag } from 'antd/es/tag';
export { default as Avatar } from 'antd/es/avatar';
export { default as Dropdown } from 'antd/es/dropdown';
export { default as Tabs } from 'antd/es/tabs';
export { default as Collapse } from 'antd/es/collapse';
export { default as List } from 'antd/es/list';
export { default as Table } from 'antd/es/table';
export { default as Pagination } from 'antd/es/pagination';
export { default as Breadcrumb } from 'antd/es/breadcrumb';
export { default as Steps } from 'antd/es/steps';
export { default as Progress } from 'antd/es/progress';
export { default as Statistic } from 'antd/es/statistic';
export { default as Empty } from 'antd/es/empty';
export { default as Result } from 'antd/es/result';
export { default as Skeleton } from 'antd/es/skeleton';
export { default as BackTop } from 'antd/es/back-top';
export { default as Affix } from 'antd/es/affix';
export { default as Anchor } from 'antd/es/anchor';

// Form Components
import FormComponent from 'antd/es/form';
import InputComponent from 'antd/es/input';
import SelectComponent from 'antd/es/select';

export { default as Form } from 'antd/es/form';
export { default as Input } from 'antd/es/input';
export { default as Select } from 'antd/es/select';
export { default as Checkbox } from 'antd/es/checkbox';
export { default as Radio } from 'antd/es/radio';
export { default as Switch } from 'antd/es/switch';
export { default as Slider } from 'antd/es/slider';
export { default as Rate } from 'antd/es/rate';
export { default as Upload } from 'antd/es/upload';
export { default as DatePicker } from 'antd/es/date-picker';
export { default as TimePicker } from 'antd/es/time-picker';
export { default as Transfer } from 'antd/es/transfer';
export { default as Cascader } from 'antd/es/cascader';
export { default as TreeSelect } from 'antd/es/tree-select';
export { default as AutoComplete } from 'antd/es/auto-complete';

// Data Display
export { default as Tree } from 'antd/es/tree';
export { default as Calendar } from 'antd/es/calendar';
export { default as Image } from 'antd/es/image';
export { default as Carousel } from 'antd/es/carousel';
export { default as Descriptions } from 'antd/es/descriptions';
export { default as Timeline } from 'antd/es/timeline';

// Feedback
export { default as message } from 'antd/es/message';
export { default as notification } from 'antd/es/notification';
export { default as Popconfirm } from 'antd/es/popconfirm';

// Navigation
export { default as FloatButton } from 'antd/es/float-button';

// Other
export { default as ConfigProvider } from 'antd/es/config-provider';

// Import CSS for commonly used components
// Note: Using the reset CSS file for basic styling
// Component-specific styles are handled by the components themselves
import 'antd/dist/reset.css';

/**
 * Utility function for component CSS (deprecated)
 * CSS is now imported globally via antd.css
 */
export const importComponentCSS = (componentName) => {
  // CSS is now imported globally, this function is kept for compatibility
  console.log(`CSS for ${componentName} is already included globally`);
};

/**
 * Common component combinations for convenience
 */
export const CommonComponents = {
  // Layout components
  Layout: {
    Layout: LayoutComponent,
    Header: LayoutComponent.Header,
    Content: LayoutComponent.Content,
    Footer: LayoutComponent.Footer,
    Sider: LayoutComponent.Sider
  },

  // Typography components
  Typography: {
    Typography: TypographyComponent,
    Title: TypographyComponent.Title,
    Text: TypographyComponent.Text,
    Paragraph: TypographyComponent.Paragraph,
    Link: TypographyComponent.Link
  },

  // Grid components
  Grid: {
    Row: RowComponent,
    Col: ColComponent
  },

  // Form components
  FormComponents: {
    Form: FormComponent,
    FormItem: FormComponent.Item,
    FormList: FormComponent.List,
    Input: InputComponent,
    TextArea: InputComponent.TextArea,
    Password: InputComponent.Password,
    Search: InputComponent.Search,
    Select: SelectComponent,
    Option: SelectComponent.Option,
    OptGroup: SelectComponent.OptGroup
  }
};

/**
 * Optimized imports for specific page types
 */
export const PageImports = {
  // For dashboard/home pages
  dashboard: () => {
    return {
      Card: CardComponent,
      Button: ButtonComponent,
      Typography: TypographyComponent,
      Row: RowComponent,
      Col: ColComponent,
      Statistic: StatisticComponent,
      List: ListComponent,
      Badge: BadgeComponent,
      Space: SpaceComponent
    };
  },

  // For form pages
  form: () => {
    return {
      Form: FormComponent,
      Input: InputComponent,
      Select: SelectComponent,
      Checkbox: CheckboxComponent,
      Radio: RadioComponent,
      Button: ButtonComponent,
      Card: CardComponent,
      Space: SpaceComponent
    };
  },

  // For data display pages
  dataDisplay: () => {
    return {
      Table: TableComponent,
      Pagination: PaginationComponent,
      Empty: EmptyComponent,
      Card: CardComponent,
      Button: ButtonComponent,
      Space: SpaceComponent
    };
  },

  // For layout pages
  layout: () => {
    return {
      Layout: LayoutComponent,
      Menu: MenuComponent,
      Drawer: DrawerComponent,
      Dropdown: DropdownComponent,
      Button: ButtonComponent,
      Avatar: AvatarComponent
    };
  }
};

/**
 * Bundle size optimization tips
 */
export const OptimizationTips = {
  // Use specific imports instead of full library
  GOOD: "import { Button } from 'antd/es/button';",
  BAD: "import { Button } from 'antd';",

  // Import CSS only for used components
  CSS_GOOD: "import 'antd/es/button/style/css';",
  CSS_BAD: "import 'antd/dist/antd.css';",

  // Use this utility for consistent imports
  UTILITY: "import { Button, Card } from '../utils/optimizedAntdImports';"
};

export default {
  CommonComponents,
  PageImports,
  OptimizationTips,
  importComponentCSS
};
