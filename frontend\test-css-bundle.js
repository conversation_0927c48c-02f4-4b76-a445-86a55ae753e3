const puppeteer = require('puppeteer');

async function testCSSBundle() {
  console.log('🎨 Starting CSS Bundle Test...\n');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Enable request interception to track CSS loading
    await page.setRequestInterception(true);
    
    const cssRequests = [];
    const failedRequests = [];
    
    page.on('request', (request) => {
      if (request.url().includes('.css')) {
        cssRequests.push(request.url());
      }
      request.continue();
    });
    
    page.on('requestfailed', (request) => {
      if (request.url().includes('.css')) {
        failedRequests.push({
          url: request.url(),
          error: request.failure().errorText
        });
      }
    });

    console.log('📱 Navigating to http://localhost:3000...');
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // Wait for React to load
    await page.waitForFunction(() => {
      return window.React && window.__APP_LOADED__;
    }, { timeout: 15000 });

    // Check CSS bundle loading
    console.log('🎨 CSS Bundle Analysis:');
    console.log('======================');
    
    if (cssRequests.length > 0) {
      console.log(`✅ CSS Requests Found: ${cssRequests.length}`);
      cssRequests.forEach((url, index) => {
        const filename = url.split('/').pop();
        console.log(`   ${index + 1}. ${filename}`);
      });
    } else {
      console.log('❌ No CSS requests detected');
    }

    if (failedRequests.length > 0) {
      console.log(`\n❌ Failed CSS Requests: ${failedRequests.length}`);
      failedRequests.forEach((req, index) => {
        console.log(`   ${index + 1}. ${req.url} - ${req.error}`);
      });
    } else {
      console.log('\n✅ All CSS requests successful');
    }

    // Check if CSS is actually applied
    const stylesApplied = await page.evaluate(() => {
      const body = document.body;
      const computedStyle = window.getComputedStyle(body);
      
      return {
        fontFamily: computedStyle.fontFamily,
        backgroundColor: computedStyle.backgroundColor,
        color: computedStyle.color,
        margin: computedStyle.margin,
        padding: computedStyle.padding
      };
    });

    console.log('\n🎨 Applied Styles Check:');
    console.log('========================');
    console.log(`Font Family: ${stylesApplied.fontFamily}`);
    console.log(`Background Color: ${stylesApplied.backgroundColor}`);
    console.log(`Text Color: ${stylesApplied.color}`);
    console.log(`Margin: ${stylesApplied.margin}`);
    console.log(`Padding: ${stylesApplied.padding}`);

    // Check for Ant Design styles
    const antdStylesPresent = await page.evaluate(() => {
      const antButton = document.querySelector('.ant-btn');
      const antLayout = document.querySelector('.ant-layout');
      const antMenu = document.querySelector('.ant-menu');
      
      return {
        hasAntButton: !!antButton,
        hasAntLayout: !!antLayout,
        hasAntMenu: !!antMenu,
        totalAntElements: document.querySelectorAll('[class*="ant-"]').length
      };
    });

    console.log('\n🐜 Ant Design Styles Check:');
    console.log('============================');
    console.log(`Ant Button Elements: ${antdStylesPresent.hasAntButton ? '✅' : '❌'}`);
    console.log(`Ant Layout Elements: ${antdStylesPresent.hasAntLayout ? '✅' : '❌'}`);
    console.log(`Ant Menu Elements: ${antdStylesPresent.hasAntMenu ? '✅' : '❌'}`);
    console.log(`Total Ant Elements: ${antdStylesPresent.totalAntElements}`);

    // Check for custom CSS variables
    const cssVariables = await page.evaluate(() => {
      const root = document.documentElement;
      const computedStyle = window.getComputedStyle(root);
      
      const variables = {};
      for (let i = 0; i < computedStyle.length; i++) {
        const property = computedStyle[i];
        if (property.startsWith('--')) {
          variables[property] = computedStyle.getPropertyValue(property);
        }
      }
      
      return variables;
    });

    console.log('\n🎨 CSS Variables Check:');
    console.log('=======================');
    const variableCount = Object.keys(cssVariables).length;
    if (variableCount > 0) {
      console.log(`✅ CSS Variables Found: ${variableCount}`);
      // Show first 5 variables as examples
      Object.entries(cssVariables).slice(0, 5).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
      if (variableCount > 5) {
        console.log(`   ... and ${variableCount - 5} more`);
      }
    } else {
      console.log('❌ No CSS variables found');
    }

    // Overall assessment
    console.log('\n🎯 Overall CSS Bundle Status:');
    console.log('==============================');
    
    const hasMainCSS = cssRequests.some(url => url.includes('main.') && url.includes('.css'));
    const hasAntdStyles = antdStylesPresent.totalAntElements > 0;
    const hasCustomStyles = variableCount > 0 || stylesApplied.fontFamily !== 'Times';
    const noFailures = failedRequests.length === 0;
    
    console.log(`CSS Bundle Generated: ${hasMainCSS ? '✅' : '❌'}`);
    console.log(`Ant Design Styles: ${hasAntdStyles ? '✅' : '❌'}`);
    console.log(`Custom Styles Applied: ${hasCustomStyles ? '✅' : '❌'}`);
    console.log(`No CSS Load Failures: ${noFailures ? '✅' : '❌'}`);
    
    const allPassed = hasMainCSS && hasAntdStyles && hasCustomStyles && noFailures;
    console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME ISSUES DETECTED'}`);

  } catch (error) {
    console.error('❌ CSS Bundle test failed:', error.message);
  } finally {
    await browser.close();
  }
}

testCSSBundle().catch(console.error);
